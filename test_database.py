#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار قاعدة البيانات
"""

import sys
import os

# إضافة مسار src إلى Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from database.database_manager import DatabaseManager

def test_database():
    """اختبار قاعدة البيانات"""
    try:
        print("جاري اختبار قاعدة البيانات...")
        
        # إنشاء مدير قاعدة البيانات
        db_manager = DatabaseManager("database/supply_management.db")
        
        print("✓ تم إنشاء قاعدة البيانات بنجاح")
        
        # اختبار الحصول على معلومات قاعدة البيانات
        info = db_manager.get_database_info()
        print(f"✓ حجم قاعدة البيانات: {info['size_bytes']} بايت")
        print(f"✓ عدد الجداول: {len(info['tables'])}")
        print(f"✓ إجمالي السجلات: {info['total_records']}")
        
        # عرض الجداول
        print("\nالجداول المتاحة:")
        for table_name, count in info['tables'].items():
            print(f"  - {table_name}: {count} سجل")
        
        # اختبار تسجيل الدخول
        print("\nاختبار تسجيل الدخول...")
        users = db_manager.get_table_data("users")
        if users:
            admin_user = users[0]
            print(f"✓ المستخدم الافتراضي: {admin_user['username']}")
            
            # اختبار كلمة المرور
            if db_manager.verify_password("admin123", admin_user['password_hash']):
                print("✓ كلمة المرور صحيحة")
            else:
                print("✗ كلمة المرور خاطئة")
        
        # اختبار الرتب
        ranks = db_manager.get_table_data("ranks")
        print(f"\n✓ تم تحميل {len(ranks)} رتبة")
        
        # اختبار الألوان
        colors = db_manager.get_table_data("colors")
        print(f"✓ تم تحميل {len(colors)} لون")
        
        print("\n" + "="*50)
        print("تم اختبار قاعدة البيانات بنجاح!")
        print("="*50)
        
        # إغلاق قاعدة البيانات
        db_manager.close()
        
    except Exception as e:
        print(f"خطأ في اختبار قاعدة البيانات: {e}")
        return False
    
    return True

if __name__ == "__main__":
    test_database()
