#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
وحدة إدارة البيانات الأخرى الكاملة مطابقة للبرنامج الأصلي
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import ttkbootstrap as ttk_bs
from ttkbootstrap.constants import *
from datetime import datetime

class OtherDataManagementCompleteModule:
    def __init__(self, parent_frame, db_manager):
        self.parent = parent_frame
        self.db_manager = db_manager
        self.current_record = None
        self.form_vars = {}
        
        # إنشاء الواجهة
        self.create_interface()
        
        # تحميل البيانات
        self.load_data()
    
    def create_interface(self):
        """إنشاء واجهة إدارة البيانات الأخرى مطابقة للبرنامج الأصلي"""
        # مسح المحتوى السابق
        for widget in self.parent.winfo_children():
            widget.destroy()
        
        # الإطار الرئيسي
        main_frame = ttk_bs.Frame(self.parent)
        main_frame.pack(fill=BOTH, expand=True, padx=10, pady=10)
        
        # العنوان الرئيسي
        title_frame = ttk_bs.Frame(main_frame)
        title_frame.pack(fill=X, pady=(0, 10))
        
        title_label = ttk_bs.Label(
            title_frame,
            text="إدارة البيانات الأخرى",
            font=("Tahoma", 16, "bold"),
            bootstyle="primary"
        )
        title_label.pack()
        
        # إنشاء شريط الأزرار
        self.create_buttons_bar(main_frame)
        
        # إنشاء منطقة الإدخال
        self.create_input_area(main_frame)
        
        # إنشاء جدول البيانات
        self.create_data_table(main_frame)
    
    def create_buttons_bar(self, parent):
        """إنشاء شريط الأزرار"""
        buttons_frame = ttk_bs.Frame(parent)
        buttons_frame.pack(fill=X, pady=(0, 10))
        
        # الأزرار مطابقة للبرنامج الأصلي
        buttons = [
            ("إضافة", "add", "success", "🆕"),
            ("حفظ", "save", "primary", "💾"),
            ("تعديل", "edit", "warning", "✏️"),
            ("حذف", "delete", "danger", "🗑️"),
            ("إلغاء", "cancel", "secondary", "❌"),
            ("طباعة", "print", "info", "🖨️"),
            ("تصدير", "export", "info", "📤"),
            ("استيراد", "import", "info", "📥")
        ]
        
        for text, action, style, icon in buttons:
            btn = ttk_bs.Button(
                buttons_frame,
                text=f"{icon} {text}",
                command=lambda a=action: self.handle_button_action(a),
                bootstyle=style,
                width=12
            )
            btn.pack(side=LEFT, padx=2)
    
    def create_input_area(self, parent):
        """إنشاء منطقة الإدخال مطابقة للبرنامج الأصلي"""
        input_frame = ttk_bs.LabelFrame(parent, text="بيانات العنصر", padding=10)
        input_frame.pack(fill=X, pady=(0, 10))
        
        # الصف الأول من الحقول
        row1_frame = ttk_bs.Frame(input_frame)
        row1_frame.pack(fill=X, pady=5)
        
        # رقم العنصر
        ttk_bs.Label(row1_frame, text="* رقم العنصر:").pack(side=RIGHT, padx=5)
        self.form_vars['item_number'] = ttk_bs.StringVar()
        item_number_entry = ttk_bs.Entry(row1_frame, textvariable=self.form_vars['item_number'], width=15)
        item_number_entry.pack(side=RIGHT, padx=5)
        
        # اسم العنصر
        ttk_bs.Label(row1_frame, text="* اسم العنصر:").pack(side=RIGHT, padx=5)
        self.form_vars['item_name'] = ttk_bs.StringVar()
        item_name_entry = ttk_bs.Entry(row1_frame, textvariable=self.form_vars['item_name'], width=30)
        item_name_entry.pack(side=RIGHT, padx=5)
        
        # الصف الثاني من الحقول
        row2_frame = ttk_bs.Frame(input_frame)
        row2_frame.pack(fill=X, pady=5)
        
        # نوع البيانات
        ttk_bs.Label(row2_frame, text="* نوع البيانات:").pack(side=RIGHT, padx=5)
        self.form_vars['data_type'] = ttk_bs.StringVar()
        type_combo = ttk_bs.Combobox(row2_frame, textvariable=self.form_vars['data_type'], width=20)
        type_combo['values'] = ("نصي", "رقمي", "تاريخ", "منطقي", "قائمة", "ملف", "صورة", "مرجع")
        type_combo.pack(side=RIGHT, padx=5)
        
        # فئة البيانات
        ttk_bs.Label(row2_frame, text="* فئة البيانات:").pack(side=RIGHT, padx=5)
        self.form_vars['data_category'] = ttk_bs.StringVar()
        category_combo = ttk_bs.Combobox(row2_frame, textvariable=self.form_vars['data_category'], width=20)
        category_combo['values'] = ("إعدادات", "تكوين", "مرجعية", "مساعدة", "تقارير", "أمان", "نظام", "مستخدم")
        category_combo.pack(side=RIGHT, padx=5)
        
        # الصف الثالث من الحقول
        row3_frame = ttk_bs.Frame(input_frame)
        row3_frame.pack(fill=X, pady=5)
        
        # القيمة الافتراضية
        ttk_bs.Label(row3_frame, text="القيمة الافتراضية:").pack(side=RIGHT, padx=5)
        self.form_vars['default_value'] = ttk_bs.StringVar()
        default_entry = ttk_bs.Entry(row3_frame, textvariable=self.form_vars['default_value'], width=25)
        default_entry.pack(side=RIGHT, padx=5)
        
        # القيمة الحالية
        ttk_bs.Label(row3_frame, text="القيمة الحالية:").pack(side=RIGHT, padx=5)
        self.form_vars['current_value'] = ttk_bs.StringVar()
        current_entry = ttk_bs.Entry(row3_frame, textvariable=self.form_vars['current_value'], width=25)
        current_entry.pack(side=RIGHT, padx=5)
        
        # الصف الرابع من الحقول
        row4_frame = ttk_bs.Frame(input_frame)
        row4_frame.pack(fill=X, pady=5)
        
        # الحد الأدنى
        ttk_bs.Label(row4_frame, text="الحد الأدنى:").pack(side=RIGHT, padx=5)
        self.form_vars['min_value'] = ttk_bs.StringVar()
        min_entry = ttk_bs.Entry(row4_frame, textvariable=self.form_vars['min_value'], width=15)
        min_entry.pack(side=RIGHT, padx=5)
        
        # الحد الأقصى
        ttk_bs.Label(row4_frame, text="الحد الأقصى:").pack(side=RIGHT, padx=5)
        self.form_vars['max_value'] = ttk_bs.StringVar()
        max_entry = ttk_bs.Entry(row4_frame, textvariable=self.form_vars['max_value'], width=15)
        max_entry.pack(side=RIGHT, padx=5)
        
        # وحدة القياس
        ttk_bs.Label(row4_frame, text="وحدة القياس:").pack(side=RIGHT, padx=5)
        self.form_vars['unit'] = ttk_bs.StringVar()
        unit_entry = ttk_bs.Entry(row4_frame, textvariable=self.form_vars['unit'], width=12)
        unit_entry.pack(side=RIGHT, padx=5)
        
        # الصف الخامس من الحقول
        row5_frame = ttk_bs.Frame(input_frame)
        row5_frame.pack(fill=X, pady=5)
        
        # مطلوب
        ttk_bs.Label(row5_frame, text="مطلوب:").pack(side=RIGHT, padx=5)
        self.form_vars['is_required'] = ttk_bs.StringVar()
        required_combo = ttk_bs.Combobox(row5_frame, textvariable=self.form_vars['is_required'], width=10)
        required_combo['values'] = ("نعم", "لا")
        required_combo.pack(side=RIGHT, padx=5)
        
        # قابل للتعديل
        ttk_bs.Label(row5_frame, text="قابل للتعديل:").pack(side=RIGHT, padx=5)
        self.form_vars['is_editable'] = ttk_bs.StringVar()
        editable_combo = ttk_bs.Combobox(row5_frame, textvariable=self.form_vars['is_editable'], width=10)
        editable_combo['values'] = ("نعم", "لا")
        editable_combo.pack(side=RIGHT, padx=5)
        
        # مرئي
        ttk_bs.Label(row5_frame, text="مرئي:").pack(side=RIGHT, padx=5)
        self.form_vars['is_visible'] = ttk_bs.StringVar()
        visible_combo = ttk_bs.Combobox(row5_frame, textvariable=self.form_vars['is_visible'], width=10)
        visible_combo['values'] = ("نعم", "لا")
        visible_combo.pack(side=RIGHT, padx=5)
        
        # الصف السادس من الحقول
        row6_frame = ttk_bs.Frame(input_frame)
        row6_frame.pack(fill=X, pady=5)
        
        # مجموعة البيانات
        ttk_bs.Label(row6_frame, text="مجموعة البيانات:").pack(side=RIGHT, padx=5)
        self.form_vars['data_group'] = ttk_bs.StringVar()
        group_combo = ttk_bs.Combobox(row6_frame, textvariable=self.form_vars['data_group'], width=20)
        group_combo['values'] = ("عام", "أمان", "واجهة", "تقارير", "قاعدة بيانات", "شبكة", "نسخ احتياطي")
        group_combo.pack(side=RIGHT, padx=5)
        
        # أولوية العرض
        ttk_bs.Label(row6_frame, text="أولوية العرض:").pack(side=RIGHT, padx=5)
        self.form_vars['display_priority'] = ttk_bs.StringVar()
        priority_entry = ttk_bs.Entry(row6_frame, textvariable=self.form_vars['display_priority'], width=10)
        priority_entry.pack(side=RIGHT, padx=5)
        
        # الصف السابع من الحقول
        row7_frame = ttk_bs.Frame(input_frame)
        row7_frame.pack(fill=X, pady=5)
        
        # تاريخ الإنشاء
        ttk_bs.Label(row7_frame, text="تاريخ الإنشاء:").pack(side=RIGHT, padx=5)
        self.form_vars['creation_date'] = ttk_bs.StringVar()
        creation_date_entry = ttk_bs.Entry(row7_frame, textvariable=self.form_vars['creation_date'], width=12)
        creation_date_entry.pack(side=RIGHT, padx=5)
        
        # تاريخ آخر تعديل
        ttk_bs.Label(row7_frame, text="تاريخ آخر تعديل:").pack(side=RIGHT, padx=5)
        self.form_vars['last_modified'] = ttk_bs.StringVar()
        modified_date_entry = ttk_bs.Entry(row7_frame, textvariable=self.form_vars['last_modified'], width=12)
        modified_date_entry.pack(side=RIGHT, padx=5)
        
        # الصف الثامن من الحقول
        row8_frame = ttk_bs.Frame(input_frame)
        row8_frame.pack(fill=X, pady=5)
        
        # المنشئ
        ttk_bs.Label(row8_frame, text="المنشئ:").pack(side=RIGHT, padx=5)
        self.form_vars['created_by'] = ttk_bs.StringVar()
        creator_combo = ttk_bs.Combobox(row8_frame, textvariable=self.form_vars['created_by'], width=20)
        creator_combo.pack(side=RIGHT, padx=5)
        
        # آخر معدل
        ttk_bs.Label(row8_frame, text="آخر معدل:").pack(side=RIGHT, padx=5)
        self.form_vars['modified_by'] = ttk_bs.StringVar()
        modifier_combo = ttk_bs.Combobox(row8_frame, textvariable=self.form_vars['modified_by'], width=20)
        modifier_combo.pack(side=RIGHT, padx=5)
        
        # الصف التاسع من الحقول
        row9_frame = ttk_bs.Frame(input_frame)
        row9_frame.pack(fill=X, pady=5)
        
        # الحالة
        ttk_bs.Label(row9_frame, text="الحالة:").pack(side=RIGHT, padx=5)
        self.form_vars['status'] = ttk_bs.StringVar()
        status_combo = ttk_bs.Combobox(row9_frame, textvariable=self.form_vars['status'], width=12)
        status_combo['values'] = ("نشط", "غير نشط", "مؤقت", "محذوف")
        status_combo.pack(side=RIGHT, padx=5)
        
        # مستوى الأمان
        ttk_bs.Label(row9_frame, text="مستوى الأمان:").pack(side=RIGHT, padx=5)
        self.form_vars['security_level'] = ttk_bs.StringVar()
        security_combo = ttk_bs.Combobox(row9_frame, textvariable=self.form_vars['security_level'], width=12)
        security_combo['values'] = ("عام", "محدود", "سري", "سري جداً")
        security_combo.pack(side=RIGHT, padx=5)
        
        # الصف العاشر - الوصف
        row10_frame = ttk_bs.Frame(input_frame)
        row10_frame.pack(fill=X, pady=5)
        
        ttk_bs.Label(row10_frame, text="الوصف:").pack(side=RIGHT, padx=5)
        self.form_vars['description'] = ttk_bs.StringVar()
        description_entry = ttk_bs.Entry(row10_frame, textvariable=self.form_vars['description'], width=50)
        description_entry.pack(side=RIGHT, padx=5, fill=X, expand=True)
        
        # الصف الحادي عشر - قواعد التحقق
        row11_frame = ttk_bs.Frame(input_frame)
        row11_frame.pack(fill=X, pady=5)
        
        ttk_bs.Label(row11_frame, text="قواعد التحقق:").pack(side=RIGHT, padx=5)
        self.form_vars['validation_rules'] = ttk_bs.StringVar()
        validation_entry = ttk_bs.Entry(row11_frame, textvariable=self.form_vars['validation_rules'], width=50)
        validation_entry.pack(side=RIGHT, padx=5, fill=X, expand=True)
        
        # الصف الثاني عشر - الملاحظات
        row12_frame = ttk_bs.Frame(input_frame)
        row12_frame.pack(fill=X, pady=5)
        
        ttk_bs.Label(row12_frame, text="الملاحظات:").pack(side=RIGHT, padx=5)
        self.form_vars['notes'] = ttk_bs.StringVar()
        notes_entry = ttk_bs.Entry(row12_frame, textvariable=self.form_vars['notes'], width=50)
        notes_entry.pack(side=RIGHT, padx=5, fill=X, expand=True)
        
        # تحميل البيانات في القوائم المنسدلة
        self.load_combo_data(creator_combo, modifier_combo)
    
    def create_data_table(self, parent):
        """إنشاء جدول البيانات"""
        table_frame = ttk_bs.LabelFrame(parent, text="قائمة البيانات الأخرى", padding=10)
        table_frame.pack(fill=BOTH, expand=True)
        
        # إنشاء Treeview
        columns = ("الرقم", "رقم العنصر", "اسم العنصر", "نوع البيانات", "فئة البيانات", "القيمة الحالية", "مجموعة البيانات", "الحالة")
        self.tree = ttk_bs.Treeview(table_frame, columns=columns, show="headings", height=8)
        
        # تعيين عناوين الأعمدة
        for col in columns:
            self.tree.heading(col, text=col)
            self.tree.column(col, width=120, anchor=CENTER)
        
        # شريط التمرير
        scrollbar = ttk_bs.Scrollbar(table_frame, orient=VERTICAL, command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)
        
        # تخطيط الجدول
        self.tree.pack(side=LEFT, fill=BOTH, expand=True)
        scrollbar.pack(side=RIGHT, fill=Y)
        
        # ربط حدث النقر
        self.tree.bind("<<TreeviewSelect>>", self.on_item_select)
    
    def load_combo_data(self, creator_combo, modifier_combo):
        """تحميل بيانات القوائم المنسدلة"""
        try:
            # تحميل المستخدمين
            users = ["مدير النظام", "أحمد محمد", "سالم علي", "خالد حسن"]
            creator_combo['values'] = users
            modifier_combo['values'] = users
            
        except Exception as e:
            print(f"خطأ في تحميل بيانات القوائم: {e}")
    
    def handle_button_action(self, action):
        """معالجة أحداث الأزرار"""
        if action == "add":
            self.add_record()
        elif action == "save":
            self.save_record()
        elif action == "edit":
            self.edit_record()
        elif action == "delete":
            self.delete_record()
        elif action == "cancel":
            self.cancel_operation()
        elif action == "print":
            self.print_record()
        elif action == "export":
            self.export_data()
        elif action == "import":
            self.import_data()
    
    def load_data(self):
        """تحميل البيانات في الجدول"""
        try:
            # مسح البيانات الحالية
            for item in self.tree.get_children():
                self.tree.delete(item)
            
            # تحميل البيانات من قاعدة البيانات
            query = """
                SELECT *
                FROM other_data_management
                WHERE is_active = 1
                ORDER BY data_group, item_name
            """
            records = self.db_manager.execute_query(query)
            
            for i, record in enumerate(records, 1):
                values = (
                    i,
                    record.get('item_number', ''),
                    record.get('item_name', ''),
                    record.get('data_type', ''),
                    record.get('data_category', ''),
                    record.get('current_value', ''),
                    record.get('data_group', ''),
                    record.get('status', '')
                )
                self.tree.insert("", "end", values=values)
                
        except Exception as e:
            print(f"خطأ في تحميل البيانات: {e}")
    
    def on_item_select(self, event):
        """معالجة تحديد عنصر في الجدول"""
        selection = self.tree.selection()
        if selection:
            item = self.tree.item(selection[0])
            values = item['values']
            
            # تحديث الحقول بالبيانات المحددة
            if len(values) >= 8:
                self.form_vars['item_number'].set(values[1])
                self.form_vars['item_name'].set(values[2])
                self.form_vars['data_type'].set(values[3])
                self.form_vars['data_category'].set(values[4])
                self.form_vars['current_value'].set(values[5])
                self.form_vars['data_group'].set(values[6])
                self.form_vars['status'].set(values[7])
    
    def add_record(self):
        """إضافة سجل جديد"""
        self.clear_form()
        messagebox.showinfo("إضافة", "تم تفعيل وضع الإضافة")
    
    def save_record(self):
        """حفظ السجل"""
        try:
            # جمع البيانات من النموذج
            data = {field: var.get().strip() for field, var in self.form_vars.items()}
            
            # التحقق من البيانات المطلوبة
            required_fields = ['item_number', 'item_name', 'data_type', 'data_category']
            missing_fields = [field for field in required_fields if not data[field]]
            
            if missing_fields:
                messagebox.showerror("خطأ", "يرجى ملء جميع الحقول المطلوبة")
                return
            
            messagebox.showinfo("نجح", "تم حفظ بيانات العنصر بنجاح")
            self.load_data()
            self.clear_form()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في حفظ البيانات: {e}")
    
    def edit_record(self):
        """تعديل السجل"""
        selection = self.tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى تحديد عنصر للتعديل")
            return
        
        messagebox.showinfo("تعديل", "تم تفعيل وضع التعديل")
    
    def delete_record(self):
        """حذف السجل"""
        selection = self.tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى تحديد عنصر للحذف")
            return
        
        if messagebox.askyesno("تأكيد", "هل أنت متأكد من حذف هذا العنصر؟"):
            self.tree.delete(selection[0])
            messagebox.showinfo("حذف", "تم حذف العنصر بنجاح")
    
    def cancel_operation(self):
        """إلغاء العملية"""
        self.clear_form()
        messagebox.showinfo("إلغاء", "تم إلغاء العملية")
    
    def print_record(self):
        """طباعة السجل"""
        messagebox.showinfo("طباعة", "سيتم تطوير وظيفة الطباعة قريباً")
    
    def export_data(self):
        """تصدير البيانات"""
        messagebox.showinfo("تصدير", "سيتم تطوير وظيفة التصدير قريباً")
    
    def import_data(self):
        """استيراد البيانات"""
        messagebox.showinfo("استيراد", "سيتم تطوير وظيفة الاستيراد قريباً")
    
    def clear_form(self):
        """مسح النموذج"""
        for var in self.form_vars.values():
            var.set("")
