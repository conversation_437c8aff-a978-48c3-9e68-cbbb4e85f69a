#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار الواجهة النهائية مع القوائم المحدثة والتذييل
"""

import sys
import os

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_final_interface():
    """اختبار الواجهة النهائية"""
    print("=" * 70)
    print("اختبار الواجهة النهائية مع القوائم المحدثة والتذييل")
    print("=" * 70)
    
    try:
        # استيراد المكونات
        from src.ui.main_window import MainWindow
        from src.database.database_manager import DatabaseManager
        
        print("✓ تم استيراد جميع المكونات بنجاح")
        
        # تهيئة قاعدة البيانات
        db_manager = DatabaseManager()
        print("✓ تم تهيئة قاعدة البيانات")
        
        # بيانات مستخدم تجريبية
        user_data = {
            'id': 1,
            'username': 'admin',
            'full_name': 'مدير النظام',
            'role': 'مدير'
        }
        
        print("✓ تم إعداد بيانات المستخدم التجريبية")
        
        # إنشاء النافذة الرئيسية
        print("\n🚀 تشغيل النافذة الرئيسية مع التصميم النهائي...")
        print("\n📋 القوائم العلوية المحدثة:")
        print("   🗂️  قائمة ملف:")
        print("      • نسخة احتياطية")
        print("      • استعادة نسخة")
        print("      • خروج")
        print("\n   🏢 قائمة جهات الإضافة:")
        print("      • إدارة البيانات الأخرى")
        print("      • إدارة العربات")
        print("      • إدارة السلاح")
        print("\n   ⚙️  قائمة الإعدادات:")
        print("      • الوحدات")
        print("      • البيانات الشخصية")
        print("      • الرتب")
        print("      • المسميات الوظيفية")
        print("      • مسميات المعدات")
        print("      • الجدول التنظيمي")
        print("      • الألوان")
        print("      • جهة الإضافة")
        print("      • العناوين والمستودعات")
        print("      • صور وشعار التقارير")
        print("\n   📊 قائمة التقارير:")
        print("      • نظام التقارير")
        print("      • نظام التقارير العامة")
        print("      • البحث الشامل")
        print("\n   ❓ قائمة مساعدة:")
        print("      • دليل المستخدم")
        print("      • الأسئلة الشائعة")
        print("      • الدعم الفني")
        print("      • حول البرنامج")
        print("\n🦶 التذييل الجديد:")
        print("   📅 اليمين: التاريخ والوقت واليوم")
        print("   🏛️  الوسط: اسم البرنامج وحقوق الحفظ")
        print("   👤 اليسار: اسم المستخدم الحالي")
        
        # تشغيل النافذة
        main_window = MainWindow(user_data, db_manager)
        main_window.run()
        
        print("✓ تم تشغيل النظام بنجاح")
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الواجهة: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

if __name__ == "__main__":
    print("بدء اختبار الواجهة النهائية...")
    success = test_final_interface()
    
    if success:
        print("\n" + "=" * 70)
        print("✅ تم اختبار الواجهة النهائية بنجاح!")
        print("🎨 القوائم العلوية محدثة حسب المطلوب")
        print("🦶 التذييل مضاف مع التاريخ والوقت واسم المستخدم")
        print("📱 شريط أدوات محدث مع الأزرار المطلوبة")
        print("=" * 70)
    else:
        print("\n" + "=" * 70)
        print("❌ فشل في اختبار الواجهة النهائية")
        print("=" * 70)
