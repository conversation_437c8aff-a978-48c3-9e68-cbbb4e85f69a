#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
وحدة النسخ الاحتياطي الكاملة مطابقة للبرنامج الأصلي
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import ttkbootstrap as ttk_bs
from ttkbootstrap.constants import *
from datetime import datetime
import os
import shutil
import sqlite3
import zipfile

class BackupCompleteModule:
    def __init__(self, parent_frame, db_manager):
        self.parent = parent_frame
        self.db_manager = db_manager
        self.backup_path = ""
        self.restore_path = ""
        
        # إنشاء الواجهة
        self.create_interface()
        
        # تحميل البيانات
        self.load_backup_history()
    
    def create_interface(self):
        """إنشاء واجهة النسخ الاحتياطي مطابقة للبرنامج الأصلي"""
        # مسح المحتوى السابق
        for widget in self.parent.winfo_children():
            widget.destroy()
        
        # الإطار الرئيسي
        main_frame = ttk_bs.Frame(self.parent)
        main_frame.pack(fill=BOTH, expand=True, padx=10, pady=10)
        
        # العنوان الرئيسي
        title_frame = ttk_bs.Frame(main_frame)
        title_frame.pack(fill=X, pady=(0, 10))
        
        title_label = ttk_bs.Label(
            title_frame,
            text="إدارة النسخ الاحتياطي",
            font=("Tahoma", 16, "bold"),
            bootstyle="primary"
        )
        title_label.pack()
        
        # إنشاء التبويبات
        self.create_tabs(main_frame)
        
        # إنشاء قسم النسخ الاحتياطي
        self.create_backup_section(main_frame)
        
        # إنشاء قسم الاستعادة
        self.create_restore_section(main_frame)
        
        # إنشاء قسم تاريخ النسخ
        self.create_history_section(main_frame)
    
    def create_tabs(self, parent):
        """إنشاء التبويبات العلوية"""
        tabs_frame = ttk_bs.Frame(parent)
        tabs_frame.pack(fill=X, pady=(0, 10))
        
        # التبويبات مطابقة للبرنامج الأصلي
        tabs = [
            ("القوة العمومية", "force", "info"),
            ("المعدات", "equipment", "info"),
            ("الأصناف", "categories", "info"),
            ("الأسلحة", "weapons", "info"),
            ("المستخدمين", "users", "info"),
            ("النسخ الاحتياطي", "backup", "primary"),  # النشط
            ("أدوات الاستعلام", "queries", "info")
        ]
        
        for text, tab_id, style in tabs:
            btn = ttk_bs.Button(
                tabs_frame,
                text=text,
                command=lambda t=tab_id: self.switch_tab(t),
                bootstyle=style,
                width=15
            )
            btn.pack(side=LEFT, padx=1)
    
    def create_backup_section(self, parent):
        """إنشاء قسم النسخ الاحتياطي"""
        backup_frame = ttk_bs.LabelFrame(parent, text="إنشاء نسخة احتياطية", padding=15)
        backup_frame.pack(fill=X, pady=(0, 10))
        
        # الصف الأول - اختيار المسار
        path_frame = ttk_bs.Frame(backup_frame)
        path_frame.pack(fill=X, pady=5)
        
        ttk_bs.Label(path_frame, text="مسار حفظ النسخة الاحتياطية:", font=("Tahoma", 10, "bold")).pack(side=RIGHT, padx=5)
        
        self.backup_path_var = ttk_bs.StringVar()
        path_entry = ttk_bs.Entry(path_frame, textvariable=self.backup_path_var, width=50, state="readonly")
        path_entry.pack(side=RIGHT, padx=5, fill=X, expand=True)
        
        browse_btn = ttk_bs.Button(
            path_frame,
            text="📁 تصفح",
            command=self.browse_backup_path,
            bootstyle="info",
            width=10
        )
        browse_btn.pack(side=LEFT, padx=5)
        
        # الصف الثاني - خيارات النسخ
        options_frame = ttk_bs.Frame(backup_frame)
        options_frame.pack(fill=X, pady=10)
        
        # خيارات النسخ
        self.include_data_var = ttk_bs.BooleanVar(value=True)
        self.include_settings_var = ttk_bs.BooleanVar(value=True)
        self.include_reports_var = ttk_bs.BooleanVar(value=False)
        
        ttk_bs.Checkbutton(
            options_frame,
            text="تضمين البيانات",
            variable=self.include_data_var,
            bootstyle="primary"
        ).pack(side=RIGHT, padx=10)
        
        ttk_bs.Checkbutton(
            options_frame,
            text="تضمين الإعدادات",
            variable=self.include_settings_var,
            bootstyle="primary"
        ).pack(side=RIGHT, padx=10)
        
        ttk_bs.Checkbutton(
            options_frame,
            text="تضمين التقارير",
            variable=self.include_reports_var,
            bootstyle="primary"
        ).pack(side=RIGHT, padx=10)
        
        # الصف الثالث - أزرار النسخ
        buttons_frame = ttk_bs.Frame(backup_frame)
        buttons_frame.pack(fill=X, pady=10)
        
        # زر النسخ الاحتياطي
        backup_btn = ttk_bs.Button(
            buttons_frame,
            text="💾 إنشاء نسخة احتياطية",
            command=self.create_backup,
            bootstyle="success",
            width=20
        )
        backup_btn.pack(side=RIGHT, padx=5)
        
        # زر النسخ السريع
        quick_backup_btn = ttk_bs.Button(
            buttons_frame,
            text="⚡ نسخ سريع",
            command=self.quick_backup,
            bootstyle="warning",
            width=15
        )
        quick_backup_btn.pack(side=RIGHT, padx=5)
        
        # زر النسخ المجدول
        schedule_btn = ttk_bs.Button(
            buttons_frame,
            text="⏰ نسخ مجدول",
            command=self.schedule_backup,
            bootstyle="info",
            width=15
        )
        schedule_btn.pack(side=RIGHT, padx=5)
    
    def create_restore_section(self, parent):
        """إنشاء قسم الاستعادة"""
        restore_frame = ttk_bs.LabelFrame(parent, text="استعادة النسخة الاحتياطية", padding=15)
        restore_frame.pack(fill=X, pady=(0, 10))
        
        # الصف الأول - اختيار ملف الاستعادة
        restore_path_frame = ttk_bs.Frame(restore_frame)
        restore_path_frame.pack(fill=X, pady=5)
        
        ttk_bs.Label(restore_path_frame, text="ملف النسخة الاحتياطية:", font=("Tahoma", 10, "bold")).pack(side=RIGHT, padx=5)
        
        self.restore_path_var = ttk_bs.StringVar()
        restore_entry = ttk_bs.Entry(restore_path_frame, textvariable=self.restore_path_var, width=50, state="readonly")
        restore_entry.pack(side=RIGHT, padx=5, fill=X, expand=True)
        
        browse_restore_btn = ttk_bs.Button(
            restore_path_frame,
            text="📁 تصفح",
            command=self.browse_restore_file,
            bootstyle="info",
            width=10
        )
        browse_restore_btn.pack(side=LEFT, padx=5)
        
        # الصف الثاني - خيارات الاستعادة
        restore_options_frame = ttk_bs.Frame(restore_frame)
        restore_options_frame.pack(fill=X, pady=10)
        
        # خيارات الاستعادة
        self.restore_data_var = ttk_bs.BooleanVar(value=True)
        self.restore_settings_var = ttk_bs.BooleanVar(value=True)
        self.create_backup_before_restore_var = ttk_bs.BooleanVar(value=True)
        
        ttk_bs.Checkbutton(
            restore_options_frame,
            text="استعادة البيانات",
            variable=self.restore_data_var,
            bootstyle="primary"
        ).pack(side=RIGHT, padx=10)
        
        ttk_bs.Checkbutton(
            restore_options_frame,
            text="استعادة الإعدادات",
            variable=self.restore_settings_var,
            bootstyle="primary"
        ).pack(side=RIGHT, padx=10)
        
        ttk_bs.Checkbutton(
            restore_options_frame,
            text="إنشاء نسخة احتياطية قبل الاستعادة",
            variable=self.create_backup_before_restore_var,
            bootstyle="warning"
        ).pack(side=RIGHT, padx=10)
        
        # الصف الثالث - أزرار الاستعادة
        restore_buttons_frame = ttk_bs.Frame(restore_frame)
        restore_buttons_frame.pack(fill=X, pady=10)
        
        # زر الاستعادة
        restore_btn = ttk_bs.Button(
            restore_buttons_frame,
            text="🔄 استعادة النسخة",
            command=self.restore_backup,
            bootstyle="danger",
            width=20
        )
        restore_btn.pack(side=RIGHT, padx=5)
        
        # زر معاينة النسخة
        preview_btn = ttk_bs.Button(
            restore_buttons_frame,
            text="👁️ معاينة النسخة",
            command=self.preview_backup,
            bootstyle="info",
            width=15
        )
        preview_btn.pack(side=RIGHT, padx=5)
    
    def create_history_section(self, parent):
        """إنشاء قسم تاريخ النسخ"""
        history_frame = ttk_bs.LabelFrame(parent, text="تاريخ النسخ الاحتياطي", padding=10)
        history_frame.pack(fill=BOTH, expand=True)
        
        # إنشاء Treeview
        columns = ("التاريخ", "الوقت", "نوع العملية", "المسار", "الحجم", "الحالة")
        self.history_tree = ttk_bs.Treeview(history_frame, columns=columns, show="headings", height=8)
        
        # تعيين عناوين الأعمدة
        for col in columns:
            self.history_tree.heading(col, text=col)
            self.history_tree.column(col, width=120, anchor=CENTER)
        
        # شريط التمرير
        history_scrollbar = ttk_bs.Scrollbar(history_frame, orient=VERTICAL, command=self.history_tree.yview)
        self.history_tree.configure(yscrollcommand=history_scrollbar.set)
        
        # تخطيط الجدول
        self.history_tree.pack(side=LEFT, fill=BOTH, expand=True)
        history_scrollbar.pack(side=RIGHT, fill=Y)
        
        # أزرار إدارة التاريخ
        history_buttons_frame = ttk_bs.Frame(history_frame)
        history_buttons_frame.pack(fill=X, pady=(10, 0))
        
        ttk_bs.Button(
            history_buttons_frame,
            text="🗑️ مسح التاريخ",
            command=self.clear_history,
            bootstyle="danger",
            width=15
        ).pack(side=LEFT, padx=5)
        
        ttk_bs.Button(
            history_buttons_frame,
            text="📤 تصدير التاريخ",
            command=self.export_history,
            bootstyle="info",
            width=15
        ).pack(side=LEFT, padx=5)
        
        ttk_bs.Button(
            history_buttons_frame,
            text="🔄 تحديث",
            command=self.load_backup_history,
            bootstyle="secondary",
            width=10
        ).pack(side=LEFT, padx=5)
    
    def switch_tab(self, tab_id):
        """تبديل التبويبات"""
        if tab_id == "force":
            # الانتقال لشاشة القوة العمومية
            from src.modules.basic_info_complete import BasicInfoCompleteModule
            BasicInfoCompleteModule(self.parent, self.db_manager)
        elif tab_id == "equipment":
            # الانتقال لشاشة المعدات
            from src.modules.equipment_complete import EquipmentCompleteModule
            EquipmentCompleteModule(self.parent, self.db_manager)
        elif tab_id == "categories":
            # الانتقال لشاشة الأصناف
            from src.modules.categories_complete import CategoriesCompleteModule
            CategoriesCompleteModule(self.parent, self.db_manager)
        elif tab_id == "users":
            # الانتقال لشاشة المستخدمين
            from src.modules.users_complete import UsersCompleteModule
            UsersCompleteModule(self.parent, self.db_manager)
        elif tab_id == "weapons":
            # الانتقال لشاشة الأسلحة
            print("الانتقال لشاشة الأسلحة")
        elif tab_id == "queries":
            # الانتقال لأدوات الاستعلام
            print("الانتقال لأدوات الاستعلام")
    
    def browse_backup_path(self):
        """تصفح مسار حفظ النسخة الاحتياطية"""
        path = filedialog.askdirectory(title="اختر مجلد حفظ النسخة الاحتياطية")
        if path:
            self.backup_path_var.set(path)
    
    def browse_restore_file(self):
        """تصفح ملف النسخة الاحتياطية للاستعادة"""
        file_path = filedialog.askopenfilename(
            title="اختر ملف النسخة الاحتياطية",
            filetypes=[("ملفات النسخ الاحتياطي", "*.backup"), ("ملفات مضغوطة", "*.zip"), ("جميع الملفات", "*.*")]
        )
        if file_path:
            self.restore_path_var.set(file_path)
    
    def create_backup(self):
        """إنشاء نسخة احتياطية"""
        if not self.backup_path_var.get():
            messagebox.showerror("خطأ", "يرجى اختيار مسار حفظ النسخة الاحتياطية")
            return
        
        try:
            # إنشاء اسم الملف مع التاريخ والوقت
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_filename = f"backup_{timestamp}.backup"
            backup_full_path = os.path.join(self.backup_path_var.get(), backup_filename)
            
            # إنشاء النسخة الاحتياطية
            with zipfile.ZipFile(backup_full_path, 'w', zipfile.ZIP_DEFLATED) as backup_zip:
                # نسخ قاعدة البيانات
                if self.include_data_var.get():
                    db_path = "src/database/supply_management.db"
                    if os.path.exists(db_path):
                        backup_zip.write(db_path, "database.db")
                
                # نسخ ملفات الإعدادات
                if self.include_settings_var.get():
                    config_path = "config/settings.json"
                    if os.path.exists(config_path):
                        backup_zip.write(config_path, "settings.json")
                
                # نسخ التقارير
                if self.include_reports_var.get():
                    reports_dir = "reports"
                    if os.path.exists(reports_dir):
                        for root, dirs, files in os.walk(reports_dir):
                            for file in files:
                                file_path = os.path.join(root, file)
                                arcname = os.path.relpath(file_path, ".")
                                backup_zip.write(file_path, arcname)
            
            # تسجيل العملية في التاريخ
            self.log_backup_operation("نسخ احتياطي", backup_full_path, "مكتمل")
            
            messagebox.showinfo("نجح", f"تم إنشاء النسخة الاحتياطية بنجاح\n{backup_full_path}")
            self.load_backup_history()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في إنشاء النسخة الاحتياطية: {e}")
    
    def quick_backup(self):
        """نسخ احتياطي سريع"""
        # تعيين مسار افتراضي
        default_path = os.path.join(os.getcwd(), "backups")
        if not os.path.exists(default_path):
            os.makedirs(default_path)
        
        self.backup_path_var.set(default_path)
        self.create_backup()
    
    def schedule_backup(self):
        """نسخ احتياطي مجدول"""
        messagebox.showinfo("نسخ مجدول", "سيتم تطوير وظيفة النسخ المجدول قريباً")
    
    def restore_backup(self):
        """استعادة النسخة الاحتياطية"""
        if not self.restore_path_var.get():
            messagebox.showerror("خطأ", "يرجى اختيار ملف النسخة الاحتياطية")
            return
        
        # تأكيد الاستعادة
        if not messagebox.askyesno("تأكيد", "هل أنت متأكد من استعادة النسخة الاحتياطية؟\nسيتم استبدال البيانات الحالية"):
            return
        
        try:
            # إنشاء نسخة احتياطية قبل الاستعادة
            if self.create_backup_before_restore_var.get():
                self.quick_backup()
            
            # استعادة النسخة
            with zipfile.ZipFile(self.restore_path_var.get(), 'r') as backup_zip:
                # استعادة قاعدة البيانات
                if self.restore_data_var.get() and "database.db" in backup_zip.namelist():
                    backup_zip.extract("database.db", "temp")
                    shutil.move("temp/database.db", "src/database/supply_management.db")
                
                # استعادة الإعدادات
                if self.restore_settings_var.get() and "settings.json" in backup_zip.namelist():
                    backup_zip.extract("settings.json", "temp")
                    shutil.move("temp/settings.json", "config/settings.json")
            
            # تنظيف المجلد المؤقت
            if os.path.exists("temp"):
                shutil.rmtree("temp")
            
            # تسجيل العملية
            self.log_backup_operation("استعادة", self.restore_path_var.get(), "مكتمل")
            
            messagebox.showinfo("نجح", "تم استعادة النسخة الاحتياطية بنجاح\nيرجى إعادة تشغيل البرنامج")
            self.load_backup_history()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في استعادة النسخة الاحتياطية: {e}")
    
    def preview_backup(self):
        """معاينة محتويات النسخة الاحتياطية"""
        if not self.restore_path_var.get():
            messagebox.showerror("خطأ", "يرجى اختيار ملف النسخة الاحتياطية")
            return
        
        try:
            with zipfile.ZipFile(self.restore_path_var.get(), 'r') as backup_zip:
                files_list = backup_zip.namelist()
                files_info = "\n".join(files_list)
                messagebox.showinfo("محتويات النسخة الاحتياطية", f"الملفات الموجودة:\n\n{files_info}")
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في قراءة النسخة الاحتياطية: {e}")
    
    def log_backup_operation(self, operation_type, file_path, status):
        """تسجيل عملية النسخ الاحتياطي"""
        try:
            # حساب حجم الملف
            file_size = "غير معروف"
            if os.path.exists(file_path):
                size_bytes = os.path.getsize(file_path)
                file_size = f"{size_bytes / (1024*1024):.2f} MB"
            
            # تسجيل في قاعدة البيانات (إذا كانت متوفرة)
            current_time = datetime.now()
            date_str = current_time.strftime("%Y-%m-%d")
            time_str = current_time.strftime("%H:%M:%S")
            
            # إضافة للجدول مباشرة
            self.history_tree.insert("", 0, values=(
                date_str, time_str, operation_type, file_path, file_size, status
            ))
            
        except Exception as e:
            print(f"خطأ في تسجيل العملية: {e}")
    
    def load_backup_history(self):
        """تحميل تاريخ النسخ الاحتياطي"""
        # مسح البيانات الحالية
        for item in self.history_tree.get_children():
            self.history_tree.delete(item)
        
        # إضافة بيانات تجريبية
        sample_data = [
            ("2024-01-15", "14:30:25", "نسخ احتياطي", "C:/backups/backup_20240115_143025.backup", "15.2 MB", "مكتمل"),
            ("2024-01-14", "09:15:10", "نسخ احتياطي", "C:/backups/backup_20240114_091510.backup", "14.8 MB", "مكتمل"),
            ("2024-01-13", "16:45:33", "استعادة", "C:/backups/backup_20240110_120000.backup", "13.5 MB", "مكتمل"),
        ]
        
        for data in sample_data:
            self.history_tree.insert("", "end", values=data)
    
    def clear_history(self):
        """مسح تاريخ النسخ"""
        if messagebox.askyesno("تأكيد", "هل أنت متأكد من مسح تاريخ النسخ الاحتياطي؟"):
            for item in self.history_tree.get_children():
                self.history_tree.delete(item)
            messagebox.showinfo("تم", "تم مسح تاريخ النسخ الاحتياطي")
    
    def export_history(self):
        """تصدير تاريخ النسخ"""
        messagebox.showinfo("تصدير", "سيتم تطوير وظيفة تصدير التاريخ قريباً")
