#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
وحدة إدارة المعدات الكاملة مطابقة للبرنامج الأصلي
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import ttkbootstrap as ttk_bs
from ttkbootstrap.constants import *
from datetime import datetime

class EquipmentCompleteModule:
    def __init__(self, parent_frame, db_manager):
        self.parent = parent_frame
        self.db_manager = db_manager
        self.current_record = None
        self.form_vars = {}
        
        # إنشاء الواجهة
        self.create_interface()
        
        # تحميل البيانات
        self.load_data()
    
    def create_interface(self):
        """إنشاء واجهة إدارة المعدات مطابقة للبرنامج الأصلي"""
        # مسح المحتوى السابق
        for widget in self.parent.winfo_children():
            widget.destroy()
        
        # الإطار الرئيسي
        main_frame = ttk_bs.Frame(self.parent)
        main_frame.pack(fill=BOTH, expand=True, padx=10, pady=10)
        
        # العنوان الرئيسي
        title_frame = ttk_bs.Frame(main_frame)
        title_frame.pack(fill=X, pady=(0, 10))
        
        title_label = ttk_bs.Label(
            title_frame,
            text="إدارة المعدات",
            font=("Tahoma", 16, "bold"),
            bootstyle="primary"
        )
        title_label.pack()
        
        # إنشاء التبويبات
        self.create_tabs(main_frame)
        
        # إنشاء شريط الأزرار
        self.create_buttons_bar(main_frame)
        
        # إنشاء منطقة الإدخال
        self.create_input_area(main_frame)
        
        # إنشاء جدول البيانات
        self.create_data_table(main_frame)
    
    def create_tabs(self, parent):
        """إنشاء التبويبات العلوية"""
        tabs_frame = ttk_bs.Frame(parent)
        tabs_frame.pack(fill=X, pady=(0, 10))
        
        # التبويبات مطابقة للبرنامج الأصلي
        tabs = [
            ("القوة العمومية", "force", "info"),
            ("المعدات", "equipment", "primary"),  # النشط
            ("الأصناف", "categories", "info"),
            ("الأسلحة", "weapons", "info"),
            ("المستخدمين", "users", "info"),
            ("أدوات الاستعلام", "queries", "info")
        ]
        
        for text, tab_id, style in tabs:
            btn = ttk_bs.Button(
                tabs_frame,
                text=text,
                command=lambda t=tab_id: self.switch_tab(t),
                bootstyle=style,
                width=15
            )
            btn.pack(side=LEFT, padx=1)
    
    def create_buttons_bar(self, parent):
        """إنشاء شريط الأزرار"""
        buttons_frame = ttk_bs.Frame(parent)
        buttons_frame.pack(fill=X, pady=(0, 10))
        
        # الأزرار مطابقة للبرنامج الأصلي
        buttons = [
            ("إضافة", "add", "success", "🆕"),
            ("حفظ", "save", "primary", "💾"),
            ("تعديل", "edit", "warning", "✏️"),
            ("حذف", "delete", "danger", "🗑️"),
            ("إلغاء", "cancel", "secondary", "❌"),
            ("طباعة", "print", "info", "🖨️"),
            ("تصدير", "export", "info", "📤")
        ]
        
        for text, action, style, icon in buttons:
            btn = ttk_bs.Button(
                buttons_frame,
                text=f"{icon} {text}",
                command=lambda a=action: self.handle_button_action(a),
                bootstyle=style,
                width=12
            )
            btn.pack(side=LEFT, padx=2)
    
    def create_input_area(self, parent):
        """إنشاء منطقة الإدخال مطابقة للبرنامج الأصلي"""
        input_frame = ttk_bs.LabelFrame(parent, text="بيانات المعدة", padding=10)
        input_frame.pack(fill=X, pady=(0, 10))
        
        # الصف الأول من الحقول
        row1_frame = ttk_bs.Frame(input_frame)
        row1_frame.pack(fill=X, pady=5)
        
        # رقم المعدة
        ttk_bs.Label(row1_frame, text="* رقم المعدة:").pack(side=RIGHT, padx=5)
        self.form_vars['serial_number'] = ttk_bs.StringVar()
        serial_entry = ttk_bs.Entry(row1_frame, textvariable=self.form_vars['serial_number'], width=15)
        serial_entry.pack(side=RIGHT, padx=5)
        
        # اسم المعدة
        ttk_bs.Label(row1_frame, text="* اسم المعدة:").pack(side=RIGHT, padx=5)
        self.form_vars['name'] = ttk_bs.StringVar()
        name_entry = ttk_bs.Entry(row1_frame, textvariable=self.form_vars['name'], width=25)
        name_entry.pack(side=RIGHT, padx=5)
        
        # اسم القوة العمومية
        ttk_bs.Label(row1_frame, text="* اسم القوة العمومية:").pack(side=RIGHT, padx=5)
        self.form_vars['force_name'] = ttk_bs.StringVar()
        force_combo = ttk_bs.Combobox(row1_frame, textvariable=self.form_vars['force_name'], width=20)
        force_combo.pack(side=RIGHT, padx=5)
        
        # الصف الثاني من الحقول
        row2_frame = ttk_bs.Frame(input_frame)
        row2_frame.pack(fill=X, pady=5)
        
        # نوع المعدة
        ttk_bs.Label(row2_frame, text="نوع المعدة:").pack(side=RIGHT, padx=5)
        self.form_vars['type'] = ttk_bs.StringVar()
        type_combo = ttk_bs.Combobox(row2_frame, textvariable=self.form_vars['type'], width=15)
        type_combo.pack(side=RIGHT, padx=5)
        
        # الموديل
        ttk_bs.Label(row2_frame, text="الموديل:").pack(side=RIGHT, padx=5)
        self.form_vars['model'] = ttk_bs.StringVar()
        model_entry = ttk_bs.Entry(row2_frame, textvariable=self.form_vars['model'], width=15)
        model_entry.pack(side=RIGHT, padx=5)
        
        # الشركة المصنعة
        ttk_bs.Label(row2_frame, text="الشركة المصنعة:").pack(side=RIGHT, padx=5)
        self.form_vars['manufacturer'] = ttk_bs.StringVar()
        manufacturer_entry = ttk_bs.Entry(row2_frame, textvariable=self.form_vars['manufacturer'], width=20)
        manufacturer_entry.pack(side=RIGHT, padx=5)
        
        # الصف الثالث من الحقول
        row3_frame = ttk_bs.Frame(input_frame)
        row3_frame.pack(fill=X, pady=5)
        
        # سنة الصنع
        ttk_bs.Label(row3_frame, text="سنة الصنع:").pack(side=RIGHT, padx=5)
        self.form_vars['manufacture_year'] = ttk_bs.StringVar()
        year_entry = ttk_bs.Entry(row3_frame, textvariable=self.form_vars['manufacture_year'], width=10)
        year_entry.pack(side=RIGHT, padx=5)
        
        # تاريخ الشراء
        ttk_bs.Label(row3_frame, text="تاريخ الشراء:").pack(side=RIGHT, padx=5)
        self.form_vars['purchase_date'] = ttk_bs.StringVar()
        purchase_entry = ttk_bs.Entry(row3_frame, textvariable=self.form_vars['purchase_date'], width=12)
        purchase_entry.pack(side=RIGHT, padx=5)
        
        # سعر الشراء
        ttk_bs.Label(row3_frame, text="سعر الشراء:").pack(side=RIGHT, padx=5)
        self.form_vars['purchase_price'] = ttk_bs.StringVar()
        price_entry = ttk_bs.Entry(row3_frame, textvariable=self.form_vars['purchase_price'], width=12)
        price_entry.pack(side=RIGHT, padx=5)
        
        # الصف الرابع من الحقول
        row4_frame = ttk_bs.Frame(input_frame)
        row4_frame.pack(fill=X, pady=5)
        
        # حالة المعدة
        ttk_bs.Label(row4_frame, text="حالة المعدة:").pack(side=RIGHT, padx=5)
        self.form_vars['condition_status'] = ttk_bs.StringVar()
        condition_combo = ttk_bs.Combobox(row4_frame, textvariable=self.form_vars['condition_status'], width=12)
        condition_combo['values'] = ("ممتاز", "جيد", "متوسط", "يحتاج صيانة", "معطل")
        condition_combo.pack(side=RIGHT, padx=5)
        
        # الموقع
        ttk_bs.Label(row4_frame, text="الموقع:").pack(side=RIGHT, padx=5)
        self.form_vars['location'] = ttk_bs.StringVar()
        location_entry = ttk_bs.Entry(row4_frame, textvariable=self.form_vars['location'], width=20)
        location_entry.pack(side=RIGHT, padx=5)
        
        # مخصص لـ
        ttk_bs.Label(row4_frame, text="مخصص لـ:").pack(side=RIGHT, padx=5)
        self.form_vars['assigned_to'] = ttk_bs.StringVar()
        assigned_combo = ttk_bs.Combobox(row4_frame, textvariable=self.form_vars['assigned_to'], width=20)
        assigned_combo.pack(side=RIGHT, padx=5)
        
        # الصف الخامس - الملاحظات
        row5_frame = ttk_bs.Frame(input_frame)
        row5_frame.pack(fill=X, pady=5)
        
        ttk_bs.Label(row5_frame, text="الملاحظات:").pack(side=RIGHT, padx=5)
        self.form_vars['notes'] = ttk_bs.StringVar()
        notes_entry = ttk_bs.Entry(row5_frame, textvariable=self.form_vars['notes'], width=60)
        notes_entry.pack(side=RIGHT, padx=5, fill=X, expand=True)
        
        # تحميل البيانات في القوائم المنسدلة
        self.load_combo_data(force_combo, type_combo, assigned_combo)
    
    def create_data_table(self, parent):
        """إنشاء جدول البيانات"""
        table_frame = ttk_bs.LabelFrame(parent, text="قائمة المعدات", padding=10)
        table_frame.pack(fill=BOTH, expand=True)
        
        # إنشاء Treeview
        columns = ("الرقم", "اسم القوة العمومية", "اسم المعدة", "رقم المعدة", "النوع", "الحالة", "الموقع")
        self.tree = ttk_bs.Treeview(table_frame, columns=columns, show="headings", height=12)
        
        # تعيين عناوين الأعمدة
        for col in columns:
            self.tree.heading(col, text=col)
            self.tree.column(col, width=120, anchor=CENTER)
        
        # شريط التمرير
        scrollbar = ttk_bs.Scrollbar(table_frame, orient=VERTICAL, command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)
        
        # تخطيط الجدول
        self.tree.pack(side=LEFT, fill=BOTH, expand=True)
        scrollbar.pack(side=RIGHT, fill=Y)
        
        # ربط حدث النقر
        self.tree.bind("<<TreeviewSelect>>", self.on_item_select)
    
    def switch_tab(self, tab_id):
        """تبديل التبويبات"""
        if tab_id == "force":
            # الانتقال لشاشة القوة العمومية
            from src.modules.basic_info_complete import BasicInfoCompleteModule
            BasicInfoCompleteModule(self.parent, self.db_manager)
        elif tab_id == "categories":
            # الانتقال لشاشة الأصناف
            from src.modules.categories_complete import CategoriesCompleteModule
            CategoriesCompleteModule(self.parent, self.db_manager)
        elif tab_id == "weapons":
            # الانتقال لشاشة الأسلحة
            print("الانتقال لشاشة الأسلحة")
        elif tab_id == "users":
            # الانتقال لشاشة المستخدمين
            from src.modules.users_complete import UsersCompleteModule
            UsersCompleteModule(self.parent, self.db_manager)
        elif tab_id == "queries":
            # الانتقال لأدوات الاستعلام
            print("الانتقال لأدوات الاستعلام")
    
    def handle_button_action(self, action):
        """معالجة أحداث الأزرار"""
        if action == "add":
            self.add_record()
        elif action == "save":
            self.save_record()
        elif action == "edit":
            self.edit_record()
        elif action == "delete":
            self.delete_record()
        elif action == "cancel":
            self.cancel_operation()
        elif action == "print":
            self.print_record()
        elif action == "export":
            self.export_data()
    
    def load_combo_data(self, force_combo, type_combo, assigned_combo):
        """تحميل بيانات القوائم المنسدلة"""
        try:
            # تحميل أسماء القوة العمومية
            forces = self.db_manager.execute_query("SELECT DISTINCT force_name FROM force_basic_info")
            force_names = [force['force_name'] for force in forces] if forces else []
            force_combo['values'] = force_names
            
            # تحميل أنواع المعدات
            equipment_types = ["حاسوب", "طابعة", "ماسح ضوئي", "كاميرا", "جهاز اتصال", "أخرى"]
            type_combo['values'] = equipment_types
            
            # تحميل الأفراد للتخصيص
            personnel = self.db_manager.execute_query("SELECT full_name FROM personnel WHERE is_active = 1")
            personnel_names = [person['full_name'] for person in personnel] if personnel else []
            assigned_combo['values'] = personnel_names
            
        except Exception as e:
            print(f"خطأ في تحميل بيانات القوائم: {e}")
    
    def load_data(self):
        """تحميل البيانات في الجدول"""
        try:
            # مسح البيانات الحالية
            for item in self.tree.get_children():
                self.tree.delete(item)
            
            # تحميل البيانات من قاعدة البيانات
            query = """
                SELECT e.*, f.force_name 
                FROM equipment e
                LEFT JOIN force_basic_info f ON e.assigned_to_unit_id = f.id
                ORDER BY e.id
            """
            records = self.db_manager.execute_query(query)
            
            for i, record in enumerate(records, 1):
                values = (
                    i,
                    record.get('force_name', ''),
                    record.get('name', ''),
                    record.get('serial_number', ''),
                    record.get('type_id', ''),
                    record.get('condition_status', ''),
                    record.get('location', '')
                )
                self.tree.insert("", "end", values=values)
                
        except Exception as e:
            print(f"خطأ في تحميل البيانات: {e}")
    
    def on_item_select(self, event):
        """معالجة تحديد عنصر في الجدول"""
        selection = self.tree.selection()
        if selection:
            item = self.tree.item(selection[0])
            values = item['values']
            
            # تحديث الحقول بالبيانات المحددة
            if len(values) >= 7:
                self.form_vars['name'].set(values[2])
                self.form_vars['serial_number'].set(values[3])
                self.form_vars['condition_status'].set(values[5])
                self.form_vars['location'].set(values[6])
    
    def add_record(self):
        """إضافة سجل جديد"""
        self.clear_form()
        messagebox.showinfo("إضافة", "تم تفعيل وضع الإضافة")
    
    def save_record(self):
        """حفظ السجل"""
        try:
            # جمع البيانات من النموذج
            data = {
                'serial_number': self.form_vars['serial_number'].get().strip(),
                'name': self.form_vars['name'].get().strip(),
                'type_id': self.form_vars['type'].get().strip(),
                'model': self.form_vars['model'].get().strip(),
                'manufacturer': self.form_vars['manufacturer'].get().strip(),
                'manufacture_year': self.form_vars['manufacture_year'].get().strip(),
                'purchase_date': self.form_vars['purchase_date'].get().strip(),
                'purchase_price': self.form_vars['purchase_price'].get().strip(),
                'condition_status': self.form_vars['condition_status'].get().strip(),
                'location': self.form_vars['location'].get().strip(),
                'notes': self.form_vars['notes'].get().strip()
            }
            
            # التحقق من البيانات المطلوبة
            if not data['serial_number'] or not data['name']:
                messagebox.showerror("خطأ", "يرجى ملء الحقول المطلوبة (رقم المعدة واسم المعدة)")
                return
            
            # إنشاء استعلام الإدراج
            query = """
                INSERT INTO equipment 
                (serial_number, name, type_id, model, manufacturer, manufacture_year,
                 purchase_date, purchase_price, condition_status, location, notes)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """
            
            params = (
                data['serial_number'], data['name'], data['type_id'], data['model'],
                data['manufacturer'], data['manufacture_year'], data['purchase_date'],
                data['purchase_price'], data['condition_status'], data['location'], data['notes']
            )
            
            # تنفيذ الاستعلام
            self.db_manager.execute_query(query, params)
            messagebox.showinfo("نجح", "تم حفظ بيانات المعدة بنجاح")
            
            # إعادة تحميل البيانات
            self.load_data()
            self.clear_form()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في حفظ البيانات: {e}")
    
    def edit_record(self):
        """تعديل السجل"""
        selection = self.tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى تحديد معدة للتعديل")
            return
        
        messagebox.showinfo("تعديل", "تم تفعيل وضع التعديل")
    
    def delete_record(self):
        """حذف السجل"""
        selection = self.tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى تحديد معدة للحذف")
            return
        
        if messagebox.askyesno("تأكيد", "هل أنت متأكد من حذف هذه المعدة؟"):
            self.tree.delete(selection[0])
            messagebox.showinfo("حذف", "تم حذف المعدة بنجاح")
    
    def cancel_operation(self):
        """إلغاء العملية"""
        self.clear_form()
        messagebox.showinfo("إلغاء", "تم إلغاء العملية")
    
    def print_record(self):
        """طباعة السجل"""
        messagebox.showinfo("طباعة", "سيتم تطوير وظيفة الطباعة قريباً")
    
    def export_data(self):
        """تصدير البيانات"""
        messagebox.showinfo("تصدير", "سيتم تطوير وظيفة التصدير قريباً")
    
    def clear_form(self):
        """مسح النموذج"""
        for var in self.form_vars.values():
            var.set("")
