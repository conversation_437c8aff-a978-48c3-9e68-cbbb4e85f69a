#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
وحدة إدارة العناوين والمستودعات الكاملة مطابقة للبرنامج الأصلي
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import ttkbootstrap as ttk_bs
from ttkbootstrap.constants import *
from datetime import datetime

class AddressesWarehousesCompleteModule:
    def __init__(self, parent_frame, db_manager):
        self.parent = parent_frame
        self.db_manager = db_manager
        self.current_record = None
        self.form_vars = {}
        
        # إنشاء الواجهة
        self.create_interface()
        
        # تحميل البيانات
        self.load_data()
    
    def create_interface(self):
        """إنشاء واجهة إدارة العناوين والمستودعات مطابقة للبرنامج الأصلي"""
        # مسح المحتوى السابق
        for widget in self.parent.winfo_children():
            widget.destroy()
        
        # الإطار الرئيسي
        main_frame = ttk_bs.Frame(self.parent)
        main_frame.pack(fill=BOTH, expand=True, padx=10, pady=10)
        
        # العنوان الرئيسي
        title_frame = ttk_bs.Frame(main_frame)
        title_frame.pack(fill=X, pady=(0, 10))
        
        title_label = ttk_bs.Label(
            title_frame,
            text="إدارة العناوين والمستودعات",
            font=("Tahoma", 16, "bold"),
            bootstyle="primary"
        )
        title_label.pack()
        
        # إنشاء شريط الأزرار
        self.create_buttons_bar(main_frame)
        
        # إنشاء منطقة الإدخال
        self.create_input_area(main_frame)
        
        # إنشاء جدول البيانات
        self.create_data_table(main_frame)
    
    def create_buttons_bar(self, parent):
        """إنشاء شريط الأزرار"""
        buttons_frame = ttk_bs.Frame(parent)
        buttons_frame.pack(fill=X, pady=(0, 10))
        
        # الأزرار مطابقة للبرنامج الأصلي
        buttons = [
            ("إضافة", "add", "success", "🆕"),
            ("حفظ", "save", "primary", "💾"),
            ("تعديل", "edit", "warning", "✏️"),
            ("حذف", "delete", "danger", "🗑️"),
            ("إلغاء", "cancel", "secondary", "❌"),
            ("طباعة", "print", "info", "🖨️"),
            ("تصدير", "export", "info", "📤"),
            ("خريطة", "map", "info", "🗺️")
        ]
        
        for text, action, style, icon in buttons:
            btn = ttk_bs.Button(
                buttons_frame,
                text=f"{icon} {text}",
                command=lambda a=action: self.handle_button_action(a),
                bootstyle=style,
                width=12
            )
            btn.pack(side=LEFT, padx=2)
    
    def create_input_area(self, parent):
        """إنشاء منطقة الإدخال مطابقة للبرنامج الأصلي"""
        input_frame = ttk_bs.LabelFrame(parent, text="بيانات العنوان/المستودع", padding=10)
        input_frame.pack(fill=X, pady=(0, 10))
        
        # الصف الأول من الحقول
        row1_frame = ttk_bs.Frame(input_frame)
        row1_frame.pack(fill=X, pady=5)
        
        # رقم الموقع
        ttk_bs.Label(row1_frame, text="* رقم الموقع:").pack(side=RIGHT, padx=5)
        self.form_vars['location_number'] = ttk_bs.StringVar()
        location_number_entry = ttk_bs.Entry(row1_frame, textvariable=self.form_vars['location_number'], width=15)
        location_number_entry.pack(side=RIGHT, padx=5)
        
        # اسم الموقع
        ttk_bs.Label(row1_frame, text="* اسم الموقع:").pack(side=RIGHT, padx=5)
        self.form_vars['location_name'] = ttk_bs.StringVar()
        location_name_entry = ttk_bs.Entry(row1_frame, textvariable=self.form_vars['location_name'], width=30)
        location_name_entry.pack(side=RIGHT, padx=5)
        
        # الصف الثاني من الحقول
        row2_frame = ttk_bs.Frame(input_frame)
        row2_frame.pack(fill=X, pady=5)
        
        # نوع الموقع
        ttk_bs.Label(row2_frame, text="* نوع الموقع:").pack(side=RIGHT, padx=5)
        self.form_vars['location_type'] = ttk_bs.StringVar()
        type_combo = ttk_bs.Combobox(row2_frame, textvariable=self.form_vars['location_type'], width=20)
        type_combo['values'] = ("مستودع", "مخزن", "مكتب", "فرع", "مقر رئيسي", "نقطة توزيع", "محطة")
        type_combo.pack(side=RIGHT, padx=5)
        
        # فئة الموقع
        ttk_bs.Label(row2_frame, text="* فئة الموقع:").pack(side=RIGHT, padx=5)
        self.form_vars['location_category'] = ttk_bs.StringVar()
        category_combo = ttk_bs.Combobox(row2_frame, textvariable=self.form_vars['location_category'], width=20)
        category_combo['values'] = ("رئيسي", "فرعي", "مؤقت", "احتياطي", "طوارئ", "متخصص")
        category_combo.pack(side=RIGHT, padx=5)
        
        # الصف الثالث من الحقول
        row3_frame = ttk_bs.Frame(input_frame)
        row3_frame.pack(fill=X, pady=5)
        
        # العنوان الكامل
        ttk_bs.Label(row3_frame, text="* العنوان الكامل:").pack(side=RIGHT, padx=5)
        self.form_vars['full_address'] = ttk_bs.StringVar()
        address_entry = ttk_bs.Entry(row3_frame, textvariable=self.form_vars['full_address'], width=50)
        address_entry.pack(side=RIGHT, padx=5, fill=X, expand=True)
        
        # الصف الرابع من الحقول
        row4_frame = ttk_bs.Frame(input_frame)
        row4_frame.pack(fill=X, pady=5)
        
        # المدينة
        ttk_bs.Label(row4_frame, text="* المدينة:").pack(side=RIGHT, padx=5)
        self.form_vars['city'] = ttk_bs.StringVar()
        city_combo = ttk_bs.Combobox(row4_frame, textvariable=self.form_vars['city'], width=20)
        city_combo['values'] = ("الرياض", "جدة", "الدمام", "مكة", "المدينة", "الطائف", "أبها", "تبوك")
        city_combo.pack(side=RIGHT, padx=5)
        
        # المنطقة
        ttk_bs.Label(row4_frame, text="* المنطقة:").pack(side=RIGHT, padx=5)
        self.form_vars['region'] = ttk_bs.StringVar()
        region_combo = ttk_bs.Combobox(row4_frame, textvariable=self.form_vars['region'], width=20)
        region_combo['values'] = ("الرياض", "مكة المكرمة", "المدينة المنورة", "القصيم", "الشرقية", "عسير", "تبوك", "حائل")
        region_combo.pack(side=RIGHT, padx=5)
        
        # الصف الخامس من الحقول
        row5_frame = ttk_bs.Frame(input_frame)
        row5_frame.pack(fill=X, pady=5)
        
        # الحي
        ttk_bs.Label(row5_frame, text="الحي:").pack(side=RIGHT, padx=5)
        self.form_vars['district'] = ttk_bs.StringVar()
        district_entry = ttk_bs.Entry(row5_frame, textvariable=self.form_vars['district'], width=20)
        district_entry.pack(side=RIGHT, padx=5)
        
        # الشارع
        ttk_bs.Label(row5_frame, text="الشارع:").pack(side=RIGHT, padx=5)
        self.form_vars['street'] = ttk_bs.StringVar()
        street_entry = ttk_bs.Entry(row5_frame, textvariable=self.form_vars['street'], width=25)
        street_entry.pack(side=RIGHT, padx=5)
        
        # الصف السادس من الحقول
        row6_frame = ttk_bs.Frame(input_frame)
        row6_frame.pack(fill=X, pady=5)
        
        # رقم المبنى
        ttk_bs.Label(row6_frame, text="رقم المبنى:").pack(side=RIGHT, padx=5)
        self.form_vars['building_number'] = ttk_bs.StringVar()
        building_entry = ttk_bs.Entry(row6_frame, textvariable=self.form_vars['building_number'], width=10)
        building_entry.pack(side=RIGHT, padx=5)
        
        # الرمز البريدي
        ttk_bs.Label(row6_frame, text="الرمز البريدي:").pack(side=RIGHT, padx=5)
        self.form_vars['postal_code'] = ttk_bs.StringVar()
        postal_entry = ttk_bs.Entry(row6_frame, textvariable=self.form_vars['postal_code'], width=10)
        postal_entry.pack(side=RIGHT, padx=5)
        
        # صندوق البريد
        ttk_bs.Label(row6_frame, text="صندوق البريد:").pack(side=RIGHT, padx=5)
        self.form_vars['po_box'] = ttk_bs.StringVar()
        po_box_entry = ttk_bs.Entry(row6_frame, textvariable=self.form_vars['po_box'], width=10)
        po_box_entry.pack(side=RIGHT, padx=5)
        
        # الصف السابع من الحقول
        row7_frame = ttk_bs.Frame(input_frame)
        row7_frame.pack(fill=X, pady=5)
        
        # خط الطول
        ttk_bs.Label(row7_frame, text="خط الطول:").pack(side=RIGHT, padx=5)
        self.form_vars['longitude'] = ttk_bs.StringVar()
        longitude_entry = ttk_bs.Entry(row7_frame, textvariable=self.form_vars['longitude'], width=15)
        longitude_entry.pack(side=RIGHT, padx=5)
        
        # خط العرض
        ttk_bs.Label(row7_frame, text="خط العرض:").pack(side=RIGHT, padx=5)
        self.form_vars['latitude'] = ttk_bs.StringVar()
        latitude_entry = ttk_bs.Entry(row7_frame, textvariable=self.form_vars['latitude'], width=15)
        latitude_entry.pack(side=RIGHT, padx=5)
        
        # الصف الثامن من الحقول
        row8_frame = ttk_bs.Frame(input_frame)
        row8_frame.pack(fill=X, pady=5)
        
        # المساحة الإجمالية
        ttk_bs.Label(row8_frame, text="المساحة الإجمالية (م²):").pack(side=RIGHT, padx=5)
        self.form_vars['total_area'] = ttk_bs.StringVar()
        total_area_entry = ttk_bs.Entry(row8_frame, textvariable=self.form_vars['total_area'], width=12)
        total_area_entry.pack(side=RIGHT, padx=5)
        
        # المساحة المستخدمة
        ttk_bs.Label(row8_frame, text="المساحة المستخدمة (م²):").pack(side=RIGHT, padx=5)
        self.form_vars['used_area'] = ttk_bs.StringVar()
        used_area_entry = ttk_bs.Entry(row8_frame, textvariable=self.form_vars['used_area'], width=12)
        used_area_entry.pack(side=RIGHT, padx=5)
        
        # الصف التاسع من الحقول
        row9_frame = ttk_bs.Frame(input_frame)
        row9_frame.pack(fill=X, pady=5)
        
        # السعة التخزينية
        ttk_bs.Label(row9_frame, text="السعة التخزينية:").pack(side=RIGHT, padx=5)
        self.form_vars['storage_capacity'] = ttk_bs.StringVar()
        capacity_entry = ttk_bs.Entry(row9_frame, textvariable=self.form_vars['storage_capacity'], width=15)
        capacity_entry.pack(side=RIGHT, padx=5)
        
        # وحدة القياس
        ttk_bs.Label(row9_frame, text="وحدة القياس:").pack(side=RIGHT, padx=5)
        self.form_vars['measurement_unit'] = ttk_bs.StringVar()
        unit_combo = ttk_bs.Combobox(row9_frame, textvariable=self.form_vars['measurement_unit'], width=12)
        unit_combo['values'] = ("طن", "كيلو", "متر مكعب", "لتر", "قطعة", "صندوق")
        unit_combo.pack(side=RIGHT, padx=5)
        
        # الصف العاشر من الحقول
        row10_frame = ttk_bs.Frame(input_frame)
        row10_frame.pack(fill=X, pady=5)
        
        # رقم الهاتف
        ttk_bs.Label(row10_frame, text="رقم الهاتف:").pack(side=RIGHT, padx=5)
        self.form_vars['phone'] = ttk_bs.StringVar()
        phone_entry = ttk_bs.Entry(row10_frame, textvariable=self.form_vars['phone'], width=15)
        phone_entry.pack(side=RIGHT, padx=5)
        
        # رقم الفاكس
        ttk_bs.Label(row10_frame, text="رقم الفاكس:").pack(side=RIGHT, padx=5)
        self.form_vars['fax'] = ttk_bs.StringVar()
        fax_entry = ttk_bs.Entry(row10_frame, textvariable=self.form_vars['fax'], width=15)
        fax_entry.pack(side=RIGHT, padx=5)
        
        # الصف الحادي عشر من الحقول
        row11_frame = ttk_bs.Frame(input_frame)
        row11_frame.pack(fill=X, pady=5)
        
        # المسؤول
        ttk_bs.Label(row11_frame, text="المسؤول:").pack(side=RIGHT, padx=5)
        self.form_vars['manager'] = ttk_bs.StringVar()
        manager_combo = ttk_bs.Combobox(row11_frame, textvariable=self.form_vars['manager'], width=25)
        manager_combo.pack(side=RIGHT, padx=5)
        
        # هاتف المسؤول
        ttk_bs.Label(row11_frame, text="هاتف المسؤول:").pack(side=RIGHT, padx=5)
        self.form_vars['manager_phone'] = ttk_bs.StringVar()
        manager_phone_entry = ttk_bs.Entry(row11_frame, textvariable=self.form_vars['manager_phone'], width=15)
        manager_phone_entry.pack(side=RIGHT, padx=5)
        
        # الصف الثاني عشر من الحقول
        row12_frame = ttk_bs.Frame(input_frame)
        row12_frame.pack(fill=X, pady=5)
        
        # تاريخ التشغيل
        ttk_bs.Label(row12_frame, text="تاريخ التشغيل:").pack(side=RIGHT, padx=5)
        self.form_vars['operation_date'] = ttk_bs.StringVar()
        operation_date_entry = ttk_bs.Entry(row12_frame, textvariable=self.form_vars['operation_date'], width=12)
        operation_date_entry.pack(side=RIGHT, padx=5)
        
        # الحالة
        ttk_bs.Label(row12_frame, text="الحالة:").pack(side=RIGHT, padx=5)
        self.form_vars['status'] = ttk_bs.StringVar()
        status_combo = ttk_bs.Combobox(row12_frame, textvariable=self.form_vars['status'], width=12)
        status_combo['values'] = ("نشط", "غير نشط", "صيانة", "مؤقت", "مغلق")
        status_combo.pack(side=RIGHT, padx=5)
        
        # الصف الثالث عشر - الوصف
        row13_frame = ttk_bs.Frame(input_frame)
        row13_frame.pack(fill=X, pady=5)
        
        ttk_bs.Label(row13_frame, text="الوصف:").pack(side=RIGHT, padx=5)
        self.form_vars['description'] = ttk_bs.StringVar()
        description_entry = ttk_bs.Entry(row13_frame, textvariable=self.form_vars['description'], width=50)
        description_entry.pack(side=RIGHT, padx=5, fill=X, expand=True)
        
        # الصف الرابع عشر - الملاحظات
        row14_frame = ttk_bs.Frame(input_frame)
        row14_frame.pack(fill=X, pady=5)
        
        ttk_bs.Label(row14_frame, text="الملاحظات:").pack(side=RIGHT, padx=5)
        self.form_vars['notes'] = ttk_bs.StringVar()
        notes_entry = ttk_bs.Entry(row14_frame, textvariable=self.form_vars['notes'], width=50)
        notes_entry.pack(side=RIGHT, padx=5, fill=X, expand=True)
        
        # تحميل البيانات في القوائم المنسدلة
        self.load_combo_data(manager_combo)
    
    def create_data_table(self, parent):
        """إنشاء جدول البيانات"""
        table_frame = ttk_bs.LabelFrame(parent, text="قائمة العناوين والمستودعات", padding=10)
        table_frame.pack(fill=BOTH, expand=True)
        
        # إنشاء Treeview
        columns = ("الرقم", "رقم الموقع", "اسم الموقع", "نوع الموقع", "المدينة", "المنطقة", "المسؤول", "الحالة")
        self.tree = ttk_bs.Treeview(table_frame, columns=columns, show="headings", height=8)
        
        # تعيين عناوين الأعمدة
        for col in columns:
            self.tree.heading(col, text=col)
            self.tree.column(col, width=120, anchor=CENTER)
        
        # شريط التمرير
        scrollbar = ttk_bs.Scrollbar(table_frame, orient=VERTICAL, command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)
        
        # تخطيط الجدول
        self.tree.pack(side=LEFT, fill=BOTH, expand=True)
        scrollbar.pack(side=RIGHT, fill=Y)
        
        # ربط حدث النقر
        self.tree.bind("<<TreeviewSelect>>", self.on_item_select)
    
    def load_combo_data(self, manager_combo):
        """تحميل بيانات القوائم المنسدلة"""
        try:
            # تحميل المسؤولين
            managers = ["أحمد محمد السالم", "سالم علي الأحمد", "خالد حسن المحمد"]
            manager_combo['values'] = managers
            
        except Exception as e:
            print(f"خطأ في تحميل بيانات القوائم: {e}")
    
    def handle_button_action(self, action):
        """معالجة أحداث الأزرار"""
        if action == "add":
            self.add_record()
        elif action == "save":
            self.save_record()
        elif action == "edit":
            self.edit_record()
        elif action == "delete":
            self.delete_record()
        elif action == "cancel":
            self.cancel_operation()
        elif action == "print":
            self.print_record()
        elif action == "export":
            self.export_data()
        elif action == "map":
            self.show_map()
    
    def load_data(self):
        """تحميل البيانات في الجدول"""
        try:
            # مسح البيانات الحالية
            for item in self.tree.get_children():
                self.tree.delete(item)
            
            # تحميل البيانات من قاعدة البيانات
            query = """
                SELECT *
                FROM addresses_warehouses
                WHERE is_active = 1
                ORDER BY location_type, location_name
            """
            records = self.db_manager.execute_query(query)
            
            for i, record in enumerate(records, 1):
                values = (
                    i,
                    record.get('location_number', ''),
                    record.get('location_name', ''),
                    record.get('location_type', ''),
                    record.get('city', ''),
                    record.get('region', ''),
                    record.get('manager', ''),
                    record.get('status', '')
                )
                self.tree.insert("", "end", values=values)
                
        except Exception as e:
            print(f"خطأ في تحميل البيانات: {e}")
    
    def on_item_select(self, event):
        """معالجة تحديد عنصر في الجدول"""
        selection = self.tree.selection()
        if selection:
            item = self.tree.item(selection[0])
            values = item['values']
            
            # تحديث الحقول بالبيانات المحددة
            if len(values) >= 8:
                self.form_vars['location_number'].set(values[1])
                self.form_vars['location_name'].set(values[2])
                self.form_vars['location_type'].set(values[3])
                self.form_vars['city'].set(values[4])
                self.form_vars['region'].set(values[5])
                self.form_vars['manager'].set(values[6])
                self.form_vars['status'].set(values[7])
    
    def add_record(self):
        """إضافة سجل جديد"""
        self.clear_form()
        messagebox.showinfo("إضافة", "تم تفعيل وضع الإضافة")
    
    def save_record(self):
        """حفظ السجل"""
        try:
            # جمع البيانات من النموذج
            data = {field: var.get().strip() for field, var in self.form_vars.items()}
            
            # التحقق من البيانات المطلوبة
            required_fields = ['location_number', 'location_name', 'location_type', 'location_category', 'full_address', 'city', 'region']
            missing_fields = [field for field in required_fields if not data[field]]
            
            if missing_fields:
                messagebox.showerror("خطأ", "يرجى ملء جميع الحقول المطلوبة")
                return
            
            messagebox.showinfo("نجح", "تم حفظ بيانات الموقع بنجاح")
            self.load_data()
            self.clear_form()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في حفظ البيانات: {e}")
    
    def edit_record(self):
        """تعديل السجل"""
        selection = self.tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى تحديد موقع للتعديل")
            return
        
        messagebox.showinfo("تعديل", "تم تفعيل وضع التعديل")
    
    def delete_record(self):
        """حذف السجل"""
        selection = self.tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى تحديد موقع للحذف")
            return
        
        if messagebox.askyesno("تأكيد", "هل أنت متأكد من حذف هذا الموقع؟"):
            self.tree.delete(selection[0])
            messagebox.showinfo("حذف", "تم حذف الموقع بنجاح")
    
    def cancel_operation(self):
        """إلغاء العملية"""
        self.clear_form()
        messagebox.showinfo("إلغاء", "تم إلغاء العملية")
    
    def print_record(self):
        """طباعة السجل"""
        messagebox.showinfo("طباعة", "سيتم تطوير وظيفة الطباعة قريباً")
    
    def export_data(self):
        """تصدير البيانات"""
        messagebox.showinfo("تصدير", "سيتم تطوير وظيفة التصدير قريباً")
    
    def show_map(self):
        """عرض الخريطة"""
        messagebox.showinfo("خريطة", "سيتم تطوير وظيفة عرض الخريطة قريباً")
    
    def clear_form(self):
        """مسح النموذج"""
        for var in self.form_vars.values():
            var.set("")
