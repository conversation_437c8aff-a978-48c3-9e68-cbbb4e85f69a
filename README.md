# نظام إدارة التموين العام

نظام شامل لإدارة التموين والموارد في القوات العمومية، مطور بلغة Python باستخدام واجهة المستخدم الرسومية tkinter.

## المميزات الرئيسية

- واجهة مستخدم عربية بالكامل مع دعم RTL
- قاعدة بيانات SQLite محسنة للأداء
- نظام مستخدمين متعدد المستويات
- تقارير شاملة بصيغ متعددة
- نظام بحث متقدم
- نسخ احتياطي واستعادة
- واجهة حديثة باستخدام ttkbootstrap

## الشاشات المتوفرة

### الشاشات الأساسية
- **لوحة التحكم**: عرض إحصائيات النظام والمعلومات العامة
- **المعلومات الأساسية**: إدارة البيانات الأساسية للقوة العمومية
- **المعدات**: إدارة المعدات والأجهزة
- **الأصناف**: إدارة أصناف المواد والمعدات
- **المستخدمين**: إدارة مستخدمي النظام
- **النسخ الاحتياطي**: إنشاء واستعادة النسخ الاحتياطية

### شاشات البيانات الإضافية
- **الوحدات**: إدارة الوحدات التنظيمية
- **البيانات الشخصية**: إدارة بيانات الأفراد
- **الرتب**: إدارة الرتب العسكرية
- **المسميات الوظيفية**: إدارة المناصب والوظائف
- **مسميات المعدات**: إدارة أسماء وأنواع المعدات
- **الجدول التنظيمي**: إدارة الهيكل التنظيمي
- **الألوان**: إدارة الألوان المستخدمة في النظام
- **جهة الإضافة**: إدارة جهات الإضافة والتزويد
- **العناوين والمستودعات**: إدارة المواقع والمستودعات
- **صور وشعار التقارير**: إدارة الصور والشعارات
- **إدارة البيانات الأخرى**: إدارة البيانات المتنوعة
- **إدارة العربات**: إدارة أسطول المركبات
- **إدارة السلاح**: إدارة الأسلحة والذخائر

### شاشات التقارير والبحث
- **نظام التقارير**: إنتاج وإدارة التقارير المتخصصة
  - إنشاء تقارير مخصصة
  - تحديد معايير التقرير
  - تصدير بصيغ متعددة (PDF, Excel, Word)
  - إدارة قوالب التقارير
  - جدولة التقارير الدورية

- **نظام التقارير العامة**: إنتاج التقارير الشاملة
  - تقارير الحالة العامة
  - تقارير الأداء الشامل
  - تقارير الموارد والتشغيل
  - تقارير الصيانة والجودة
  - تضمين البيانات من جميع الأقسام

- **البحث الشامل**: البحث المتقدم عبر النظام
  - البحث في جميع قواعد البيانات
  - فلترة متقدمة حسب التاريخ والقسم
  - البحث في المخزون والمعدات والأفراد
  - البحث في العربات والأسلحة
  - حفظ وتحميل معايير البحث
  - تصدير نتائج البحث

## متطلبات النظام

- Python 3.8 أو أحدث
- نظام التشغيل: Windows 10/11, Linux, macOS
- ذاكرة: 4 GB RAM كحد أدنى
- مساحة القرص: 500 MB للتطبيق + مساحة إضافية للبيانات

## التثبيت والتشغيل

### 1. تثبيت Python
تأكد من تثبيت Python 3.8 أو أحدث على النظام

### 2. إنشاء البيئة الافتراضية
```bash
python -m venv venv
```

### 3. تفعيل البيئة الافتراضية
```bash
# Windows
venv\Scripts\activate

# Linux/macOS
source venv/bin/activate
```

### 4. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

### 5. تشغيل التطبيق
```bash
python main.py
```

## بيانات الدخول الافتراضية

- **اسم المستخدم**: admin
- **كلمة المرور**: admin123

## هيكل المشروع

```
نظام_التموين_الجديد/
├── main.py                 # نقطة البداية الرئيسية
├── requirements.txt        # متطلبات Python
├── config/                 # ملفات التكوين
├── src/                    # الكود المصدري
│   ├── ui/                 # واجهة المستخدم
│   ├── modules/            # وحدات النظام
│   ├── database/           # إدارة قاعدة البيانات
│   └── utils/              # أدوات مساعدة
├── data/                   # ملفات البيانات
├── logs/                   # ملفات السجلات
└── backups/                # النسخ الاحتياطية
```

## الدعم والصيانة

للحصول على الدعم أو الإبلاغ عن مشاكل، يرجى التواصل مع فريق التطوير.

## الترخيص

هذا البرنامج مطور خصيصاً لإدارة التموين في القوات العمومية.
