#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
وحدة البيانات الشخصية الكاملة مطابقة للبرنامج الأصلي
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import ttkbootstrap as ttk_bs
from ttkbootstrap.constants import *
from datetime import datetime

class PersonalDataCompleteModule:
    def __init__(self, parent_frame, db_manager):
        self.parent = parent_frame
        self.db_manager = db_manager
        self.current_record = None
        self.form_vars = {}
        
        # إنشاء الواجهة
        self.create_interface()
        
        # تحميل البيانات
        self.load_data()
    
    def create_interface(self):
        """إنشاء واجهة البيانات الشخصية مطابقة للبرنامج الأصلي"""
        # مسح المحتوى السابق
        for widget in self.parent.winfo_children():
            widget.destroy()
        
        # الإطار الرئيسي
        main_frame = ttk_bs.Frame(self.parent)
        main_frame.pack(fill=BOTH, expand=True, padx=10, pady=10)
        
        # العنوان الرئيسي
        title_frame = ttk_bs.Frame(main_frame)
        title_frame.pack(fill=X, pady=(0, 10))
        
        title_label = ttk_bs.Label(
            title_frame,
            text="البيانات الشخصية",
            font=("Tahoma", 16, "bold"),
            bootstyle="primary"
        )
        title_label.pack()
        
        # إنشاء شريط الأزرار
        self.create_buttons_bar(main_frame)
        
        # إنشاء منطقة الإدخال
        self.create_input_area(main_frame)
        
        # إنشاء جدول البيانات
        self.create_data_table(main_frame)
    
    def create_buttons_bar(self, parent):
        """إنشاء شريط الأزرار"""
        buttons_frame = ttk_bs.Frame(parent)
        buttons_frame.pack(fill=X, pady=(0, 10))
        
        # الأزرار مطابقة للبرنامج الأصلي
        buttons = [
            ("إضافة", "add", "success", "🆕"),
            ("حفظ", "save", "primary", "💾"),
            ("تعديل", "edit", "warning", "✏️"),
            ("حذف", "delete", "danger", "🗑️"),
            ("إلغاء", "cancel", "secondary", "❌"),
            ("طباعة", "print", "info", "🖨️"),
            ("تصدير", "export", "info", "📤")
        ]
        
        for text, action, style, icon in buttons:
            btn = ttk_bs.Button(
                buttons_frame,
                text=f"{icon} {text}",
                command=lambda a=action: self.handle_button_action(a),
                bootstyle=style,
                width=12
            )
            btn.pack(side=LEFT, padx=2)
    
    def create_input_area(self, parent):
        """إنشاء منطقة الإدخال مطابقة للبرنامج الأصلي"""
        input_frame = ttk_bs.LabelFrame(parent, text="البيانات الشخصية", padding=10)
        input_frame.pack(fill=X, pady=(0, 10))
        
        # الصف الأول من الحقول
        row1_frame = ttk_bs.Frame(input_frame)
        row1_frame.pack(fill=X, pady=5)
        
        # الرقم العسكري
        ttk_bs.Label(row1_frame, text="* الرقم العسكري:").pack(side=RIGHT, padx=5)
        self.form_vars['military_number'] = ttk_bs.StringVar()
        military_entry = ttk_bs.Entry(row1_frame, textvariable=self.form_vars['military_number'], width=15)
        military_entry.pack(side=RIGHT, padx=5)
        
        # الاسم الكامل
        ttk_bs.Label(row1_frame, text="* الاسم الكامل:").pack(side=RIGHT, padx=5)
        self.form_vars['full_name'] = ttk_bs.StringVar()
        name_entry = ttk_bs.Entry(row1_frame, textvariable=self.form_vars['full_name'], width=30)
        name_entry.pack(side=RIGHT, padx=5)
        
        # الصف الثاني من الحقول
        row2_frame = ttk_bs.Frame(input_frame)
        row2_frame.pack(fill=X, pady=5)
        
        # رقم الهوية
        ttk_bs.Label(row2_frame, text="* رقم الهوية:").pack(side=RIGHT, padx=5)
        self.form_vars['national_id'] = ttk_bs.StringVar()
        id_entry = ttk_bs.Entry(row2_frame, textvariable=self.form_vars['national_id'], width=15)
        id_entry.pack(side=RIGHT, padx=5)
        
        # تاريخ الميلاد
        ttk_bs.Label(row2_frame, text="* تاريخ الميلاد:").pack(side=RIGHT, padx=5)
        self.form_vars['birth_date'] = ttk_bs.StringVar()
        birth_entry = ttk_bs.Entry(row2_frame, textvariable=self.form_vars['birth_date'], width=12)
        birth_entry.pack(side=RIGHT, padx=5)
        
        # مكان الميلاد
        ttk_bs.Label(row2_frame, text="مكان الميلاد:").pack(side=RIGHT, padx=5)
        self.form_vars['birth_place'] = ttk_bs.StringVar()
        birth_place_entry = ttk_bs.Entry(row2_frame, textvariable=self.form_vars['birth_place'], width=20)
        birth_place_entry.pack(side=RIGHT, padx=5)
        
        # الصف الثالث من الحقول
        row3_frame = ttk_bs.Frame(input_frame)
        row3_frame.pack(fill=X, pady=5)
        
        # الجنسية
        ttk_bs.Label(row3_frame, text="* الجنسية:").pack(side=RIGHT, padx=5)
        self.form_vars['nationality'] = ttk_bs.StringVar()
        nationality_combo = ttk_bs.Combobox(row3_frame, textvariable=self.form_vars['nationality'], width=15)
        nationality_combo['values'] = ("سعودي", "مقيم", "أخرى")
        nationality_combo.pack(side=RIGHT, padx=5)
        
        # الديانة
        ttk_bs.Label(row3_frame, text="الديانة:").pack(side=RIGHT, padx=5)
        self.form_vars['religion'] = ttk_bs.StringVar()
        religion_combo = ttk_bs.Combobox(row3_frame, textvariable=self.form_vars['religion'], width=12)
        religion_combo['values'] = ("مسلم", "مسيحي", "أخرى")
        religion_combo.pack(side=RIGHT, padx=5)
        
        # الحالة الاجتماعية
        ttk_bs.Label(row3_frame, text="الحالة الاجتماعية:").pack(side=RIGHT, padx=5)
        self.form_vars['marital_status'] = ttk_bs.StringVar()
        marital_combo = ttk_bs.Combobox(row3_frame, textvariable=self.form_vars['marital_status'], width=12)
        marital_combo['values'] = ("أعزب", "متزوج", "مطلق", "أرمل")
        marital_combo.pack(side=RIGHT, padx=5)
        
        # الصف الرابع من الحقول
        row4_frame = ttk_bs.Frame(input_frame)
        row4_frame.pack(fill=X, pady=5)
        
        # المؤهل العلمي
        ttk_bs.Label(row4_frame, text="المؤهل العلمي:").pack(side=RIGHT, padx=5)
        self.form_vars['education'] = ttk_bs.StringVar()
        education_combo = ttk_bs.Combobox(row4_frame, textvariable=self.form_vars['education'], width=20)
        education_combo['values'] = ("ابتدائي", "متوسط", "ثانوي", "دبلوم", "بكالوريوس", "ماجستير", "دكتوراه")
        education_combo.pack(side=RIGHT, padx=5)
        
        # التخصص
        ttk_bs.Label(row4_frame, text="التخصص:").pack(side=RIGHT, padx=5)
        self.form_vars['specialization'] = ttk_bs.StringVar()
        spec_entry = ttk_bs.Entry(row4_frame, textvariable=self.form_vars['specialization'], width=20)
        spec_entry.pack(side=RIGHT, padx=5)
        
        # الصف الخامس من الحقول
        row5_frame = ttk_bs.Frame(input_frame)
        row5_frame.pack(fill=X, pady=5)
        
        # رقم الجوال
        ttk_bs.Label(row5_frame, text="رقم الجوال:").pack(side=RIGHT, padx=5)
        self.form_vars['mobile'] = ttk_bs.StringVar()
        mobile_entry = ttk_bs.Entry(row5_frame, textvariable=self.form_vars['mobile'], width=15)
        mobile_entry.pack(side=RIGHT, padx=5)
        
        # البريد الإلكتروني
        ttk_bs.Label(row5_frame, text="البريد الإلكتروني:").pack(side=RIGHT, padx=5)
        self.form_vars['email'] = ttk_bs.StringVar()
        email_entry = ttk_bs.Entry(row5_frame, textvariable=self.form_vars['email'], width=25)
        email_entry.pack(side=RIGHT, padx=5)
        
        # الصف السادس من الحقول
        row6_frame = ttk_bs.Frame(input_frame)
        row6_frame.pack(fill=X, pady=5)
        
        # العنوان الحالي
        ttk_bs.Label(row6_frame, text="العنوان الحالي:").pack(side=RIGHT, padx=5)
        self.form_vars['current_address'] = ttk_bs.StringVar()
        current_address_entry = ttk_bs.Entry(row6_frame, textvariable=self.form_vars['current_address'], width=40)
        current_address_entry.pack(side=RIGHT, padx=5)
        
        # الصف السابع من الحقول
        row7_frame = ttk_bs.Frame(input_frame)
        row7_frame.pack(fill=X, pady=5)
        
        # العنوان الدائم
        ttk_bs.Label(row7_frame, text="العنوان الدائم:").pack(side=RIGHT, padx=5)
        self.form_vars['permanent_address'] = ttk_bs.StringVar()
        permanent_address_entry = ttk_bs.Entry(row7_frame, textvariable=self.form_vars['permanent_address'], width=40)
        permanent_address_entry.pack(side=RIGHT, padx=5)
        
        # الصف الثامن من الحقول
        row8_frame = ttk_bs.Frame(input_frame)
        row8_frame.pack(fill=X, pady=5)
        
        # اسم الأب
        ttk_bs.Label(row8_frame, text="اسم الأب:").pack(side=RIGHT, padx=5)
        self.form_vars['father_name'] = ttk_bs.StringVar()
        father_entry = ttk_bs.Entry(row8_frame, textvariable=self.form_vars['father_name'], width=25)
        father_entry.pack(side=RIGHT, padx=5)
        
        # اسم الأم
        ttk_bs.Label(row8_frame, text="اسم الأم:").pack(side=RIGHT, padx=5)
        self.form_vars['mother_name'] = ttk_bs.StringVar()
        mother_entry = ttk_bs.Entry(row8_frame, textvariable=self.form_vars['mother_name'], width=25)
        mother_entry.pack(side=RIGHT, padx=5)
        
        # الصف التاسع من الحقول
        row9_frame = ttk_bs.Frame(input_frame)
        row9_frame.pack(fill=X, pady=5)
        
        # شخص الاتصال في حالة الطوارئ
        ttk_bs.Label(row9_frame, text="شخص الاتصال في حالة الطوارئ:").pack(side=RIGHT, padx=5)
        self.form_vars['emergency_contact'] = ttk_bs.StringVar()
        emergency_entry = ttk_bs.Entry(row9_frame, textvariable=self.form_vars['emergency_contact'], width=25)
        emergency_entry.pack(side=RIGHT, padx=5)
        
        # رقم هاتف الطوارئ
        ttk_bs.Label(row9_frame, text="رقم هاتف الطوارئ:").pack(side=RIGHT, padx=5)
        self.form_vars['emergency_phone'] = ttk_bs.StringVar()
        emergency_phone_entry = ttk_bs.Entry(row9_frame, textvariable=self.form_vars['emergency_phone'], width=15)
        emergency_phone_entry.pack(side=RIGHT, padx=5)
        
        # الصف العاشر - الملاحظات
        row10_frame = ttk_bs.Frame(input_frame)
        row10_frame.pack(fill=X, pady=5)
        
        ttk_bs.Label(row10_frame, text="الملاحظات:").pack(side=RIGHT, padx=5)
        self.form_vars['notes'] = ttk_bs.StringVar()
        notes_entry = ttk_bs.Entry(row10_frame, textvariable=self.form_vars['notes'], width=60)
        notes_entry.pack(side=RIGHT, padx=5, fill=X, expand=True)
    
    def create_data_table(self, parent):
        """إنشاء جدول البيانات"""
        table_frame = ttk_bs.LabelFrame(parent, text="قائمة البيانات الشخصية", padding=10)
        table_frame.pack(fill=BOTH, expand=True)
        
        # إنشاء Treeview
        columns = ("الرقم", "الرقم العسكري", "الاسم الكامل", "رقم الهوية", "تاريخ الميلاد", "الجنسية", "رقم الجوال", "المؤهل العلمي")
        self.tree = ttk_bs.Treeview(table_frame, columns=columns, show="headings", height=10)
        
        # تعيين عناوين الأعمدة
        for col in columns:
            self.tree.heading(col, text=col)
            self.tree.column(col, width=120, anchor=CENTER)
        
        # شريط التمرير
        scrollbar = ttk_bs.Scrollbar(table_frame, orient=VERTICAL, command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)
        
        # تخطيط الجدول
        self.tree.pack(side=LEFT, fill=BOTH, expand=True)
        scrollbar.pack(side=RIGHT, fill=Y)
        
        # ربط حدث النقر
        self.tree.bind("<<TreeviewSelect>>", self.on_item_select)
    
    def handle_button_action(self, action):
        """معالجة أحداث الأزرار"""
        if action == "add":
            self.add_record()
        elif action == "save":
            self.save_record()
        elif action == "edit":
            self.edit_record()
        elif action == "delete":
            self.delete_record()
        elif action == "cancel":
            self.cancel_operation()
        elif action == "print":
            self.print_record()
        elif action == "export":
            self.export_data()
    
    def load_data(self):
        """تحميل البيانات في الجدول"""
        try:
            # مسح البيانات الحالية
            for item in self.tree.get_children():
                self.tree.delete(item)
            
            # تحميل البيانات من قاعدة البيانات
            query = """
                SELECT *
                FROM personal_data
                WHERE is_active = 1
                ORDER BY id
            """
            records = self.db_manager.execute_query(query)
            
            for i, record in enumerate(records, 1):
                values = (
                    i,
                    record.get('military_number', ''),
                    record.get('full_name', ''),
                    record.get('national_id', ''),
                    record.get('birth_date', ''),
                    record.get('nationality', ''),
                    record.get('mobile', ''),
                    record.get('education', '')
                )
                self.tree.insert("", "end", values=values)
                
        except Exception as e:
            print(f"خطأ في تحميل البيانات: {e}")
    
    def on_item_select(self, event):
        """معالجة تحديد عنصر في الجدول"""
        selection = self.tree.selection()
        if selection:
            item = self.tree.item(selection[0])
            values = item['values']
            
            # تحديث الحقول بالبيانات المحددة
            if len(values) >= 8:
                self.form_vars['military_number'].set(values[1])
                self.form_vars['full_name'].set(values[2])
                self.form_vars['national_id'].set(values[3])
                self.form_vars['birth_date'].set(values[4])
                self.form_vars['nationality'].set(values[5])
                self.form_vars['mobile'].set(values[6])
                self.form_vars['education'].set(values[7])
    
    def add_record(self):
        """إضافة سجل جديد"""
        self.clear_form()
        messagebox.showinfo("إضافة", "تم تفعيل وضع الإضافة")
    
    def save_record(self):
        """حفظ السجل"""
        try:
            # جمع البيانات من النموذج
            data = {field: var.get().strip() for field, var in self.form_vars.items()}
            
            # التحقق من البيانات المطلوبة
            required_fields = ['military_number', 'full_name', 'national_id', 'birth_date', 'nationality']
            missing_fields = [field for field in required_fields if not data[field]]
            
            if missing_fields:
                messagebox.showerror("خطأ", "يرجى ملء جميع الحقول المطلوبة")
                return
            
            # إنشاء استعلام الإدراج
            query = """
                INSERT INTO personal_data 
                (military_number, full_name, national_id, birth_date, birth_place, nationality,
                 religion, marital_status, education, specialization, mobile, email,
                 current_address, permanent_address, father_name, mother_name,
                 emergency_contact, emergency_phone, notes, is_active)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 1)
            """
            
            params = tuple(data[field] for field in [
                'military_number', 'full_name', 'national_id', 'birth_date', 'birth_place',
                'nationality', 'religion', 'marital_status', 'education', 'specialization',
                'mobile', 'email', 'current_address', 'permanent_address', 'father_name',
                'mother_name', 'emergency_contact', 'emergency_phone', 'notes'
            ])
            
            # تنفيذ الاستعلام
            self.db_manager.execute_query(query, params)
            messagebox.showinfo("نجح", "تم حفظ البيانات الشخصية بنجاح")
            
            # إعادة تحميل البيانات
            self.load_data()
            self.clear_form()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في حفظ البيانات: {e}")
    
    def edit_record(self):
        """تعديل السجل"""
        selection = self.tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى تحديد سجل للتعديل")
            return
        
        messagebox.showinfo("تعديل", "تم تفعيل وضع التعديل")
    
    def delete_record(self):
        """حذف السجل"""
        selection = self.tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى تحديد سجل للحذف")
            return
        
        if messagebox.askyesno("تأكيد", "هل أنت متأكد من حذف هذا السجل؟"):
            self.tree.delete(selection[0])
            messagebox.showinfo("حذف", "تم حذف السجل بنجاح")
    
    def cancel_operation(self):
        """إلغاء العملية"""
        self.clear_form()
        messagebox.showinfo("إلغاء", "تم إلغاء العملية")
    
    def print_record(self):
        """طباعة السجل"""
        messagebox.showinfo("طباعة", "سيتم تطوير وظيفة الطباعة قريباً")
    
    def export_data(self):
        """تصدير البيانات"""
        messagebox.showinfo("تصدير", "سيتم تطوير وظيفة التصدير قريباً")
    
    def clear_form(self):
        """مسح النموذج"""
        for var in self.form_vars.values():
            var.set("")
