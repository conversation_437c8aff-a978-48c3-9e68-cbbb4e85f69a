#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار إدارة النوافذ
اختبار بسيط للتأكد من أن النوافذ تعمل بشكل صحيح
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox
import ttkbootstrap as ttk_bs
from ttkbootstrap.constants import *

# إضافة مسار src إلى Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_window_management():
    """اختبار إدارة النوافذ"""
    
    # إنشاء نافذة اختبار
    root = ttk_bs.Window(
        title="اختبار إدارة النوافذ",
        themename="cosmo",
        size=(800, 600)
    )
    
    # جعل النافذة تظهر بحجم كامل
    try:
        root.state('zoomed')  # للويندوز
    except:
        root.attributes('-zoomed', True)  # للينكس
    
    # إطار رئيسي
    main_frame = ttk_bs.Frame(root)
    main_frame.pack(fill=BOTH, expand=True, padx=10, pady=10)
    
    # عنوان
    title_label = ttk_bs.Label(
        main_frame,
        text="اختبار إدارة النوافذ",
        font=("Tahoma", 16, "bold"),
        bootstyle="primary"
    )
    title_label.pack(pady=20)
    
    # إطار المحتوى
    content_frame = ttk_bs.Frame(main_frame)
    content_frame.pack(fill=BOTH, expand=True, pady=10)
    
    # متغير لتتبع الشاشة الحالية
    current_screen = {"value": None}
    
    def clear_content_and_set_screen(screen_name):
        """مسح المحتوى وتعيين الشاشة الجديدة"""
        if current_screen["value"] == screen_name:
            print(f"الشاشة {screen_name} مفتوحة بالفعل")
            return
            
        # مسح المحتوى الحالي
        for widget in content_frame.winfo_children():
            widget.destroy()
            
        current_screen["value"] = screen_name
        print(f"تم التبديل إلى الشاشة: {screen_name}")
    
    def show_screen_1():
        """عرض الشاشة الأولى"""
        clear_content_and_set_screen("screen_1")
        
        label = ttk_bs.Label(
            content_frame,
            text="هذه هي الشاشة الأولى",
            font=("Tahoma", 14),
            bootstyle="info"
        )
        label.pack(pady=50)
    
    def show_screen_2():
        """عرض الشاشة الثانية"""
        clear_content_and_set_screen("screen_2")
        
        label = ttk_bs.Label(
            content_frame,
            text="هذه هي الشاشة الثانية",
            font=("Tahoma", 14),
            bootstyle="success"
        )
        label.pack(pady=50)
    
    def show_dashboard():
        """عرض لوحة التحكم"""
        clear_content_and_set_screen("dashboard")
        
        label = ttk_bs.Label(
            content_frame,
            text="لوحة التحكم الرئيسية",
            font=("Tahoma", 14),
            bootstyle="warning"
        )
        label.pack(pady=50)
    
    # أزرار التنقل
    buttons_frame = ttk_bs.Frame(main_frame)
    buttons_frame.pack(pady=20)
    
    btn1 = ttk_bs.Button(
        buttons_frame,
        text="الشاشة الأولى",
        command=show_screen_1,
        bootstyle="info",
        width=15
    )
    btn1.pack(side=LEFT, padx=5)
    
    btn2 = ttk_bs.Button(
        buttons_frame,
        text="الشاشة الثانية",
        command=show_screen_2,
        bootstyle="success",
        width=15
    )
    btn2.pack(side=LEFT, padx=5)
    
    btn_dashboard = ttk_bs.Button(
        buttons_frame,
        text="لوحة التحكم",
        command=show_dashboard,
        bootstyle="warning",
        width=15
    )
    btn_dashboard.pack(side=LEFT, padx=5)
    
    # عرض لوحة التحكم في البداية
    show_dashboard()
    
    # تشغيل النافذة
    root.mainloop()

if __name__ == "__main__":
    print("بدء اختبار إدارة النوافذ...")
    test_window_management()
