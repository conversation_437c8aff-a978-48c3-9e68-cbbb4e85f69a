#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
وحدة نظام التقارير العامة الكاملة مطابقة للبرنامج الأصلي
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import ttkbootstrap as ttk_bs
from ttkbootstrap.constants import *
from datetime import datetime, date
import os

class GeneralReportsCompleteModule:
    def __init__(self, parent_frame, db_manager):
        self.parent = parent_frame
        self.db_manager = db_manager
        self.current_record = None
        self.form_vars = {}
        
        # إنشاء الواجهة
        self.create_interface()
        
        # تحميل البيانات
        self.load_data()
    
    def create_interface(self):
        """إنشاء واجهة نظام التقارير العامة مطابقة للبرنامج الأصلي"""
        # مسح المحتوى السابق
        for widget in self.parent.winfo_children():
            widget.destroy()
        
        # الإطار الرئيسي
        main_frame = ttk_bs.Frame(self.parent)
        main_frame.pack(fill=BOTH, expand=True, padx=10, pady=10)
        
        # العنوان الرئيسي
        title_frame = ttk_bs.Frame(main_frame)
        title_frame.pack(fill=X, pady=(0, 10))
        
        title_label = ttk_bs.Label(
            title_frame,
            text="نظام التقارير العامة",
            font=("Tahoma", 16, "bold"),
            bootstyle="primary"
        )
        title_label.pack()
        
        # إنشاء شريط الأزرار
        self.create_buttons_bar(main_frame)
        
        # إنشاء منطقة الإدخال
        self.create_input_area(main_frame)
        
        # إنشاء جدول البيانات
        self.create_data_table(main_frame)
    
    def create_buttons_bar(self, parent):
        """إنشاء شريط الأزرار"""
        buttons_frame = ttk_bs.Frame(parent)
        buttons_frame.pack(fill=X, pady=(0, 10))
        
        # الأزرار مطابقة للبرنامج الأصلي
        buttons = [
            ("تقرير المخزون", "inventory_report", "success", "📦"),
            ("تقرير المعدات", "equipment_report", "primary", "🔧"),
            ("تقرير الأفراد", "personnel_report", "info", "👥"),
            ("تقرير العربات", "vehicles_report", "warning", "🚗"),
            ("تقرير الأسلحة", "weapons_report", "danger", "🔫"),
            ("تقرير مالي", "financial_report", "success", "💰"),
            ("تقرير إحصائي", "statistical_report", "info", "📊"),
            ("تقرير شامل", "comprehensive_report", "primary", "📋"),
            ("طباعة", "print", "secondary", "🖨️"),
            ("تصدير", "export", "secondary", "📤")
        ]
        
        for text, action, style, icon in buttons:
            btn = ttk_bs.Button(
                buttons_frame,
                text=f"{icon} {text}",
                command=lambda a=action: self.handle_button_action(a),
                bootstyle=style,
                width=14
            )
            btn.pack(side=LEFT, padx=2)
    
    def create_input_area(self, parent):
        """إنشاء منطقة الإدخال مطابقة للبرنامج الأصلي"""
        input_frame = ttk_bs.LabelFrame(parent, text="معايير التقرير العام", padding=10)
        input_frame.pack(fill=X, pady=(0, 10))
        
        # الصف الأول من الحقول
        row1_frame = ttk_bs.Frame(input_frame)
        row1_frame.pack(fill=X, pady=5)
        
        # نوع التقرير العام
        ttk_bs.Label(row1_frame, text="* نوع التقرير العام:").pack(side=RIGHT, padx=5)
        self.form_vars['general_report_type'] = ttk_bs.StringVar()
        type_combo = ttk_bs.Combobox(row1_frame, textvariable=self.form_vars['general_report_type'], width=25)
        type_combo['values'] = (
            "تقرير الحالة العامة", "تقرير الأداء الشامل", "تقرير الموارد", 
            "تقرير التشغيل", "تقرير الصيانة", "تقرير الجودة", "تقرير الأمان",
            "تقرير الكفاءة", "تقرير التكاليف", "تقرير المخاطر"
        )
        type_combo.pack(side=RIGHT, padx=5)
        
        # مستوى التقرير
        ttk_bs.Label(row1_frame, text="* مستوى التقرير:").pack(side=RIGHT, padx=5)
        self.form_vars['report_level'] = ttk_bs.StringVar()
        level_combo = ttk_bs.Combobox(row1_frame, textvariable=self.form_vars['report_level'], width=20)
        level_combo['values'] = ("إداري عليا", "إداري متوسط", "تشغيلي", "فني", "مالي")
        level_combo.pack(side=RIGHT, padx=5)
        
        # الصف الثاني من الحقول
        row2_frame = ttk_bs.Frame(input_frame)
        row2_frame.pack(fill=X, pady=5)
        
        # من تاريخ
        ttk_bs.Label(row2_frame, text="* من تاريخ:").pack(side=RIGHT, padx=5)
        self.form_vars['date_from'] = ttk_bs.StringVar()
        date_from_entry = ttk_bs.Entry(row2_frame, textvariable=self.form_vars['date_from'], width=12)
        date_from_entry.pack(side=RIGHT, padx=5)
        
        # إلى تاريخ
        ttk_bs.Label(row2_frame, text="* إلى تاريخ:").pack(side=RIGHT, padx=5)
        self.form_vars['date_to'] = ttk_bs.StringVar()
        date_to_entry = ttk_bs.Entry(row2_frame, textvariable=self.form_vars['date_to'], width=12)
        date_to_entry.pack(side=RIGHT, padx=5)
        
        # نطاق التقرير
        ttk_bs.Label(row2_frame, text="نطاق التقرير:").pack(side=RIGHT, padx=5)
        self.form_vars['report_scope'] = ttk_bs.StringVar()
        scope_combo = ttk_bs.Combobox(row2_frame, textvariable=self.form_vars['report_scope'], width=20)
        scope_combo['values'] = ("جميع الأقسام", "قسم محدد", "وحدة محددة", "مشروع محدد", "نشاط محدد")
        scope_combo.pack(side=RIGHT, padx=5)
        
        # الصف الثالث من الحقول
        row3_frame = ttk_bs.Frame(input_frame)
        row3_frame.pack(fill=X, pady=5)
        
        # تضمين المخزون
        self.form_vars['include_inventory'] = ttk_bs.BooleanVar()
        inventory_check = ttk_bs.Checkbutton(
            row3_frame, 
            text="تضمين بيانات المخزون", 
            variable=self.form_vars['include_inventory']
        )
        inventory_check.pack(side=RIGHT, padx=5)
        
        # تضمين المعدات
        self.form_vars['include_equipment'] = ttk_bs.BooleanVar()
        equipment_check = ttk_bs.Checkbutton(
            row3_frame, 
            text="تضمين بيانات المعدات", 
            variable=self.form_vars['include_equipment']
        )
        equipment_check.pack(side=RIGHT, padx=5)
        
        # تضمين الأفراد
        self.form_vars['include_personnel'] = ttk_bs.BooleanVar()
        personnel_check = ttk_bs.Checkbutton(
            row3_frame, 
            text="تضمين بيانات الأفراد", 
            variable=self.form_vars['include_personnel']
        )
        personnel_check.pack(side=RIGHT, padx=5)
        
        # الصف الرابع من الحقول
        row4_frame = ttk_bs.Frame(input_frame)
        row4_frame.pack(fill=X, pady=5)
        
        # تضمين العربات
        self.form_vars['include_vehicles'] = ttk_bs.BooleanVar()
        vehicles_check = ttk_bs.Checkbutton(
            row4_frame, 
            text="تضمين بيانات العربات", 
            variable=self.form_vars['include_vehicles']
        )
        vehicles_check.pack(side=RIGHT, padx=5)
        
        # تضمين الأسلحة
        self.form_vars['include_weapons'] = ttk_bs.BooleanVar()
        weapons_check = ttk_bs.Checkbutton(
            row4_frame, 
            text="تضمين بيانات الأسلحة", 
            variable=self.form_vars['include_weapons']
        )
        weapons_check.pack(side=RIGHT, padx=5)
        
        # تضمين البيانات المالية
        self.form_vars['include_financial'] = ttk_bs.BooleanVar()
        financial_check = ttk_bs.Checkbutton(
            row4_frame, 
            text="تضمين البيانات المالية", 
            variable=self.form_vars['include_financial']
        )
        financial_check.pack(side=RIGHT, padx=5)
        
        # الصف الخامس من الحقول
        row5_frame = ttk_bs.Frame(input_frame)
        row5_frame.pack(fill=X, pady=5)
        
        # تضمين الإحصائيات
        self.form_vars['include_statistics'] = ttk_bs.BooleanVar()
        statistics_check = ttk_bs.Checkbutton(
            row5_frame, 
            text="تضمين الإحصائيات", 
            variable=self.form_vars['include_statistics']
        )
        statistics_check.pack(side=RIGHT, padx=5)
        
        # تضمين الرسوم البيانية
        self.form_vars['include_charts'] = ttk_bs.BooleanVar()
        charts_check = ttk_bs.Checkbutton(
            row5_frame, 
            text="تضمين الرسوم البيانية", 
            variable=self.form_vars['include_charts']
        )
        charts_check.pack(side=RIGHT, padx=5)
        
        # تضمين التوصيات
        self.form_vars['include_recommendations'] = ttk_bs.BooleanVar()
        recommendations_check = ttk_bs.Checkbutton(
            row5_frame, 
            text="تضمين التوصيات", 
            variable=self.form_vars['include_recommendations']
        )
        recommendations_check.pack(side=RIGHT, padx=5)
        
        # الصف السادس من الحقول
        row6_frame = ttk_bs.Frame(input_frame)
        row6_frame.pack(fill=X, pady=5)
        
        # تنسيق التقرير
        ttk_bs.Label(row6_frame, text="تنسيق التقرير:").pack(side=RIGHT, padx=5)
        self.form_vars['format'] = ttk_bs.StringVar()
        format_combo = ttk_bs.Combobox(row6_frame, textvariable=self.form_vars['format'], width=15)
        format_combo['values'] = ("PDF", "Excel", "Word", "PowerPoint", "HTML")
        format_combo.pack(side=RIGHT, padx=5)
        
        # اللغة
        ttk_bs.Label(row6_frame, text="لغة التقرير:").pack(side=RIGHT, padx=5)
        self.form_vars['language'] = ttk_bs.StringVar()
        language_combo = ttk_bs.Combobox(row6_frame, textvariable=self.form_vars['language'], width=15)
        language_combo['values'] = ("العربية", "الإنجليزية", "ثنائي اللغة")
        language_combo.pack(side=RIGHT, padx=5)
        
        # الصف السابع من الحقول
        row7_frame = ttk_bs.Frame(input_frame)
        row7_frame.pack(fill=X, pady=5)
        
        # مستوى السرية
        ttk_bs.Label(row7_frame, text="مستوى السرية:").pack(side=RIGHT, padx=5)
        self.form_vars['confidentiality'] = ttk_bs.StringVar()
        confidentiality_combo = ttk_bs.Combobox(row7_frame, textvariable=self.form_vars['confidentiality'], width=15)
        confidentiality_combo['values'] = ("عام", "محدود", "سري", "سري جداً")
        confidentiality_combo.pack(side=RIGHT, padx=5)
        
        # طريقة التوزيع
        ttk_bs.Label(row7_frame, text="طريقة التوزيع:").pack(side=RIGHT, padx=5)
        self.form_vars['distribution'] = ttk_bs.StringVar()
        distribution_combo = ttk_bs.Combobox(row7_frame, textvariable=self.form_vars['distribution'], width=20)
        distribution_combo['values'] = ("طباعة", "بريد إلكتروني", "نظام إلكتروني", "يدوي", "مختلط")
        distribution_combo.pack(side=RIGHT, padx=5)
        
        # الصف الثامن من الحقول
        row8_frame = ttk_bs.Frame(input_frame)
        row8_frame.pack(fill=X, pady=5)
        
        # المعد بواسطة
        ttk_bs.Label(row8_frame, text="المعد بواسطة:").pack(side=RIGHT, padx=5)
        self.form_vars['prepared_by'] = ttk_bs.StringVar()
        prepared_combo = ttk_bs.Combobox(row8_frame, textvariable=self.form_vars['prepared_by'], width=25)
        prepared_combo.pack(side=RIGHT, padx=5)
        
        # المراجع بواسطة
        ttk_bs.Label(row8_frame, text="المراجع بواسطة:").pack(side=RIGHT, padx=5)
        self.form_vars['reviewed_by'] = ttk_bs.StringVar()
        reviewed_combo = ttk_bs.Combobox(row8_frame, textvariable=self.form_vars['reviewed_by'], width=25)
        reviewed_combo.pack(side=RIGHT, padx=5)
        
        # الصف التاسع من الحقول
        row9_frame = ttk_bs.Frame(input_frame)
        row9_frame.pack(fill=X, pady=5)
        
        # المعتمد بواسطة
        ttk_bs.Label(row9_frame, text="المعتمد بواسطة:").pack(side=RIGHT, padx=5)
        self.form_vars['approved_by'] = ttk_bs.StringVar()
        approved_combo = ttk_bs.Combobox(row9_frame, textvariable=self.form_vars['approved_by'], width=25)
        approved_combo.pack(side=RIGHT, padx=5)
        
        # أولوية التقرير
        ttk_bs.Label(row9_frame, text="أولوية التقرير:").pack(side=RIGHT, padx=5)
        self.form_vars['priority'] = ttk_bs.StringVar()
        priority_combo = ttk_bs.Combobox(row9_frame, textvariable=self.form_vars['priority'], width=15)
        priority_combo['values'] = ("عادي", "مهم", "عاجل", "عاجل جداً")
        priority_combo.pack(side=RIGHT, padx=5)
        
        # الصف العاشر - الملاحظات
        row10_frame = ttk_bs.Frame(input_frame)
        row10_frame.pack(fill=X, pady=5)
        
        ttk_bs.Label(row10_frame, text="ملاحظات التقرير:").pack(side=RIGHT, padx=5)
        self.form_vars['notes'] = ttk_bs.StringVar()
        notes_entry = ttk_bs.Entry(row10_frame, textvariable=self.form_vars['notes'], width=60)
        notes_entry.pack(side=RIGHT, padx=5, fill=X, expand=True)
        
        # تحميل البيانات في القوائم المنسدلة
        self.load_combo_data(prepared_combo, reviewed_combo, approved_combo)
    
    def create_data_table(self, parent):
        """إنشاء جدول البيانات"""
        table_frame = ttk_bs.LabelFrame(parent, text="قائمة التقارير العامة", padding=10)
        table_frame.pack(fill=BOTH, expand=True)
        
        # إنشاء Treeview
        columns = ("الرقم", "نوع التقرير", "مستوى التقرير", "النطاق", "من تاريخ", "إلى تاريخ", "المعد بواسطة", "الأولوية")
        self.tree = ttk_bs.Treeview(table_frame, columns=columns, show="headings", height=8)
        
        # تعيين عناوين الأعمدة
        for col in columns:
            self.tree.heading(col, text=col)
            self.tree.column(col, width=120, anchor=CENTER)
        
        # شريط التمرير
        scrollbar = ttk_bs.Scrollbar(table_frame, orient=VERTICAL, command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)
        
        # تخطيط الجدول
        self.tree.pack(side=LEFT, fill=BOTH, expand=True)
        scrollbar.pack(side=RIGHT, fill=Y)
        
        # ربط حدث النقر
        self.tree.bind("<<TreeviewSelect>>", self.on_item_select)
    
    def load_combo_data(self, prepared_combo, reviewed_combo, approved_combo):
        """تحميل بيانات القوائم المنسدلة"""
        try:
            # تحميل الأفراد
            personnel = ["أحمد محمد السالم", "سالم علي الأحمد", "خالد حسن المحمد", "محمد عبدالله الزهراني"]
            prepared_combo['values'] = personnel
            reviewed_combo['values'] = personnel
            approved_combo['values'] = personnel
            
        except Exception as e:
            print(f"خطأ في تحميل بيانات القوائم: {e}")
    
    def handle_button_action(self, action):
        """معالجة أحداث الأزرار"""
        if action == "inventory_report":
            self.generate_inventory_report()
        elif action == "equipment_report":
            self.generate_equipment_report()
        elif action == "personnel_report":
            self.generate_personnel_report()
        elif action == "vehicles_report":
            self.generate_vehicles_report()
        elif action == "weapons_report":
            self.generate_weapons_report()
        elif action == "financial_report":
            self.generate_financial_report()
        elif action == "statistical_report":
            self.generate_statistical_report()
        elif action == "comprehensive_report":
            self.generate_comprehensive_report()
        elif action == "print":
            self.print_report()
        elif action == "export":
            self.export_report()
    
    def load_data(self):
        """تحميل البيانات في الجدول"""
        try:
            # مسح البيانات الحالية
            for item in self.tree.get_children():
                self.tree.delete(item)
            
            # بيانات تجريبية للتقارير العامة
            sample_reports = [
                ("1", "تقرير الحالة العامة", "إداري عليا", "جميع الأقسام", "2024-01-01", "2024-01-31", "أحمد محمد", "مهم"),
                ("2", "تقرير الأداء الشامل", "إداري متوسط", "قسم محدد", "2024-01-15", "2024-02-15", "سالم علي", "عادي"),
                ("3", "تقرير الموارد", "تشغيلي", "وحدة محددة", "2024-02-01", "2024-02-28", "خالد حسن", "عاجل")
            ]
            
            for report in sample_reports:
                self.tree.insert("", "end", values=report)
                
        except Exception as e:
            print(f"خطأ في تحميل البيانات: {e}")
    
    def on_item_select(self, event):
        """معالجة تحديد عنصر في الجدول"""
        selection = self.tree.selection()
        if selection:
            item = self.tree.item(selection[0])
            values = item['values']
            
            # تحديث الحقول بالبيانات المحددة
            if len(values) >= 8:
                self.form_vars['general_report_type'].set(values[1])
                self.form_vars['report_level'].set(values[2])
                self.form_vars['report_scope'].set(values[3])
                self.form_vars['date_from'].set(values[4])
                self.form_vars['date_to'].set(values[5])
                self.form_vars['prepared_by'].set(values[6])
                self.form_vars['priority'].set(values[7])
    
    def generate_inventory_report(self):
        """إنتاج تقرير المخزون"""
        messagebox.showinfo("تقرير المخزون", "سيتم إنتاج تقرير المخزون العام")
    
    def generate_equipment_report(self):
        """إنتاج تقرير المعدات"""
        messagebox.showinfo("تقرير المعدات", "سيتم إنتاج تقرير المعدات العام")
    
    def generate_personnel_report(self):
        """إنتاج تقرير الأفراد"""
        messagebox.showinfo("تقرير الأفراد", "سيتم إنتاج تقرير الأفراد العام")
    
    def generate_vehicles_report(self):
        """إنتاج تقرير العربات"""
        messagebox.showinfo("تقرير العربات", "سيتم إنتاج تقرير العربات العام")
    
    def generate_weapons_report(self):
        """إنتاج تقرير الأسلحة"""
        messagebox.showinfo("تقرير الأسلحة", "سيتم إنتاج تقرير الأسلحة العام")
    
    def generate_financial_report(self):
        """إنتاج تقرير مالي"""
        messagebox.showinfo("تقرير مالي", "سيتم إنتاج التقرير المالي العام")
    
    def generate_statistical_report(self):
        """إنتاج تقرير إحصائي"""
        messagebox.showinfo("تقرير إحصائي", "سيتم إنتاج التقرير الإحصائي العام")
    
    def generate_comprehensive_report(self):
        """إنتاج تقرير شامل"""
        messagebox.showinfo("تقرير شامل", "سيتم إنتاج التقرير الشامل العام")
    
    def print_report(self):
        """طباعة التقرير"""
        messagebox.showinfo("طباعة", "سيتم تطوير وظيفة الطباعة قريباً")
    
    def export_report(self):
        """تصدير التقرير"""
        messagebox.showinfo("تصدير", "سيتم تطوير وظيفة التصدير قريباً")
    
    def clear_form(self):
        """مسح النموذج"""
        for var in self.form_vars.values():
            if hasattr(var, 'set'):
                var.set("")
