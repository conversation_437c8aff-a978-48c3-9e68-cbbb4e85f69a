#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار التصميم الجديد للوحة التحكم
"""

import sys
import os

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_new_dashboard_design():
    """اختبار التصميم الجديد للوحة التحكم"""
    print("=" * 60)
    print("اختبار التصميم الجديد للوحة التحكم")
    print("=" * 60)
    
    try:
        # استيراد المكونات
        from src.ui.main_window import MainWindow
        from src.database.database_manager import DatabaseManager
        from src.utils.config import Config
        from src.utils.logger import Logger
        
        print("✓ تم استيراد جميع المكونات بنجاح")
        
        # تهيئة قاعدة البيانات
        db_manager = DatabaseManager()
        print("✓ تم تهيئة قاعدة البيانات")
        
        # بيانات مستخدم تجريبية
        user_data = {
            'id': 1,
            'username': 'admin',
            'full_name': 'مدير النظام',
            'role': 'مدير'
        }
        
        print("✓ تم إعداد بيانات المستخدم التجريبية")
        
        # إنشاء النافذة الرئيسية
        print("\n🚀 تشغيل النافذة الرئيسية مع التصميم الجديد...")
        print("📋 المميزات الجديدة:")
        print("   • شريط أدوات محدث مع ألوان زرقاء")
        print("   • لوحة تحكم رئيسية مطابقة للصورة")
        print("   • إحصائيات ملونة مع أيقونات")
        print("   • أقسام سفلية للنشاطات والتحديثات")
        print("   • شريط حالة محسن مع معلومات النظام")
        print("   • تصميم عربي محسن مع RTL")
        
        # تشغيل النافذة
        main_window = MainWindow(user_data, db_manager)
        main_window.run()
        
        print("✓ تم تشغيل النظام بنجاح")
        
    except Exception as e:
        print(f"❌ خطأ في اختبار التصميم: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

if __name__ == "__main__":
    print("بدء اختبار التصميم الجديد...")
    success = test_new_dashboard_design()
    
    if success:
        print("\n" + "=" * 60)
        print("✅ تم اختبار التصميم الجديد بنجاح!")
        print("🎨 التصميم الآن مطابق للصورة المطلوبة")
        print("=" * 60)
    else:
        print("\n" + "=" * 60)
        print("❌ فشل في اختبار التصميم الجديد")
        print("=" * 60)
