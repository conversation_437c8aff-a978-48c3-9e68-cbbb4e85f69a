# تحسينات إدارة النوافذ

## المشاكل التي تم حلها:

### 1. مشكلة فتح لوحة التحكم أكثر من مرة
- **المشكلة**: كانت دالة `show_dashboard()` لا تقوم بمسح المحتوى السابق قبل إنشاء المحتوى الجديد
- **الحل**: تم إضافة مسح المحتوى السابق في بداية الدالة

### 2. مشكلة عدم اختفاء الشاشة السابقة عند فتح شاشة جديدة
- **المشكلة**: بعض الدوال لم تكن تقوم بمسح المحتوى السابق
- **الحل**: تم إضافة مسح المحتوى السابق في جميع دوال عرض الشاشات

### 3. مشكلة عدم ظهور النوافذ بحجم كامل
- **المشكلة**: النوافذ كانت تظهر بحجم محدود
- **الحل**: تم إضافة `root.state('zoomed')` لجعل النافذة تظهر بحجم كامل

## التحسينات المضافة:

### 1. دالة مساعدة لإدارة النوافذ
```python
def clear_content_and_set_screen(self, screen_name: str) -> None:
    """مسح المحتوى الحالي وتعيين الشاشة الجديدة"""
    # تجنب إعادة تحميل نفس الشاشة
    if self.current_screen == screen_name:
        return
        
    # مسح المحتوى الحالي
    for widget in self.content_frame.winfo_children():
        widget.destroy()
        
    # تحديث الشاشة الحالية
    self.current_screen = screen_name
```

### 2. متغير لتتبع الشاشة الحالية
- تم إضافة `self.current_screen = None` لتتبع الشاشة المفتوحة حالياً
- يمنع إعادة تحميل نفس الشاشة إذا كانت مفتوحة بالفعل

### 3. تحديث جميع دوال عرض الشاشات
تم تحديث الدوال التالية لاستخدام الدالة المساعدة الجديدة:
- `show_dashboard()`
- `show_basic_info_complete()`
- `show_equipment_complete()`
- `show_categories_complete()`
- `show_users_complete()`
- `show_backup_complete()`
- `show_reports_system_complete()`
- `show_general_reports_complete()`
- `show_comprehensive_search_complete()`
- `show_units_complete()`
- `show_personal_data_complete()`
- `show_ranks_complete()`
- `show_job_titles_complete()`

### 4. تحسين إعدادات النافذة
```python
def setup_window(self) -> None:
    """إعداد النافذة الرئيسية"""
    # تعيين العنوان
    self.root.title("نظام إدارة التموين العام والقوة العمومية")
    
    # تعيين الحد الأدنى للحجم
    self.root.minsize(1200, 700)
    
    # جعل النافذة تظهر بحجم كامل
    self.root.state('zoomed')  # للويندوز
    
    # باقي الإعدادات...
```

## النتائج المتوقعة:

1. **لوحة التحكم تفتح مرة واحدة فقط**: لن تتكرر العناصر عند الضغط على زر لوحة التحكم مرة أخرى
2. **انتقال سلس بين الشاشات**: عند فتح شاشة جديدة، تختفي الشاشة السابقة تماماً وتظهر الجديدة
3. **عرض بحجم كامل**: جميع الشاشات تظهر بحجم كامل على الشاشة
4. **أداء محسن**: تجنب إعادة تحميل نفس الشاشة يحسن الأداء

## ملف الاختبار:
تم إنشاء ملف `test_window_management.py` لاختبار التحسينات الجديدة.

## كيفية الاختبار:
1. تشغيل النظام الرئيسي
2. الضغط على زر "لوحة التحكم" عدة مرات والتأكد من عدم تكرار المحتوى
3. التنقل بين الشاشات المختلفة والتأكد من اختفاء الشاشة السابقة
4. التأكد من ظهور النوافذ بحجم كامل
