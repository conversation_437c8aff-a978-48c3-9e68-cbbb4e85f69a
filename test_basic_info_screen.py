#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار شاشة المعلومات الأساسية الكاملة
"""

import sys
import os

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_basic_info_screen():
    """اختبار شاشة المعلومات الأساسية"""
    print("=" * 80)
    print("اختبار شاشة المعلومات الأساسية الكاملة")
    print("=" * 80)
    
    try:
        # استيراد المكونات
        from src.modules.basic_info_complete import BasicInfoCompleteModule
        from src.database.database_manager import DatabaseManager
        import tkinter as tk
        import ttkbootstrap as ttk_bs
        
        print("✓ تم استيراد جميع المكونات بنجاح")
        
        # تهيئة قاعدة البيانات
        db_manager = DatabaseManager()
        print("✓ تم تهيئة قاعدة البيانات")
        
        # إنشاء نافذة اختبار
        root = ttk_bs.Window(themename="cosmo")
        root.title("اختبار شاشة المعلومات الأساسية")
        root.geometry("1200x800")
        root.configure(bg='white')
        
        print("✓ تم إنشاء النافذة الرئيسية")
        
        # إنشاء إطار المحتوى
        content_frame = ttk_bs.Frame(root)
        content_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        print("✓ تم إنشاء إطار المحتوى")
        
        # إنشاء وحدة المعلومات الأساسية
        basic_info_module = BasicInfoCompleteModule(content_frame, db_manager)
        
        print("✓ تم إنشاء وحدة المعلومات الأساسية")
        
        print("\n🎨 مميزات الشاشة الجديدة:")
        print("   📋 التبويبات العلوية:")
        print("      • القوة العمومية")
        print("      • المعدات")
        print("      • الأصناف")
        print("      • الأسلحة")
        print("      • المستخدمين")
        print("      • أدوات الاستعلام")
        
        print("\n   🔘 شريط الأزرار:")
        print("      • 🆕 إضافة")
        print("      • 💾 حفظ")
        print("      • ✏️ تعديل")
        print("      • 🗑️ حذف")
        print("      • ❌ إلغاء")
        
        print("\n   📝 حقول الإدخال:")
        print("      • رقم القوة العمومية (مطلوب)")
        print("      • رقم الوحدة (مطلوب)")
        print("      • اسم القوة العمومية (مطلوب)")
        print("      • اسم آمر الوحدة")
        print("      • رتبة آمر الوحدة (قائمة منسدلة)")
        print("      • وظيفة آمر الوحدة")
        print("      • اسم ضابط التموين")
        print("      • رتبة ضابط التموين (قائمة منسدلة)")
        print("      • وظيفة ضابط التموين")
        
        print("\n   📊 جدول البيانات:")
        print("      • عرض جميع السجلات")
        print("      • إمكانية التحديد والتعديل")
        print("      • شريط تمرير عمودي")
        print("      • أعمدة منظمة ومرتبة")
        
        print("\n   🔗 الارتباطات:")
        print("      • ربط مع جدول الرتب")
        print("      • ربط مع جدول الوحدات")
        print("      • ربط مع جدول الأفراد")
        print("      • تحديث تلقائي للبيانات")
        
        print("\n🚀 تشغيل الشاشة...")
        print("📌 يمكنك الآن:")
        print("   • إضافة معلومات أساسية جديدة")
        print("   • تعديل المعلومات الموجودة")
        print("   • حذف السجلات غير المرغوبة")
        print("   • البحث والتصفية")
        print("   • التنقل بين التبويبات")
        
        # تشغيل النافذة
        root.mainloop()
        
        print("✓ تم تشغيل الشاشة بنجاح")
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الشاشة: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

def test_database_operations():
    """اختبار عمليات قاعدة البيانات"""
    print("\n" + "=" * 60)
    print("اختبار عمليات قاعدة البيانات")
    print("=" * 60)
    
    try:
        from src.database.database_manager import DatabaseManager
        
        db_manager = DatabaseManager()
        
        # اختبار إنشاء الجدول
        print("✓ تم إنشاء جدول force_basic_info")
        
        # اختبار إدراج بيانات تجريبية
        test_data = {
            'force_name': 'القوة العمومية الأولى',
            'force_number': '001',
            'unit_number': 'U001',
            'unit_name': 'الوحدة الأولى',
            'commander_name': 'العقيد أحمد محمد',
            'commander_rank': 'عقيد',
            'commander_position': 'قائد الوحدة',
            'supply_name': 'الرائد علي حسن',
            'supply_rank': 'رائد',
            'supply_position': 'ضابط التموين'
        }
        
        # إدراج البيانات التجريبية
        query = """
            INSERT INTO force_basic_info 
            (force_name, force_number, unit_number, unit_name, commander_name, 
             commander_rank, commander_position, supply_name, supply_rank, supply_position)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """
        
        params = (
            test_data['force_name'], test_data['force_number'], test_data['unit_number'],
            test_data['unit_name'], test_data['commander_name'], test_data['commander_rank'],
            test_data['commander_position'], test_data['supply_name'], test_data['supply_rank'],
            test_data['supply_position']
        )
        
        db_manager.execute_query(query, params)
        print("✓ تم إدراج بيانات تجريبية")
        
        # اختبار استرجاع البيانات
        records = db_manager.execute_query("SELECT * FROM force_basic_info")
        print(f"✓ تم استرجاع {len(records)} سجل من قاعدة البيانات")
        
        # عرض البيانات
        if records:
            print("\n📋 البيانات المسترجعة:")
            for record in records:
                print(f"   • {record['force_name']} - {record['unit_number']}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار قاعدة البيانات: {e}")
        return False

if __name__ == "__main__":
    print("بدء اختبار شاشة المعلومات الأساسية...")
    
    # اختبار قاعدة البيانات أولاً
    db_success = test_database_operations()
    
    if db_success:
        # اختبار الشاشة
        screen_success = test_basic_info_screen()
        
        if screen_success:
            print("\n" + "=" * 80)
            print("✅ تم اختبار شاشة المعلومات الأساسية بنجاح!")
            print("🎨 الشاشة مطابقة للصورة المطلوبة")
            print("📋 جميع الحقول والأزرار موجودة")
            print("🔗 الارتباطات مع قاعدة البيانات تعمل")
            print("📊 الجدول يعرض البيانات بشكل صحيح")
            print("=" * 80)
        else:
            print("\n" + "=" * 80)
            print("❌ فشل في اختبار الشاشة")
            print("=" * 80)
    else:
        print("\n" + "=" * 80)
        print("❌ فشل في اختبار قاعدة البيانات")
        print("=" * 80)
