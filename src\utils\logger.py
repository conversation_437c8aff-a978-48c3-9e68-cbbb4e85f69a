#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
وحدة إدارة السجلات
"""

import os
import logging
import logging.handlers
from datetime import datetime
from typing import Optional


class Logger:
    """فئة إدارة السجلات"""
    
    def __init__(self, name: str = "SupplyManagement", log_file: str = "logs/app.log"):
        """
        تهيئة نظام السجلات
        
        Args:
            name: اسم السجل
            log_file: مسار ملف السجل
        """
        self.name = name
        self.log_file = log_file
        self.logger = None
        self._setup_logger()
    
    def _setup_logger(self) -> None:
        """إعداد نظام السجلات"""
        try:
            # إنشاء مجلد السجلات إذا لم يكن موجوداً
            log_dir = os.path.dirname(self.log_file)
            if log_dir:
                os.makedirs(log_dir, exist_ok=True)
            
            # إنشاء السجل
            self.logger = logging.getLogger(self.name)
            self.logger.setLevel(logging.INFO)
            
            # تجنب إضافة معالجات متعددة
            if not self.logger.handlers:
                # معالج الملف مع التدوير
                file_handler = logging.handlers.RotatingFileHandler(
                    self.log_file,
                    maxBytes=10*1024*1024,  # 10 MB
                    backupCount=5,
                    encoding='utf-8'
                )
                file_handler.setLevel(logging.INFO)
                
                # معالج وحدة التحكم
                console_handler = logging.StreamHandler()
                console_handler.setLevel(logging.WARNING)
                
                # تنسيق السجلات
                formatter = logging.Formatter(
                    '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                    datefmt='%Y-%m-%d %H:%M:%S'
                )
                
                file_handler.setFormatter(formatter)
                console_handler.setFormatter(formatter)
                
                # إضافة المعالجات
                self.logger.addHandler(file_handler)
                self.logger.addHandler(console_handler)
                
        except Exception as e:
            print(f"خطأ في إعداد نظام السجلات: {e}")
            # إنشاء سجل بسيط في حالة الفشل
            self.logger = logging.getLogger(self.name)
            self.logger.setLevel(logging.INFO)
    
    def info(self, message: str, extra_data: Optional[dict] = None) -> None:
        """تسجيل رسالة معلومات"""
        try:
            if extra_data:
                message = f"{message} - البيانات الإضافية: {extra_data}"
            self.logger.info(message)
        except Exception as e:
            print(f"خطأ في تسجيل رسالة المعلومات: {e}")
    
    def warning(self, message: str, extra_data: Optional[dict] = None) -> None:
        """تسجيل رسالة تحذير"""
        try:
            if extra_data:
                message = f"{message} - البيانات الإضافية: {extra_data}"
            self.logger.warning(message)
        except Exception as e:
            print(f"خطأ في تسجيل رسالة التحذير: {e}")
    
    def error(self, message: str, extra_data: Optional[dict] = None) -> None:
        """تسجيل رسالة خطأ"""
        try:
            if extra_data:
                message = f"{message} - البيانات الإضافية: {extra_data}"
            self.logger.error(message)
        except Exception as e:
            print(f"خطأ في تسجيل رسالة الخطأ: {e}")
    
    def debug(self, message: str, extra_data: Optional[dict] = None) -> None:
        """تسجيل رسالة تصحيح"""
        try:
            if extra_data:
                message = f"{message} - البيانات الإضافية: {extra_data}"
            self.logger.debug(message)
        except Exception as e:
            print(f"خطأ في تسجيل رسالة التصحيح: {e}")
    
    def critical(self, message: str, extra_data: Optional[dict] = None) -> None:
        """تسجيل رسالة حرجة"""
        try:
            if extra_data:
                message = f"{message} - البيانات الإضافية: {extra_data}"
            self.logger.critical(message)
        except Exception as e:
            print(f"خطأ في تسجيل رسالة حرجة: {e}")
    
    def log_user_action(self, user_id: str, action: str, details: str = "") -> None:
        """تسجيل عمل المستخدم"""
        try:
            timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            message = f"المستخدم {user_id} - العمل: {action}"
            if details:
                message += f" - التفاصيل: {details}"
            
            self.info(f"[عمل المستخدم] {message}")
        except Exception as e:
            print(f"خطأ في تسجيل عمل المستخدم: {e}")
    
    def log_database_operation(self, operation: str, table: str, details: str = "") -> None:
        """تسجيل عملية قاعدة البيانات"""
        try:
            message = f"عملية قاعدة البيانات - {operation} في الجدول {table}"
            if details:
                message += f" - التفاصيل: {details}"
            
            self.info(f"[قاعدة البيانات] {message}")
        except Exception as e:
            print(f"خطأ في تسجيل عملية قاعدة البيانات: {e}")
    
    def log_system_event(self, event: str, details: str = "") -> None:
        """تسجيل حدث النظام"""
        try:
            message = f"حدث النظام - {event}"
            if details:
                message += f" - التفاصيل: {details}"
            
            self.info(f"[النظام] {message}")
        except Exception as e:
            print(f"خطأ في تسجيل حدث النظام: {e}")
    
    def log_error_with_traceback(self, message: str, exception: Exception) -> None:
        """تسجيل خطأ مع تتبع المكدس"""
        try:
            import traceback
            error_details = traceback.format_exc()
            full_message = f"{message}\nتفاصيل الخطأ:\n{error_details}"
            self.error(full_message)
        except Exception as e:
            print(f"خطأ في تسجيل الخطأ مع التتبع: {e}")
    
    def set_level(self, level: str) -> None:
        """تعيين مستوى السجل"""
        try:
            level_map = {
                'DEBUG': logging.DEBUG,
                'INFO': logging.INFO,
                'WARNING': logging.WARNING,
                'ERROR': logging.ERROR,
                'CRITICAL': logging.CRITICAL
            }
            
            if level.upper() in level_map:
                self.logger.setLevel(level_map[level.upper()])
            else:
                self.warning(f"مستوى سجل غير صحيح: {level}")
        except Exception as e:
            print(f"خطأ في تعيين مستوى السجل: {e}")
    
    def clear_logs(self) -> bool:
        """مسح ملفات السجلات"""
        try:
            if os.path.exists(self.log_file):
                os.remove(self.log_file)
                
                # مسح ملفات النسخ الاحتياطية
                log_dir = os.path.dirname(self.log_file)
                log_name = os.path.basename(self.log_file)
                
                for i in range(1, 6):  # حتى 5 نسخ احتياطية
                    backup_file = os.path.join(log_dir, f"{log_name}.{i}")
                    if os.path.exists(backup_file):
                        os.remove(backup_file)
                
                self.info("تم مسح ملفات السجلات")
                return True
            return False
        except Exception as e:
            print(f"خطأ في مسح ملفات السجلات: {e}")
            return False
    
    def get_log_size(self) -> int:
        """الحصول على حجم ملف السجل بالبايت"""
        try:
            if os.path.exists(self.log_file):
                return os.path.getsize(self.log_file)
            return 0
        except Exception:
            return 0
    
    def get_recent_logs(self, lines: int = 100) -> list:
        """الحصول على آخر سجلات"""
        try:
            if not os.path.exists(self.log_file):
                return []
            
            with open(self.log_file, 'r', encoding='utf-8') as f:
                all_lines = f.readlines()
                return all_lines[-lines:] if len(all_lines) > lines else all_lines
        except Exception as e:
            print(f"خطأ في قراءة السجلات الأخيرة: {e}")
            return []
