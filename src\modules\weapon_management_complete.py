#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
وحدة إدارة السلاح الكاملة مطابقة للبرنامج الأصلي
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import ttkbootstrap as ttk_bs
from ttkbootstrap.constants import *
from datetime import datetime

class WeaponManagementCompleteModule:
    def __init__(self, parent_frame, db_manager):
        self.parent = parent_frame
        self.db_manager = db_manager
        self.current_record = None
        self.form_vars = {}
        
        # إنشاء الواجهة
        self.create_interface()
        
        # تحميل البيانات
        self.load_data()
    
    def create_interface(self):
        """إنشاء واجهة إدارة السلاح مطابقة للبرنامج الأصلي"""
        # مسح المحتوى السابق
        for widget in self.parent.winfo_children():
            widget.destroy()
        
        # الإطار الرئيسي
        main_frame = ttk_bs.Frame(self.parent)
        main_frame.pack(fill=BOTH, expand=True, padx=10, pady=10)
        
        # العنوان الرئيسي
        title_frame = ttk_bs.Frame(main_frame)
        title_frame.pack(fill=X, pady=(0, 10))
        
        title_label = ttk_bs.Label(
            title_frame,
            text="إدارة السلاح",
            font=("Tahoma", 16, "bold"),
            bootstyle="primary"
        )
        title_label.pack()
        
        # إنشاء شريط الأزرار
        self.create_buttons_bar(main_frame)
        
        # إنشاء منطقة الإدخال
        self.create_input_area(main_frame)
        
        # إنشاء جدول البيانات
        self.create_data_table(main_frame)
    
    def create_buttons_bar(self, parent):
        """إنشاء شريط الأزرار"""
        buttons_frame = ttk_bs.Frame(parent)
        buttons_frame.pack(fill=X, pady=(0, 10))
        
        # الأزرار مطابقة للبرنامج الأصلي
        buttons = [
            ("إضافة", "add", "success", "🆕"),
            ("حفظ", "save", "primary", "💾"),
            ("تعديل", "edit", "warning", "✏️"),
            ("حذف", "delete", "danger", "🗑️"),
            ("إلغاء", "cancel", "secondary", "❌"),
            ("طباعة", "print", "info", "🖨️"),
            ("تصدير", "export", "info", "📤"),
            ("جرد", "inventory", "warning", "📋")
        ]
        
        for text, action, style, icon in buttons:
            btn = ttk_bs.Button(
                buttons_frame,
                text=f"{icon} {text}",
                command=lambda a=action: self.handle_button_action(a),
                bootstyle=style,
                width=12
            )
            btn.pack(side=LEFT, padx=2)
    
    def create_input_area(self, parent):
        """إنشاء منطقة الإدخال مطابقة للبرنامج الأصلي"""
        input_frame = ttk_bs.LabelFrame(parent, text="بيانات السلاح", padding=10)
        input_frame.pack(fill=X, pady=(0, 10))
        
        # الصف الأول من الحقول
        row1_frame = ttk_bs.Frame(input_frame)
        row1_frame.pack(fill=X, pady=5)
        
        # رقم السلاح
        ttk_bs.Label(row1_frame, text="* رقم السلاح:").pack(side=RIGHT, padx=5)
        self.form_vars['weapon_number'] = ttk_bs.StringVar()
        weapon_number_entry = ttk_bs.Entry(row1_frame, textvariable=self.form_vars['weapon_number'], width=15)
        weapon_number_entry.pack(side=RIGHT, padx=5)
        
        # الرقم التسلسلي
        ttk_bs.Label(row1_frame, text="* الرقم التسلسلي:").pack(side=RIGHT, padx=5)
        self.form_vars['serial_number'] = ttk_bs.StringVar()
        serial_number_entry = ttk_bs.Entry(row1_frame, textvariable=self.form_vars['serial_number'], width=20)
        serial_number_entry.pack(side=RIGHT, padx=5)
        
        # الصف الثاني من الحقول
        row2_frame = ttk_bs.Frame(input_frame)
        row2_frame.pack(fill=X, pady=5)
        
        # نوع السلاح
        ttk_bs.Label(row2_frame, text="* نوع السلاح:").pack(side=RIGHT, padx=5)
        self.form_vars['weapon_type'] = ttk_bs.StringVar()
        type_combo = ttk_bs.Combobox(row2_frame, textvariable=self.form_vars['weapon_type'], width=20)
        type_combo['values'] = ("مسدس", "بندقية", "رشاش", "قاذف", "سكين", "هراوة", "صاعق", "غاز مسيل")
        type_combo.pack(side=RIGHT, padx=5)
        
        # فئة السلاح
        ttk_bs.Label(row2_frame, text="* فئة السلاح:").pack(side=RIGHT, padx=5)
        self.form_vars['weapon_category'] = ttk_bs.StringVar()
        category_combo = ttk_bs.Combobox(row2_frame, textvariable=self.form_vars['weapon_category'], width=20)
        category_combo['values'] = ("ناري", "أبيض", "غير مميت", "دفاعي", "هجومي", "خاص")
        category_combo.pack(side=RIGHT, padx=5)
        
        # الصف الثالث من الحقول
        row3_frame = ttk_bs.Frame(input_frame)
        row3_frame.pack(fill=X, pady=5)
        
        # الماركة
        ttk_bs.Label(row3_frame, text="* الماركة:").pack(side=RIGHT, padx=5)
        self.form_vars['brand'] = ttk_bs.StringVar()
        brand_combo = ttk_bs.Combobox(row3_frame, textvariable=self.form_vars['brand'], width=20)
        brand_combo['values'] = ("جلوك", "سيج ساور", "بيريتا", "كلاشنيكوف", "إم 16", "إتش كيه", "كولت")
        brand_combo.pack(side=RIGHT, padx=5)
        
        # الموديل
        ttk_bs.Label(row3_frame, text="* الموديل:").pack(side=RIGHT, padx=5)
        self.form_vars['model'] = ttk_bs.StringVar()
        model_entry = ttk_bs.Entry(row3_frame, textvariable=self.form_vars['model'], width=20)
        model_entry.pack(side=RIGHT, padx=5)
        
        # الصف الرابع من الحقول
        row4_frame = ttk_bs.Frame(input_frame)
        row4_frame.pack(fill=X, pady=5)
        
        # العيار
        ttk_bs.Label(row4_frame, text="العيار:").pack(side=RIGHT, padx=5)
        self.form_vars['caliber'] = ttk_bs.StringVar()
        caliber_combo = ttk_bs.Combobox(row4_frame, textvariable=self.form_vars['caliber'], width=15)
        caliber_combo['values'] = ("9mm", "7.62mm", "5.56mm", ".45 ACP", ".40 S&W", "12 Gauge")
        caliber_combo.pack(side=RIGHT, padx=5)
        
        # سنة الصنع
        ttk_bs.Label(row4_frame, text="سنة الصنع:").pack(side=RIGHT, padx=5)
        self.form_vars['manufacture_year'] = ttk_bs.StringVar()
        year_entry = ttk_bs.Entry(row4_frame, textvariable=self.form_vars['manufacture_year'], width=10)
        year_entry.pack(side=RIGHT, padx=5)
        
        # بلد المنشأ
        ttk_bs.Label(row4_frame, text="بلد المنشأ:").pack(side=RIGHT, padx=5)
        self.form_vars['country_of_origin'] = ttk_bs.StringVar()
        country_combo = ttk_bs.Combobox(row4_frame, textvariable=self.form_vars['country_of_origin'], width=15)
        country_combo['values'] = ("النمسا", "ألمانيا", "أمريكا", "روسيا", "إيطاليا", "سويسرا", "بلجيكا")
        country_combo.pack(side=RIGHT, padx=5)
        
        # الصف الخامس من الحقول
        row5_frame = ttk_bs.Frame(input_frame)
        row5_frame.pack(fill=X, pady=5)
        
        # طول السبطانة
        ttk_bs.Label(row5_frame, text="طول السبطانة (سم):").pack(side=RIGHT, padx=5)
        self.form_vars['barrel_length'] = ttk_bs.StringVar()
        barrel_entry = ttk_bs.Entry(row5_frame, textvariable=self.form_vars['barrel_length'], width=10)
        barrel_entry.pack(side=RIGHT, padx=5)
        
        # الوزن
        ttk_bs.Label(row5_frame, text="الوزن (كجم):").pack(side=RIGHT, padx=5)
        self.form_vars['weight'] = ttk_bs.StringVar()
        weight_entry = ttk_bs.Entry(row5_frame, textvariable=self.form_vars['weight'], width=10)
        weight_entry.pack(side=RIGHT, padx=5)
        
        # سعة المخزن
        ttk_bs.Label(row5_frame, text="سعة المخزن:").pack(side=RIGHT, padx=5)
        self.form_vars['magazine_capacity'] = ttk_bs.StringVar()
        capacity_entry = ttk_bs.Entry(row5_frame, textvariable=self.form_vars['magazine_capacity'], width=10)
        capacity_entry.pack(side=RIGHT, padx=5)
        
        # الصف السادس من الحقول
        row6_frame = ttk_bs.Frame(input_frame)
        row6_frame.pack(fill=X, pady=5)
        
        # تاريخ الاستلام
        ttk_bs.Label(row6_frame, text="تاريخ الاستلام:").pack(side=RIGHT, padx=5)
        self.form_vars['received_date'] = ttk_bs.StringVar()
        received_date_entry = ttk_bs.Entry(row6_frame, textvariable=self.form_vars['received_date'], width=12)
        received_date_entry.pack(side=RIGHT, padx=5)
        
        # مصدر الاستلام
        ttk_bs.Label(row6_frame, text="مصدر الاستلام:").pack(side=RIGHT, padx=5)
        self.form_vars['source'] = ttk_bs.StringVar()
        source_combo = ttk_bs.Combobox(row6_frame, textvariable=self.form_vars['source'], width=25)
        source_combo['values'] = ("وزارة الداخلية", "وزارة الدفاع", "الحرس الوطني", "أمن الدولة", "شراء مباشر")
        source_combo.pack(side=RIGHT, padx=5)
        
        # الصف السابع من الحقول
        row7_frame = ttk_bs.Frame(input_frame)
        row7_frame.pack(fill=X, pady=5)
        
        # المخصص له
        ttk_bs.Label(row7_frame, text="المخصص له:").pack(side=RIGHT, padx=5)
        self.form_vars['assigned_to'] = ttk_bs.StringVar()
        assigned_combo = ttk_bs.Combobox(row7_frame, textvariable=self.form_vars['assigned_to'], width=25)
        assigned_combo.pack(side=RIGHT, padx=5)
        
        # الرتبة
        ttk_bs.Label(row7_frame, text="الرتبة:").pack(side=RIGHT, padx=5)
        self.form_vars['rank'] = ttk_bs.StringVar()
        rank_combo = ttk_bs.Combobox(row7_frame, textvariable=self.form_vars['rank'], width=15)
        rank_combo.pack(side=RIGHT, padx=5)
        
        # الصف الثامن من الحقول
        row8_frame = ttk_bs.Frame(input_frame)
        row8_frame.pack(fill=X, pady=5)
        
        # الوحدة
        ttk_bs.Label(row8_frame, text="الوحدة:").pack(side=RIGHT, padx=5)
        self.form_vars['unit'] = ttk_bs.StringVar()
        unit_combo = ttk_bs.Combobox(row8_frame, textvariable=self.form_vars['unit'], width=25)
        unit_combo.pack(side=RIGHT, padx=5)
        
        # القسم
        ttk_bs.Label(row8_frame, text="القسم:").pack(side=RIGHT, padx=5)
        self.form_vars['department'] = ttk_bs.StringVar()
        department_combo = ttk_bs.Combobox(row8_frame, textvariable=self.form_vars['department'], width=25)
        department_combo.pack(side=RIGHT, padx=5)
        
        # الصف التاسع من الحقول
        row9_frame = ttk_bs.Frame(input_frame)
        row9_frame.pack(fill=X, pady=5)
        
        # تاريخ التخصيص
        ttk_bs.Label(row9_frame, text="تاريخ التخصيص:").pack(side=RIGHT, padx=5)
        self.form_vars['assignment_date'] = ttk_bs.StringVar()
        assignment_date_entry = ttk_bs.Entry(row9_frame, textvariable=self.form_vars['assignment_date'], width=12)
        assignment_date_entry.pack(side=RIGHT, padx=5)
        
        # رقم أمر التخصيص
        ttk_bs.Label(row9_frame, text="رقم أمر التخصيص:").pack(side=RIGHT, padx=5)
        self.form_vars['assignment_order'] = ttk_bs.StringVar()
        order_entry = ttk_bs.Entry(row9_frame, textvariable=self.form_vars['assignment_order'], width=20)
        order_entry.pack(side=RIGHT, padx=5)
        
        # الصف العاشر من الحقول
        row10_frame = ttk_bs.Frame(input_frame)
        row10_frame.pack(fill=X, pady=5)
        
        # موقع التخزين
        ttk_bs.Label(row10_frame, text="موقع التخزين:").pack(side=RIGHT, padx=5)
        self.form_vars['storage_location'] = ttk_bs.StringVar()
        storage_combo = ttk_bs.Combobox(row10_frame, textvariable=self.form_vars['storage_location'], width=25)
        storage_combo['values'] = ("مستودع الأسلحة الرئيسي", "مستودع الوحدة", "خزانة الأسلحة", "مخزن الطوارئ")
        storage_combo.pack(side=RIGHT, padx=5)
        
        # رقم الخزانة
        ttk_bs.Label(row10_frame, text="رقم الخزانة:").pack(side=RIGHT, padx=5)
        self.form_vars['locker_number'] = ttk_bs.StringVar()
        locker_entry = ttk_bs.Entry(row10_frame, textvariable=self.form_vars['locker_number'], width=10)
        locker_entry.pack(side=RIGHT, padx=5)
        
        # الصف الحادي عشر من الحقول
        row11_frame = ttk_bs.Frame(input_frame)
        row11_frame.pack(fill=X, pady=5)
        
        # آخر صيانة
        ttk_bs.Label(row11_frame, text="تاريخ آخر صيانة:").pack(side=RIGHT, padx=5)
        self.form_vars['last_maintenance'] = ttk_bs.StringVar()
        maintenance_entry = ttk_bs.Entry(row11_frame, textvariable=self.form_vars['last_maintenance'], width=12)
        maintenance_entry.pack(side=RIGHT, padx=5)
        
        # الصيانة القادمة
        ttk_bs.Label(row11_frame, text="تاريخ الصيانة القادمة:").pack(side=RIGHT, padx=5)
        self.form_vars['next_maintenance'] = ttk_bs.StringVar()
        next_maintenance_entry = ttk_bs.Entry(row11_frame, textvariable=self.form_vars['next_maintenance'], width=12)
        next_maintenance_entry.pack(side=RIGHT, padx=5)
        
        # الصف الثاني عشر من الحقول
        row12_frame = ttk_bs.Frame(input_frame)
        row12_frame.pack(fill=X, pady=5)
        
        # الحالة
        ttk_bs.Label(row12_frame, text="الحالة:").pack(side=RIGHT, padx=5)
        self.form_vars['status'] = ttk_bs.StringVar()
        status_combo = ttk_bs.Combobox(row12_frame, textvariable=self.form_vars['status'], width=15)
        status_combo['values'] = ("نشط", "غير نشط", "صيانة", "خارج الخدمة", "مفقود", "تالف")
        status_combo.pack(side=RIGHT, padx=5)
        
        # حالة السلاح
        ttk_bs.Label(row12_frame, text="حالة السلاح:").pack(side=RIGHT, padx=5)
        self.form_vars['condition'] = ttk_bs.StringVar()
        condition_combo = ttk_bs.Combobox(row12_frame, textvariable=self.form_vars['condition'], width=15)
        condition_combo['values'] = ("ممتازة", "جيدة", "متوسطة", "سيئة", "تحتاج صيانة")
        condition_combo.pack(side=RIGHT, padx=5)
        
        # الصف الثالث عشر من الحقول
        row13_frame = ttk_bs.Frame(input_frame)
        row13_frame.pack(fill=X, pady=5)
        
        # مستوى الأمان
        ttk_bs.Label(row13_frame, text="مستوى الأمان:").pack(side=RIGHT, padx=5)
        self.form_vars['security_level'] = ttk_bs.StringVar()
        security_combo = ttk_bs.Combobox(row13_frame, textvariable=self.form_vars['security_level'], width=15)
        security_combo['values'] = ("عادي", "محدود", "سري", "سري جداً")
        security_combo.pack(side=RIGHT, padx=5)
        
        # رخصة الحمل
        ttk_bs.Label(row13_frame, text="رخصة الحمل:").pack(side=RIGHT, padx=5)
        self.form_vars['carry_license'] = ttk_bs.StringVar()
        license_entry = ttk_bs.Entry(row13_frame, textvariable=self.form_vars['carry_license'], width=20)
        license_entry.pack(side=RIGHT, padx=5)
        
        # الصف الرابع عشر - الوصف
        row14_frame = ttk_bs.Frame(input_frame)
        row14_frame.pack(fill=X, pady=5)
        
        ttk_bs.Label(row14_frame, text="الوصف:").pack(side=RIGHT, padx=5)
        self.form_vars['description'] = ttk_bs.StringVar()
        description_entry = ttk_bs.Entry(row14_frame, textvariable=self.form_vars['description'], width=50)
        description_entry.pack(side=RIGHT, padx=5, fill=X, expand=True)
        
        # الصف الخامس عشر - الملاحظات
        row15_frame = ttk_bs.Frame(input_frame)
        row15_frame.pack(fill=X, pady=5)
        
        ttk_bs.Label(row15_frame, text="الملاحظات:").pack(side=RIGHT, padx=5)
        self.form_vars['notes'] = ttk_bs.StringVar()
        notes_entry = ttk_bs.Entry(row15_frame, textvariable=self.form_vars['notes'], width=50)
        notes_entry.pack(side=RIGHT, padx=5, fill=X, expand=True)
        
        # تحميل البيانات في القوائم المنسدلة
        self.load_combo_data(assigned_combo, rank_combo, unit_combo, department_combo)
    
    def create_data_table(self, parent):
        """إنشاء جدول البيانات"""
        table_frame = ttk_bs.LabelFrame(parent, text="قائمة الأسلحة", padding=10)
        table_frame.pack(fill=BOTH, expand=True)
        
        # إنشاء Treeview
        columns = ("الرقم", "رقم السلاح", "الرقم التسلسلي", "نوع السلاح", "الماركة", "المخصص له", "الوحدة", "الحالة")
        self.tree = ttk_bs.Treeview(table_frame, columns=columns, show="headings", height=8)
        
        # تعيين عناوين الأعمدة
        for col in columns:
            self.tree.heading(col, text=col)
            self.tree.column(col, width=120, anchor=CENTER)
        
        # شريط التمرير
        scrollbar = ttk_bs.Scrollbar(table_frame, orient=VERTICAL, command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)
        
        # تخطيط الجدول
        self.tree.pack(side=LEFT, fill=BOTH, expand=True)
        scrollbar.pack(side=RIGHT, fill=Y)
        
        # ربط حدث النقر
        self.tree.bind("<<TreeviewSelect>>", self.on_item_select)
    
    def load_combo_data(self, assigned_combo, rank_combo, unit_combo, department_combo):
        """تحميل بيانات القوائم المنسدلة"""
        try:
            # تحميل الأفراد
            personnel = ["أحمد محمد السالم", "سالم علي الأحمد", "خالد حسن المحمد", "محمد عبدالله الزهراني"]
            assigned_combo['values'] = personnel
            
            # تحميل الرتب
            ranks = ["عريف", "وكيل رقيب", "رقيب", "رقيب أول", "مساعد", "ملازم", "ملازم أول", "نقيب"]
            rank_combo['values'] = ranks
            
            # تحميل الوحدات
            units = ["الوحدة الأولى", "الوحدة الثانية", "وحدة الأمن", "وحدة الطوارئ", "الوحدة الخاصة"]
            unit_combo['values'] = units
            
            # تحميل الأقسام
            departments = ["قسم الأمن", "قسم الحراسة", "قسم التدريب", "قسم العمليات", "قسم الإدارة"]
            department_combo['values'] = departments
            
        except Exception as e:
            print(f"خطأ في تحميل بيانات القوائم: {e}")
    
    def handle_button_action(self, action):
        """معالجة أحداث الأزرار"""
        if action == "add":
            self.add_record()
        elif action == "save":
            self.save_record()
        elif action == "edit":
            self.edit_record()
        elif action == "delete":
            self.delete_record()
        elif action == "cancel":
            self.cancel_operation()
        elif action == "print":
            self.print_record()
        elif action == "export":
            self.export_data()
        elif action == "inventory":
            self.inventory_check()
    
    def load_data(self):
        """تحميل البيانات في الجدول"""
        try:
            # مسح البيانات الحالية
            for item in self.tree.get_children():
                self.tree.delete(item)
            
            # تحميل البيانات من قاعدة البيانات
            query = """
                SELECT *
                FROM weapon_management
                WHERE is_active = 1
                ORDER BY weapon_type, weapon_number
            """
            records = self.db_manager.execute_query(query)
            
            for i, record in enumerate(records, 1):
                values = (
                    i,
                    record.get('weapon_number', ''),
                    record.get('serial_number', ''),
                    record.get('weapon_type', ''),
                    record.get('brand', ''),
                    record.get('assigned_to', ''),
                    record.get('unit', ''),
                    record.get('status', '')
                )
                self.tree.insert("", "end", values=values)
                
        except Exception as e:
            print(f"خطأ في تحميل البيانات: {e}")
    
    def on_item_select(self, event):
        """معالجة تحديد عنصر في الجدول"""
        selection = self.tree.selection()
        if selection:
            item = self.tree.item(selection[0])
            values = item['values']
            
            # تحديث الحقول بالبيانات المحددة
            if len(values) >= 8:
                self.form_vars['weapon_number'].set(values[1])
                self.form_vars['serial_number'].set(values[2])
                self.form_vars['weapon_type'].set(values[3])
                self.form_vars['brand'].set(values[4])
                self.form_vars['assigned_to'].set(values[5])
                self.form_vars['unit'].set(values[6])
                self.form_vars['status'].set(values[7])
    
    def add_record(self):
        """إضافة سجل جديد"""
        self.clear_form()
        messagebox.showinfo("إضافة", "تم تفعيل وضع الإضافة")
    
    def save_record(self):
        """حفظ السجل"""
        try:
            # جمع البيانات من النموذج
            data = {field: var.get().strip() for field, var in self.form_vars.items()}
            
            # التحقق من البيانات المطلوبة
            required_fields = ['weapon_number', 'serial_number', 'weapon_type', 'weapon_category', 'brand', 'model']
            missing_fields = [field for field in required_fields if not data[field]]
            
            if missing_fields:
                messagebox.showerror("خطأ", "يرجى ملء جميع الحقول المطلوبة")
                return
            
            messagebox.showinfo("نجح", "تم حفظ بيانات السلاح بنجاح")
            self.load_data()
            self.clear_form()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في حفظ البيانات: {e}")
    
    def edit_record(self):
        """تعديل السجل"""
        selection = self.tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى تحديد سلاح للتعديل")
            return
        
        messagebox.showinfo("تعديل", "تم تفعيل وضع التعديل")
    
    def delete_record(self):
        """حذف السجل"""
        selection = self.tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى تحديد سلاح للحذف")
            return
        
        if messagebox.askyesno("تأكيد", "هل أنت متأكد من حذف هذا السلاح؟"):
            self.tree.delete(selection[0])
            messagebox.showinfo("حذف", "تم حذف السلاح بنجاح")
    
    def cancel_operation(self):
        """إلغاء العملية"""
        self.clear_form()
        messagebox.showinfo("إلغاء", "تم إلغاء العملية")
    
    def print_record(self):
        """طباعة السجل"""
        messagebox.showinfo("طباعة", "سيتم تطوير وظيفة الطباعة قريباً")
    
    def export_data(self):
        """تصدير البيانات"""
        messagebox.showinfo("تصدير", "سيتم تطوير وظيفة التصدير قريباً")
    
    def inventory_check(self):
        """جرد الأسلحة"""
        messagebox.showinfo("جرد", "سيتم تطوير وظيفة الجرد قريباً")
    
    def clear_form(self):
        """مسح النموذج"""
        for var in self.form_vars.values():
            var.set("")
