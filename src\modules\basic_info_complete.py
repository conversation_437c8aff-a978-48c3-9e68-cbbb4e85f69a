#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
وحدة المعلومات الأساسية الكاملة مطابقة للصورة
"""

import tkinter as tk
from tkinter import ttk, messagebox
import ttkbootstrap as ttk_bs
from ttkbootstrap.constants import *

class BasicInfoCompleteModule:
    def __init__(self, parent_frame, db_manager):
        self.parent = parent_frame
        self.db_manager = db_manager
        self.current_record = None
        self.form_vars = {}
        
        # إنشاء الواجهة
        self.create_interface()
        
        # تحميل البيانات
        self.load_data()
    
    def create_interface(self):
        """إنشاء واجهة المعلومات الأساسية مطابقة للصورة"""
        # مسح المحتوى السابق
        for widget in self.parent.winfo_children():
            widget.destroy()

        # تعيين خلفية فاتحة للإطار الرئيسي
        # self.parent.configure(bg='white')  # لا يعمل مع ttkbootstrap

        # الإطار الرئيسي مع خلفية بيضاء
        main_frame = ttk_bs.Frame(self.parent, bootstyle="light")
        main_frame.pack(fill=BOTH, expand=True, padx=5, pady=5)

        # العنوان الرئيسي
        title_frame = ttk_bs.Frame(main_frame, bootstyle="light")
        title_frame.pack(fill=X, pady=(5, 10))

        title_label = ttk_bs.Label(
            title_frame,
            text="إدارة المعلومات الأساسية",
            font=("Tahoma", 14, "bold"),
            bootstyle="dark"
        )
        title_label.pack()

        # إنشاء التبويبات
        self.create_tabs(main_frame)

        # إنشاء شريط الأزرار
        self.create_buttons_bar(main_frame)

        # إنشاء منطقة الإدخال
        self.create_input_area(main_frame)

        # إنشاء جدول البيانات
        self.create_data_table(main_frame)
    
    def create_tabs(self, parent):
        """إنشاء التبويبات العلوية مطابقة للصورة"""
        tabs_frame = ttk_bs.Frame(parent, bootstyle="light")
        tabs_frame.pack(fill=X, pady=(0, 5))

        # التبويبات مطابقة للصورة بالألوان الصحيحة
        tabs = [
            ("القوة العمومية", "force", "success"),  # أخضر - النشط
            ("المعدات", "equipment", "info"),         # أزرق
            ("الأصناف", "categories", "secondary"),    # رمادي
            ("الأسلحة", "weapons", "danger"),          # أحمر
            ("المستخدمين", "users", "secondary"),     # رمادي
            ("أدوات الاستعلام", "queries", "secondary") # رمادي
        ]

        for i, (text, tab_id, style) in enumerate(tabs):
            btn = ttk_bs.Button(
                tabs_frame,
                text=text,
                command=lambda t=tab_id: self.switch_tab(t),
                bootstyle=style,
                width=18
            )
            btn.pack(side=LEFT, padx=1)
    
    def create_buttons_bar(self, parent):
        """إنشاء شريط الأزرار مطابق للصورة"""
        buttons_frame = ttk_bs.Frame(parent, bootstyle="light")
        buttons_frame.pack(fill=X, pady=(5, 10))

        # الأزرار مطابقة للصورة بدون أيقونات
        buttons = [
            ("إضافة", "add", "success"),
            ("حفظ", "save", "primary"),
            ("تعديل", "edit", "warning"),
            ("حذف", "delete", "danger"),
            ("إلغاء", "cancel", "secondary")
        ]

        for text, action, style in buttons:
            btn = ttk_bs.Button(
                buttons_frame,
                text=text,
                command=lambda a=action: self.handle_button_action(a),
                bootstyle=style,
                width=10
            )
            btn.pack(side=LEFT, padx=3)
    
    def create_input_area(self, parent):
        """إنشاء منطقة الإدخال مطابقة للصورة"""
        # إطار منطقة الإدخال مع خلفية بيضاء وحدود
        input_frame = ttk_bs.LabelFrame(
            parent,
            text="",
            padding=15,
            bootstyle="light",
            relief="solid",
            borderwidth=1
        )
        input_frame.pack(fill=X, pady=(5, 10), padx=5)

        # الصف الأول من الحقول
        row1_frame = ttk_bs.Frame(input_frame, bootstyle="light")
        row1_frame.pack(fill=X, pady=5)

        # رقم القوة العمومية (أقصى اليمين)
        force_number_frame = ttk_bs.Frame(row1_frame, bootstyle="light")
        force_number_frame.pack(side=RIGHT, padx=10)

        ttk_bs.Label(force_number_frame, text="* رقم القوة العمومية:",
                    font=("Tahoma", 9)).pack(anchor="e")
        self.form_vars['force_number'] = ttk_bs.StringVar()
        force_number_entry = ttk_bs.Entry(force_number_frame,
                                        textvariable=self.form_vars['force_number'],
                                        width=20, font=("Tahoma", 9))
        force_number_entry.pack(pady=2)
        
        # رقم الوحدة (وسط)
        unit_number_frame = ttk_bs.Frame(row1_frame, bootstyle="light")
        unit_number_frame.pack(side=RIGHT, padx=10)

        ttk_bs.Label(unit_number_frame, text="* رقم الوحدة:",
                    font=("Tahoma", 9)).pack(anchor="e")
        self.form_vars['unit_number'] = ttk_bs.StringVar()
        unit_number_entry = ttk_bs.Entry(unit_number_frame,
                                       textvariable=self.form_vars['unit_number'],
                                       width=20, font=("Tahoma", 9))
        unit_number_entry.pack(pady=2)

        # اسم القوة العمومية (أقصى اليسار)
        force_name_frame = ttk_bs.Frame(row1_frame, bootstyle="light")
        force_name_frame.pack(side=RIGHT, padx=10)

        ttk_bs.Label(force_name_frame, text="* اسم القوة العمومية:",
                    font=("Tahoma", 9)).pack(anchor="e")
        self.form_vars['force_name'] = ttk_bs.StringVar()
        force_name_entry = ttk_bs.Entry(force_name_frame,
                                      textvariable=self.form_vars['force_name'],
                                      width=25, font=("Tahoma", 9))
        force_name_entry.pack(pady=2)

        # الصف الثاني من الحقول
        row2_frame = ttk_bs.Frame(input_frame, bootstyle="light")
        row2_frame.pack(fill=X, pady=10)

        # وظيفة آمر الوحدة (أقصى اليمين)
        commander_pos_frame = ttk_bs.Frame(row2_frame, bootstyle="light")
        commander_pos_frame.pack(side=RIGHT, padx=10)

        ttk_bs.Label(commander_pos_frame, text="وظيفة آمر الوحدة:",
                    font=("Tahoma", 9)).pack(anchor="e")
        self.form_vars['commander_position'] = ttk_bs.StringVar()
        commander_pos_entry = ttk_bs.Entry(commander_pos_frame,
                                         textvariable=self.form_vars['commander_position'],
                                         width=20, font=("Tahoma", 9))
        commander_pos_entry.pack(pady=2)

        # رتبة آمر الوحدة (وسط)
        commander_rank_frame = ttk_bs.Frame(row2_frame, bootstyle="light")
        commander_rank_frame.pack(side=RIGHT, padx=10)

        ttk_bs.Label(commander_rank_frame, text="رتبة آمر الوحدة:",
                    font=("Tahoma", 9)).pack(anchor="e")
        self.form_vars['commander_rank'] = ttk_bs.StringVar()
        commander_rank_combo = ttk_bs.Combobox(commander_rank_frame,
                                             textvariable=self.form_vars['commander_rank'],
                                             width=18, font=("Tahoma", 9))
        commander_rank_combo.pack(pady=2)

        # اسم آمر الوحدة (أقصى اليسار)
        commander_name_frame = ttk_bs.Frame(row2_frame, bootstyle="light")
        commander_name_frame.pack(side=RIGHT, padx=10)

        ttk_bs.Label(commander_name_frame, text="اسم آمر الوحدة:",
                    font=("Tahoma", 9)).pack(anchor="e")
        self.form_vars['commander_name'] = ttk_bs.StringVar()
        commander_name_entry = ttk_bs.Entry(commander_name_frame,
                                          textvariable=self.form_vars['commander_name'],
                                          width=25, font=("Tahoma", 9))
        commander_name_entry.pack(pady=2)
        
        # الصف الثالث من الحقول
        row3_frame = ttk_bs.Frame(input_frame, bootstyle="light")
        row3_frame.pack(fill=X, pady=10)

        # وظيفة ضابط التموين (أقصى اليمين)
        supply_pos_frame = ttk_bs.Frame(row3_frame, bootstyle="light")
        supply_pos_frame.pack(side=RIGHT, padx=10)

        ttk_bs.Label(supply_pos_frame, text="وظيفة ضابط التموين:",
                    font=("Tahoma", 9)).pack(anchor="e")
        self.form_vars['supply_position'] = ttk_bs.StringVar()
        supply_pos_entry = ttk_bs.Entry(supply_pos_frame,
                                      textvariable=self.form_vars['supply_position'],
                                      width=20, font=("Tahoma", 9))
        supply_pos_entry.pack(pady=2)

        # رتبة ضابط التموين (وسط)
        supply_rank_frame = ttk_bs.Frame(row3_frame, bootstyle="light")
        supply_rank_frame.pack(side=RIGHT, padx=10)

        ttk_bs.Label(supply_rank_frame, text="رتبة ضابط التموين:",
                    font=("Tahoma", 9)).pack(anchor="e")
        self.form_vars['supply_rank'] = ttk_bs.StringVar()
        supply_rank_combo = ttk_bs.Combobox(supply_rank_frame,
                                          textvariable=self.form_vars['supply_rank'],
                                          width=18, font=("Tahoma", 9))
        supply_rank_combo.pack(pady=2)

        # اسم ضابط التموين (أقصى اليسار)
        supply_name_frame = ttk_bs.Frame(row3_frame, bootstyle="light")
        supply_name_frame.pack(side=RIGHT, padx=10)

        ttk_bs.Label(supply_name_frame, text="اسم ضابط التموين:",
                    font=("Tahoma", 9)).pack(anchor="e")
        self.form_vars['supply_name'] = ttk_bs.StringVar()
        supply_name_entry = ttk_bs.Entry(supply_name_frame,
                                       textvariable=self.form_vars['supply_name'],
                                       width=25, font=("Tahoma", 9))
        supply_name_entry.pack(pady=2)

        # تحميل بيانات الرتب في القوائم المنسدلة
        self.load_ranks_data(commander_rank_combo, supply_rank_combo)
    
    def create_data_table(self, parent):
        """إنشاء جدول البيانات مطابق للصورة"""
        # إطار الجدول مع خلفية بيضاء
        table_frame = ttk_bs.LabelFrame(
            parent,
            text="البيانات المحفوظة",
            padding=10,
            bootstyle="light",
            relief="solid",
            borderwidth=1
        )
        table_frame.pack(fill=BOTH, expand=True, pady=(5, 0), padx=5)

        # إنشاء Treeview مطابق للصورة
        columns = ("الرقم", "اسم القوة العمومية", "رقم الوحدة", "اسم آمر الوحدة", "رتبة آمر الوحدة",
                  "وظيفة آمر الوحدة", "اسم ضابط التموين", "رتبة ضابط التموين", "وظيفة ضابط التموين")
        self.tree = ttk_bs.Treeview(table_frame, columns=columns, show="headings", height=12)
        
        # تعيين عناوين الأعمدة وأحجامها مطابقة للصورة
        column_widths = {
            "الرقم": 60,
            "اسم القوة العمومية": 200,
            "رقم الوحدة": 100,
            "اسم آمر الوحدة": 150,
            "رتبة آمر الوحدة": 120,
            "وظيفة آمر الوحدة": 130,
            "اسم ضابط التموين": 150,
            "رتبة ضابط التموين": 130,
            "وظيفة ضابط التموين": 140
        }

        for col in columns:
            self.tree.heading(col, text=col, anchor=CENTER)
            width = column_widths.get(col, 120)
            self.tree.column(col, width=width, anchor=CENTER, minwidth=50)
        
        # شريط التمرير
        scrollbar = ttk_bs.Scrollbar(table_frame, orient=VERTICAL, command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)
        
        # تخطيط الجدول
        self.tree.pack(side=LEFT, fill=BOTH, expand=True)
        scrollbar.pack(side=RIGHT, fill=Y)
        
        # ربط حدث النقر
        self.tree.bind("<<TreeviewSelect>>", self.on_item_select)
    
    def switch_tab(self, tab_id):
        """تبديل التبويبات"""
        print(f"تم تبديل إلى تبويب: {tab_id}")
        # هنا يمكن إضافة منطق تبديل التبويبات
    
    def handle_button_action(self, action):
        """معالجة أحداث الأزرار"""
        if action == "add":
            self.add_record()
        elif action == "save":
            self.save_record()
        elif action == "edit":
            self.edit_record()
        elif action == "delete":
            self.delete_record()
        elif action == "cancel":
            self.cancel_operation()
    
    def load_ranks_data(self, commander_combo, supply_combo):
        """تحميل بيانات الرتب"""
        try:
            # استخدام execute_query بدلاً من get_all_records
            ranks = self.db_manager.execute_query("SELECT name FROM ranks ORDER BY name")
            rank_names = [rank['name'] for rank in ranks] if ranks else []

            commander_combo['values'] = rank_names
            supply_combo['values'] = rank_names
        except Exception as e:
            print(f"خطأ في تحميل الرتب: {e}")

    def load_data(self):
        """تحميل البيانات في الجدول"""
        try:
            # مسح البيانات الحالية
            for item in self.tree.get_children():
                self.tree.delete(item)

            # تحميل البيانات من قاعدة البيانات
            records = self.db_manager.execute_query("SELECT * FROM force_basic_info ORDER BY id")

            for i, record in enumerate(records, 1):
                values = (
                    i,
                    record.get('force_name', ''),
                    record.get('unit_number', ''),
                    record.get('unit_name', ''),
                    record.get('commander_name', ''),
                    record.get('supply_name', '')
                )
                self.tree.insert("", "end", values=values)

        except Exception as e:
            print(f"خطأ في تحميل البيانات: {e}")
    
    def on_item_select(self, event):
        """معالجة تحديد عنصر في الجدول"""
        selection = self.tree.selection()
        if selection:
            item = self.tree.item(selection[0])
            values = item['values']
            
            # تحديث الحقول بالبيانات المحددة
            if len(values) >= 6:
                self.form_vars['force_name'].set(values[1])
                self.form_vars['unit_number'].set(values[2])
                # يمكن إضافة المزيد من الحقول هنا
    
    def add_record(self):
        """إضافة سجل جديد"""
        self.clear_form()
        messagebox.showinfo("إضافة", "تم تفعيل وضع الإضافة")
    
    def save_record(self):
        """حفظ السجل"""
        try:
            # جمع البيانات من النموذج
            force_name = self.form_vars['force_name'].get().strip()
            unit_number = self.form_vars['unit_number'].get().strip()
            force_number = self.form_vars['force_number'].get().strip()
            commander_name = self.form_vars['commander_name'].get().strip()
            commander_rank = self.form_vars['commander_rank'].get().strip()
            commander_position = self.form_vars['commander_position'].get().strip()
            supply_name = self.form_vars['supply_name'].get().strip()
            supply_rank = self.form_vars['supply_rank'].get().strip()
            supply_position = self.form_vars['supply_position'].get().strip()

            # التحقق من البيانات المطلوبة
            if not force_name or not unit_number:
                messagebox.showerror("خطأ", "يرجى ملء الحقول المطلوبة (اسم القوة العمومية ورقم الوحدة)")
                return

            # إنشاء استعلام الإدراج
            query = """
                INSERT INTO force_basic_info
                (force_name, unit_number, force_number, commander_name, commander_rank,
                 commander_position, supply_name, supply_rank, supply_position)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """

            params = (force_name, unit_number, force_number, commander_name, commander_rank,
                     commander_position, supply_name, supply_rank, supply_position)

            # تنفيذ الاستعلام
            self.db_manager.execute_query(query, params)
            messagebox.showinfo("نجح", "تم حفظ البيانات بنجاح")

            # إعادة تحميل البيانات
            self.load_data()
            self.clear_form()

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في حفظ البيانات: {e}")
    
    def edit_record(self):
        """تعديل السجل"""
        selection = self.tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى تحديد سجل للتعديل")
            return
        
        messagebox.showinfo("تعديل", "تم تفعيل وضع التعديل")
    
    def delete_record(self):
        """حذف السجل"""
        selection = self.tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى تحديد سجل للحذف")
            return
        
        if messagebox.askyesno("تأكيد", "هل أنت متأكد من حذف هذا السجل؟"):
            self.tree.delete(selection[0])
            messagebox.showinfo("حذف", "تم حذف السجل بنجاح")
    
    def cancel_operation(self):
        """إلغاء العملية"""
        self.clear_form()
        messagebox.showinfo("إلغاء", "تم إلغاء العملية")
    
    def clear_form(self):
        """مسح النموذج"""
        for var in self.form_vars.values():
            var.set("")
