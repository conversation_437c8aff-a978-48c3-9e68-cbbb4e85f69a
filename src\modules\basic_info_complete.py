#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
وحدة المعلومات الأساسية الكاملة مطابقة للصورة
"""

import tkinter as tk
from tkinter import ttk, messagebox
import ttkbootstrap as ttk_bs
from ttkbootstrap.constants import *

class BasicInfoCompleteModule:
    def __init__(self, parent_frame, db_manager):
        self.parent = parent_frame
        self.db_manager = db_manager
        self.current_record = None
        self.form_vars = {}
        
        # إنشاء الواجهة
        self.create_interface()
        
        # تحميل البيانات
        self.load_data()
    
    def create_interface(self):
        """إنشاء واجهة المعلومات الأساسية مطابقة للصورة"""
        # مسح المحتوى السابق
        for widget in self.parent.winfo_children():
            widget.destroy()
        
        # الإطار الرئيسي
        main_frame = ttk_bs.Frame(self.parent)
        main_frame.pack(fill=BOTH, expand=True, padx=10, pady=10)
        
        # العنوان الرئيسي
        title_frame = ttk_bs.Frame(main_frame)
        title_frame.pack(fill=X, pady=(0, 10))
        
        title_label = ttk_bs.Label(
            title_frame,
            text="إدارة المعلومات الأساسية",
            font=("Tahoma", 16, "bold"),
            bootstyle="primary"
        )
        title_label.pack()
        
        # إنشاء التبويبات
        self.create_tabs(main_frame)
        
        # إنشاء شريط الأزرار
        self.create_buttons_bar(main_frame)
        
        # إنشاء منطقة الإدخال
        self.create_input_area(main_frame)
        
        # إنشاء جدول البيانات
        self.create_data_table(main_frame)
    
    def create_tabs(self, parent):
        """إنشاء التبويبات العلوية"""
        tabs_frame = ttk_bs.Frame(parent)
        tabs_frame.pack(fill=X, pady=(0, 10))
        
        # التبويبات مطابقة للصورة
        tabs = [
            ("القوة العمومية", "force", "info"),
            ("المعدات", "equipment", "info"),
            ("الأصناف", "categories", "info"),
            ("الأسلحة", "weapons", "info"),
            ("المستخدمين", "users", "info"),
            ("أدوات الاستعلام", "queries", "info")
        ]
        
        for text, tab_id, style in tabs:
            btn = ttk_bs.Button(
                tabs_frame,
                text=text,
                command=lambda t=tab_id: self.switch_tab(t),
                bootstyle=style,
                width=15
            )
            btn.pack(side=LEFT, padx=1)
    
    def create_buttons_bar(self, parent):
        """إنشاء شريط الأزرار"""
        buttons_frame = ttk_bs.Frame(parent)
        buttons_frame.pack(fill=X, pady=(0, 10))
        
        # الأزرار مطابقة للصورة
        buttons = [
            ("إضافة", "add", "success", "🆕"),
            ("حفظ", "save", "primary", "💾"),
            ("تعديل", "edit", "warning", "✏️"),
            ("حذف", "delete", "danger", "🗑️"),
            ("إلغاء", "cancel", "secondary", "❌")
        ]
        
        for text, action, style, icon in buttons:
            btn = ttk_bs.Button(
                buttons_frame,
                text=f"{icon} {text}",
                command=lambda a=action: self.handle_button_action(a),
                bootstyle=style,
                width=12
            )
            btn.pack(side=LEFT, padx=2)
    
    def create_input_area(self, parent):
        """إنشاء منطقة الإدخال مطابقة للصورة"""
        input_frame = ttk_bs.LabelFrame(parent, text="بيانات الإدخال", padding=10)
        input_frame.pack(fill=X, pady=(0, 10))
        
        # الصف الأول من الحقول
        row1_frame = ttk_bs.Frame(input_frame)
        row1_frame.pack(fill=X, pady=5)
        
        # رقم القوة العمومية
        ttk_bs.Label(row1_frame, text="* رقم القوة العمومية:").pack(side=RIGHT, padx=5)
        self.form_vars['force_number'] = ttk_bs.StringVar()
        force_number_entry = ttk_bs.Entry(row1_frame, textvariable=self.form_vars['force_number'], width=15)
        force_number_entry.pack(side=RIGHT, padx=5)
        
        # رقم الوحدة
        ttk_bs.Label(row1_frame, text="* رقم الوحدة:").pack(side=RIGHT, padx=5)
        self.form_vars['unit_number'] = ttk_bs.StringVar()
        unit_number_entry = ttk_bs.Entry(row1_frame, textvariable=self.form_vars['unit_number'], width=15)
        unit_number_entry.pack(side=RIGHT, padx=5)
        
        # اسم القوة العمومية
        ttk_bs.Label(row1_frame, text="* اسم القوة العمومية:").pack(side=RIGHT, padx=5)
        self.form_vars['force_name'] = ttk_bs.StringVar()
        force_name_entry = ttk_bs.Entry(row1_frame, textvariable=self.form_vars['force_name'], width=25)
        force_name_entry.pack(side=RIGHT, padx=5)
        
        # الصف الثاني من الحقول
        row2_frame = ttk_bs.Frame(input_frame)
        row2_frame.pack(fill=X, pady=5)
        
        # وظيفة آمر الوحدة
        ttk_bs.Label(row2_frame, text="وظيفة آمر الوحدة:").pack(side=RIGHT, padx=5)
        self.form_vars['commander_position'] = ttk_bs.StringVar()
        commander_pos_entry = ttk_bs.Entry(row2_frame, textvariable=self.form_vars['commander_position'], width=15)
        commander_pos_entry.pack(side=RIGHT, padx=5)
        
        # رتبة آمر الوحدة
        ttk_bs.Label(row2_frame, text="رتبة آمر الوحدة:").pack(side=RIGHT, padx=5)
        self.form_vars['commander_rank'] = ttk_bs.StringVar()
        commander_rank_combo = ttk_bs.Combobox(row2_frame, textvariable=self.form_vars['commander_rank'], width=15)
        commander_rank_combo.pack(side=RIGHT, padx=5)
        
        # اسم آمر الوحدة
        ttk_bs.Label(row2_frame, text="اسم آمر الوحدة:").pack(side=RIGHT, padx=5)
        self.form_vars['commander_name'] = ttk_bs.StringVar()
        commander_name_entry = ttk_bs.Entry(row2_frame, textvariable=self.form_vars['commander_name'], width=25)
        commander_name_entry.pack(side=RIGHT, padx=5)
        
        # الصف الثالث من الحقول
        row3_frame = ttk_bs.Frame(input_frame)
        row3_frame.pack(fill=X, pady=5)
        
        # وظيفة ضابط التموين
        ttk_bs.Label(row3_frame, text="وظيفة ضابط التموين:").pack(side=RIGHT, padx=5)
        self.form_vars['supply_position'] = ttk_bs.StringVar()
        supply_pos_entry = ttk_bs.Entry(row3_frame, textvariable=self.form_vars['supply_position'], width=15)
        supply_pos_entry.pack(side=RIGHT, padx=5)
        
        # رتبة ضابط التموين
        ttk_bs.Label(row3_frame, text="رتبة ضابط التموين:").pack(side=RIGHT, padx=5)
        self.form_vars['supply_rank'] = ttk_bs.StringVar()
        supply_rank_combo = ttk_bs.Combobox(row3_frame, textvariable=self.form_vars['supply_rank'], width=15)
        supply_rank_combo.pack(side=RIGHT, padx=5)
        
        # اسم ضابط التموين
        ttk_bs.Label(row3_frame, text="اسم ضابط التموين:").pack(side=RIGHT, padx=5)
        self.form_vars['supply_name'] = ttk_bs.StringVar()
        supply_name_entry = ttk_bs.Entry(row3_frame, textvariable=self.form_vars['supply_name'], width=25)
        supply_name_entry.pack(side=RIGHT, padx=5)
        
        # تحميل بيانات الرتب في القوائم المنسدلة
        self.load_ranks_data(commander_rank_combo, supply_rank_combo)
    
    def create_data_table(self, parent):
        """إنشاء جدول البيانات"""
        table_frame = ttk_bs.LabelFrame(parent, text="قائمة البيانات", padding=10)
        table_frame.pack(fill=BOTH, expand=True)
        
        # إنشاء Treeview
        columns = ("الرقم", "اسم القوة العمومية", "رقم الوحدة", "اسم الوحدة", "اسم القائد", "ضابط التموين")
        self.tree = ttk_bs.Treeview(table_frame, columns=columns, show="headings", height=10)
        
        # تعيين عناوين الأعمدة
        for col in columns:
            self.tree.heading(col, text=col)
            self.tree.column(col, width=150, anchor=CENTER)
        
        # شريط التمرير
        scrollbar = ttk_bs.Scrollbar(table_frame, orient=VERTICAL, command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)
        
        # تخطيط الجدول
        self.tree.pack(side=LEFT, fill=BOTH, expand=True)
        scrollbar.pack(side=RIGHT, fill=Y)
        
        # ربط حدث النقر
        self.tree.bind("<<TreeviewSelect>>", self.on_item_select)
    
    def switch_tab(self, tab_id):
        """تبديل التبويبات"""
        print(f"تم تبديل إلى تبويب: {tab_id}")
        # هنا يمكن إضافة منطق تبديل التبويبات
    
    def handle_button_action(self, action):
        """معالجة أحداث الأزرار"""
        if action == "add":
            self.add_record()
        elif action == "save":
            self.save_record()
        elif action == "edit":
            self.edit_record()
        elif action == "delete":
            self.delete_record()
        elif action == "cancel":
            self.cancel_operation()
    
    def load_ranks_data(self, commander_combo, supply_combo):
        """تحميل بيانات الرتب"""
        try:
            # استخدام execute_query بدلاً من get_all_records
            ranks = self.db_manager.execute_query("SELECT name FROM ranks ORDER BY name")
            rank_names = [rank['name'] for rank in ranks] if ranks else []

            commander_combo['values'] = rank_names
            supply_combo['values'] = rank_names
        except Exception as e:
            print(f"خطأ في تحميل الرتب: {e}")

    def load_data(self):
        """تحميل البيانات في الجدول"""
        try:
            # مسح البيانات الحالية
            for item in self.tree.get_children():
                self.tree.delete(item)

            # تحميل البيانات من قاعدة البيانات
            records = self.db_manager.execute_query("SELECT * FROM force_basic_info ORDER BY id")

            for i, record in enumerate(records, 1):
                values = (
                    i,
                    record.get('force_name', ''),
                    record.get('unit_number', ''),
                    record.get('unit_name', ''),
                    record.get('commander_name', ''),
                    record.get('supply_name', '')
                )
                self.tree.insert("", "end", values=values)

        except Exception as e:
            print(f"خطأ في تحميل البيانات: {e}")
    
    def on_item_select(self, event):
        """معالجة تحديد عنصر في الجدول"""
        selection = self.tree.selection()
        if selection:
            item = self.tree.item(selection[0])
            values = item['values']
            
            # تحديث الحقول بالبيانات المحددة
            if len(values) >= 6:
                self.form_vars['force_name'].set(values[1])
                self.form_vars['unit_number'].set(values[2])
                # يمكن إضافة المزيد من الحقول هنا
    
    def add_record(self):
        """إضافة سجل جديد"""
        self.clear_form()
        messagebox.showinfo("إضافة", "تم تفعيل وضع الإضافة")
    
    def save_record(self):
        """حفظ السجل"""
        try:
            # جمع البيانات من النموذج
            force_name = self.form_vars['force_name'].get().strip()
            unit_number = self.form_vars['unit_number'].get().strip()
            force_number = self.form_vars['force_number'].get().strip()
            commander_name = self.form_vars['commander_name'].get().strip()
            commander_rank = self.form_vars['commander_rank'].get().strip()
            commander_position = self.form_vars['commander_position'].get().strip()
            supply_name = self.form_vars['supply_name'].get().strip()
            supply_rank = self.form_vars['supply_rank'].get().strip()
            supply_position = self.form_vars['supply_position'].get().strip()

            # التحقق من البيانات المطلوبة
            if not force_name or not unit_number:
                messagebox.showerror("خطأ", "يرجى ملء الحقول المطلوبة (اسم القوة العمومية ورقم الوحدة)")
                return

            # إنشاء استعلام الإدراج
            query = """
                INSERT INTO force_basic_info
                (force_name, unit_number, force_number, commander_name, commander_rank,
                 commander_position, supply_name, supply_rank, supply_position)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """

            params = (force_name, unit_number, force_number, commander_name, commander_rank,
                     commander_position, supply_name, supply_rank, supply_position)

            # تنفيذ الاستعلام
            self.db_manager.execute_query(query, params)
            messagebox.showinfo("نجح", "تم حفظ البيانات بنجاح")

            # إعادة تحميل البيانات
            self.load_data()
            self.clear_form()

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في حفظ البيانات: {e}")
    
    def edit_record(self):
        """تعديل السجل"""
        selection = self.tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى تحديد سجل للتعديل")
            return
        
        messagebox.showinfo("تعديل", "تم تفعيل وضع التعديل")
    
    def delete_record(self):
        """حذف السجل"""
        selection = self.tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى تحديد سجل للحذف")
            return
        
        if messagebox.askyesno("تأكيد", "هل أنت متأكد من حذف هذا السجل؟"):
            self.tree.delete(selection[0])
            messagebox.showinfo("حذف", "تم حذف السجل بنجاح")
    
    def cancel_operation(self):
        """إلغاء العملية"""
        self.clear_form()
        messagebox.showinfo("إلغاء", "تم إلغاء العملية")
    
    def clear_form(self):
        """مسح النموذج"""
        for var in self.form_vars.values():
            var.set("")
