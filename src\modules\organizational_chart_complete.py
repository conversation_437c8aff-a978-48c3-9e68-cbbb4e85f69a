#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
وحدة الجدول التنظيمي الكاملة مطابقة للبرنامج الأصلي
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import ttkbootstrap as ttk_bs
from ttkbootstrap.constants import *
from datetime import datetime

class OrganizationalChartCompleteModule:
    def __init__(self, parent_frame, db_manager):
        self.parent = parent_frame
        self.db_manager = db_manager
        self.current_record = None
        self.form_vars = {}
        
        # إنشاء الواجهة
        self.create_interface()
        
        # تحميل البيانات
        self.load_data()
    
    def create_interface(self):
        """إنشاء واجهة الجدول التنظيمي مطابقة للبرنامج الأصلي"""
        # مسح المحتوى السابق
        for widget in self.parent.winfo_children():
            widget.destroy()
        
        # الإطار الرئيسي
        main_frame = ttk_bs.Frame(self.parent)
        main_frame.pack(fill=BOTH, expand=True, padx=10, pady=10)
        
        # العنوان الرئيسي
        title_frame = ttk_bs.Frame(main_frame)
        title_frame.pack(fill=X, pady=(0, 10))
        
        title_label = ttk_bs.Label(
            title_frame,
            text="الجدول التنظيمي",
            font=("Tahoma", 16, "bold"),
            bootstyle="primary"
        )
        title_label.pack()
        
        # إنشاء شريط الأزرار
        self.create_buttons_bar(main_frame)
        
        # إنشاء منطقة الإدخال
        self.create_input_area(main_frame)
        
        # إنشاء جدول البيانات
        self.create_data_table(main_frame)
    
    def create_buttons_bar(self, parent):
        """إنشاء شريط الأزرار"""
        buttons_frame = ttk_bs.Frame(parent)
        buttons_frame.pack(fill=X, pady=(0, 10))
        
        # الأزرار مطابقة للبرنامج الأصلي
        buttons = [
            ("إضافة", "add", "success", "🆕"),
            ("حفظ", "save", "primary", "💾"),
            ("تعديل", "edit", "warning", "✏️"),
            ("حذف", "delete", "danger", "🗑️"),
            ("إلغاء", "cancel", "secondary", "❌"),
            ("طباعة", "print", "info", "🖨️"),
            ("تصدير", "export", "info", "📤"),
            ("عرض الهيكل", "view_chart", "info", "📊")
        ]
        
        for text, action, style, icon in buttons:
            btn = ttk_bs.Button(
                buttons_frame,
                text=f"{icon} {text}",
                command=lambda a=action: self.handle_button_action(a),
                bootstyle=style,
                width=12
            )
            btn.pack(side=LEFT, padx=2)
    
    def create_input_area(self, parent):
        """إنشاء منطقة الإدخال مطابقة للبرنامج الأصلي"""
        input_frame = ttk_bs.LabelFrame(parent, text="بيانات المنصب التنظيمي", padding=10)
        input_frame.pack(fill=X, pady=(0, 10))
        
        # الصف الأول من الحقول
        row1_frame = ttk_bs.Frame(input_frame)
        row1_frame.pack(fill=X, pady=5)
        
        # رقم المنصب
        ttk_bs.Label(row1_frame, text="* رقم المنصب:").pack(side=RIGHT, padx=5)
        self.form_vars['position_number'] = ttk_bs.StringVar()
        position_number_entry = ttk_bs.Entry(row1_frame, textvariable=self.form_vars['position_number'], width=15)
        position_number_entry.pack(side=RIGHT, padx=5)
        
        # اسم المنصب
        ttk_bs.Label(row1_frame, text="* اسم المنصب:").pack(side=RIGHT, padx=5)
        self.form_vars['position_name'] = ttk_bs.StringVar()
        position_name_entry = ttk_bs.Entry(row1_frame, textvariable=self.form_vars['position_name'], width=30)
        position_name_entry.pack(side=RIGHT, padx=5)
        
        # الصف الثاني من الحقول
        row2_frame = ttk_bs.Frame(input_frame)
        row2_frame.pack(fill=X, pady=5)
        
        # المستوى التنظيمي
        ttk_bs.Label(row2_frame, text="* المستوى التنظيمي:").pack(side=RIGHT, padx=5)
        self.form_vars['organizational_level'] = ttk_bs.StringVar()
        level_combo = ttk_bs.Combobox(row2_frame, textvariable=self.form_vars['organizational_level'], width=20)
        level_combo['values'] = ("المستوى الأول", "المستوى الثاني", "المستوى الثالث", "المستوى الرابع", "المستوى الخامس")
        level_combo.pack(side=RIGHT, padx=5)
        
        # نوع المنصب
        ttk_bs.Label(row2_frame, text="* نوع المنصب:").pack(side=RIGHT, padx=5)
        self.form_vars['position_type'] = ttk_bs.StringVar()
        type_combo = ttk_bs.Combobox(row2_frame, textvariable=self.form_vars['position_type'], width=20)
        type_combo['values'] = ("قيادي", "إشرافي", "تنفيذي", "استشاري", "فني", "إداري")
        type_combo.pack(side=RIGHT, padx=5)
        
        # الصف الثالث من الحقول
        row3_frame = ttk_bs.Frame(input_frame)
        row3_frame.pack(fill=X, pady=5)
        
        # المنصب الأعلى
        ttk_bs.Label(row3_frame, text="المنصب الأعلى:").pack(side=RIGHT, padx=5)
        self.form_vars['parent_position'] = ttk_bs.StringVar()
        parent_combo = ttk_bs.Combobox(row3_frame, textvariable=self.form_vars['parent_position'], width=25)
        parent_combo.pack(side=RIGHT, padx=5)
        
        # الوحدة التنظيمية
        ttk_bs.Label(row3_frame, text="* الوحدة التنظيمية:").pack(side=RIGHT, padx=5)
        self.form_vars['organizational_unit'] = ttk_bs.StringVar()
        unit_combo = ttk_bs.Combobox(row3_frame, textvariable=self.form_vars['organizational_unit'], width=25)
        unit_combo.pack(side=RIGHT, padx=5)
        
        # الصف الرابع من الحقول
        row4_frame = ttk_bs.Frame(input_frame)
        row4_frame.pack(fill=X, pady=5)
        
        # شاغل المنصب
        ttk_bs.Label(row4_frame, text="شاغل المنصب:").pack(side=RIGHT, padx=5)
        self.form_vars['position_holder'] = ttk_bs.StringVar()
        holder_combo = ttk_bs.Combobox(row4_frame, textvariable=self.form_vars['position_holder'], width=25)
        holder_combo.pack(side=RIGHT, padx=5)
        
        # رتبة شاغل المنصب
        ttk_bs.Label(row4_frame, text="رتبة شاغل المنصب:").pack(side=RIGHT, padx=5)
        self.form_vars['holder_rank'] = ttk_bs.StringVar()
        rank_combo = ttk_bs.Combobox(row4_frame, textvariable=self.form_vars['holder_rank'], width=20)
        rank_combo.pack(side=RIGHT, padx=5)
        
        # الصف الخامس من الحقول
        row5_frame = ttk_bs.Frame(input_frame)
        row5_frame.pack(fill=X, pady=5)
        
        # الرتبة المطلوبة
        ttk_bs.Label(row5_frame, text="الرتبة المطلوبة:").pack(side=RIGHT, padx=5)
        self.form_vars['required_rank'] = ttk_bs.StringVar()
        required_rank_combo = ttk_bs.Combobox(row5_frame, textvariable=self.form_vars['required_rank'], width=20)
        required_rank_combo.pack(side=RIGHT, padx=5)
        
        # المؤهل المطلوب
        ttk_bs.Label(row5_frame, text="المؤهل المطلوب:").pack(side=RIGHT, padx=5)
        self.form_vars['required_qualification'] = ttk_bs.StringVar()
        qualification_combo = ttk_bs.Combobox(row5_frame, textvariable=self.form_vars['required_qualification'], width=20)
        qualification_combo['values'] = ("ابتدائي", "متوسط", "ثانوي", "دبلوم", "بكالوريوس", "ماجستير", "دكتوراه")
        qualification_combo.pack(side=RIGHT, padx=5)
        
        # الصف السادس من الحقول
        row6_frame = ttk_bs.Frame(input_frame)
        row6_frame.pack(fill=X, pady=5)
        
        # سنوات الخبرة المطلوبة
        ttk_bs.Label(row6_frame, text="سنوات الخبرة المطلوبة:").pack(side=RIGHT, padx=5)
        self.form_vars['required_experience'] = ttk_bs.StringVar()
        experience_entry = ttk_bs.Entry(row6_frame, textvariable=self.form_vars['required_experience'], width=10)
        experience_entry.pack(side=RIGHT, padx=5)
        
        # عدد المرؤوسين
        ttk_bs.Label(row6_frame, text="عدد المرؤوسين:").pack(side=RIGHT, padx=5)
        self.form_vars['subordinates_count'] = ttk_bs.StringVar()
        subordinates_entry = ttk_bs.Entry(row6_frame, textvariable=self.form_vars['subordinates_count'], width=10)
        subordinates_entry.pack(side=RIGHT, padx=5)
        
        # الصف السابع من الحقول
        row7_frame = ttk_bs.Frame(input_frame)
        row7_frame.pack(fill=X, pady=5)
        
        # تاريخ الإنشاء
        ttk_bs.Label(row7_frame, text="تاريخ الإنشاء:").pack(side=RIGHT, padx=5)
        self.form_vars['creation_date'] = ttk_bs.StringVar()
        creation_date_entry = ttk_bs.Entry(row7_frame, textvariable=self.form_vars['creation_date'], width=12)
        creation_date_entry.pack(side=RIGHT, padx=5)
        
        # تاريخ آخر تحديث
        ttk_bs.Label(row7_frame, text="تاريخ آخر تحديث:").pack(side=RIGHT, padx=5)
        self.form_vars['last_update'] = ttk_bs.StringVar()
        update_date_entry = ttk_bs.Entry(row7_frame, textvariable=self.form_vars['last_update'], width=12)
        update_date_entry.pack(side=RIGHT, padx=5)
        
        # الصف الثامن من الحقول
        row8_frame = ttk_bs.Frame(input_frame)
        row8_frame.pack(fill=X, pady=5)
        
        # الحالة
        ttk_bs.Label(row8_frame, text="الحالة:").pack(side=RIGHT, padx=5)
        self.form_vars['status'] = ttk_bs.StringVar()
        status_combo = ttk_bs.Combobox(row8_frame, textvariable=self.form_vars['status'], width=12)
        status_combo['values'] = ("نشط", "شاغر", "ملغي", "مؤقت")
        status_combo.pack(side=RIGHT, padx=5)
        
        # أولوية الملء
        ttk_bs.Label(row8_frame, text="أولوية الملء:").pack(side=RIGHT, padx=5)
        self.form_vars['fill_priority'] = ttk_bs.StringVar()
        priority_combo = ttk_bs.Combobox(row8_frame, textvariable=self.form_vars['fill_priority'], width=12)
        priority_combo['values'] = ("عالية", "متوسطة", "منخفضة")
        priority_combo.pack(side=RIGHT, padx=5)
        
        # الصف التاسع - الوصف الوظيفي
        row9_frame = ttk_bs.Frame(input_frame)
        row9_frame.pack(fill=X, pady=5)
        
        ttk_bs.Label(row9_frame, text="الوصف الوظيفي:").pack(side=RIGHT, padx=5)
        self.form_vars['job_description'] = ttk_bs.StringVar()
        description_entry = ttk_bs.Entry(row9_frame, textvariable=self.form_vars['job_description'], width=50)
        description_entry.pack(side=RIGHT, padx=5, fill=X, expand=True)
        
        # الصف العاشر - المهام والمسؤوليات
        row10_frame = ttk_bs.Frame(input_frame)
        row10_frame.pack(fill=X, pady=5)
        
        ttk_bs.Label(row10_frame, text="المهام والمسؤوليات:").pack(side=RIGHT, padx=5)
        self.form_vars['responsibilities'] = ttk_bs.StringVar()
        responsibilities_entry = ttk_bs.Entry(row10_frame, textvariable=self.form_vars['responsibilities'], width=50)
        responsibilities_entry.pack(side=RIGHT, padx=5, fill=X, expand=True)
        
        # الصف الحادي عشر - الملاحظات
        row11_frame = ttk_bs.Frame(input_frame)
        row11_frame.pack(fill=X, pady=5)
        
        ttk_bs.Label(row11_frame, text="الملاحظات:").pack(side=RIGHT, padx=5)
        self.form_vars['notes'] = ttk_bs.StringVar()
        notes_entry = ttk_bs.Entry(row11_frame, textvariable=self.form_vars['notes'], width=50)
        notes_entry.pack(side=RIGHT, padx=5, fill=X, expand=True)
        
        # تحميل البيانات في القوائم المنسدلة
        self.load_combo_data(parent_combo, unit_combo, holder_combo, rank_combo, required_rank_combo)
    
    def create_data_table(self, parent):
        """إنشاء جدول البيانات"""
        table_frame = ttk_bs.LabelFrame(parent, text="الهيكل التنظيمي", padding=10)
        table_frame.pack(fill=BOTH, expand=True)
        
        # إنشاء Treeview
        columns = ("الرقم", "رقم المنصب", "اسم المنصب", "المستوى التنظيمي", "نوع المنصب", "الوحدة التنظيمية", "شاغل المنصب", "الحالة")
        self.tree = ttk_bs.Treeview(table_frame, columns=columns, show="headings", height=8)
        
        # تعيين عناوين الأعمدة
        for col in columns:
            self.tree.heading(col, text=col)
            self.tree.column(col, width=120, anchor=CENTER)
        
        # شريط التمرير
        scrollbar = ttk_bs.Scrollbar(table_frame, orient=VERTICAL, command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)
        
        # تخطيط الجدول
        self.tree.pack(side=LEFT, fill=BOTH, expand=True)
        scrollbar.pack(side=RIGHT, fill=Y)
        
        # ربط حدث النقر
        self.tree.bind("<<TreeviewSelect>>", self.on_item_select)
    
    def handle_button_action(self, action):
        """معالجة أحداث الأزرار"""
        if action == "add":
            self.add_record()
        elif action == "save":
            self.save_record()
        elif action == "edit":
            self.edit_record()
        elif action == "delete":
            self.delete_record()
        elif action == "cancel":
            self.cancel_operation()
        elif action == "print":
            self.print_record()
        elif action == "export":
            self.export_data()
        elif action == "view_chart":
            self.view_organizational_chart()
    
    def load_combo_data(self, parent_combo, unit_combo, holder_combo, rank_combo, required_rank_combo):
        """تحميل بيانات القوائم المنسدلة"""
        try:
            # تحميل المناصب الأعلى
            positions = ["القائد العام", "نائب القائد", "رئيس الأركان", "مدير الإدارة"]
            parent_combo['values'] = positions
            
            # تحميل الوحدات التنظيمية
            units = ["القيادة العامة", "إدارة العمليات", "إدارة الاستخبارات", "إدارة الإمداد"]
            unit_combo['values'] = units
            
            # تحميل شاغلي المناصب
            holders = ["العقيد أحمد محمد", "المقدم سالم علي", "الرائد خالد حسن"]
            holder_combo['values'] = holders
            
            # تحميل الرتب
            ranks = ["فريق", "لواء", "عميد", "عقيد", "مقدم", "رائد", "نقيب"]
            rank_combo['values'] = ranks
            required_rank_combo['values'] = ranks
            
        except Exception as e:
            print(f"خطأ في تحميل بيانات القوائم: {e}")
    
    def load_data(self):
        """تحميل البيانات في الجدول"""
        try:
            # مسح البيانات الحالية
            for item in self.tree.get_children():
                self.tree.delete(item)
            
            # تحميل البيانات من قاعدة البيانات
            query = """
                SELECT *
                FROM organizational_chart
                WHERE is_active = 1
                ORDER BY organizational_level, position_number
            """
            records = self.db_manager.execute_query(query)
            
            for i, record in enumerate(records, 1):
                values = (
                    i,
                    record.get('position_number', ''),
                    record.get('position_name', ''),
                    record.get('organizational_level', ''),
                    record.get('position_type', ''),
                    record.get('organizational_unit', ''),
                    record.get('position_holder', ''),
                    record.get('status', '')
                )
                self.tree.insert("", "end", values=values)
                
        except Exception as e:
            print(f"خطأ في تحميل البيانات: {e}")
    
    def on_item_select(self, event):
        """معالجة تحديد عنصر في الجدول"""
        selection = self.tree.selection()
        if selection:
            item = self.tree.item(selection[0])
            values = item['values']
            
            # تحديث الحقول بالبيانات المحددة
            if len(values) >= 8:
                self.form_vars['position_number'].set(values[1])
                self.form_vars['position_name'].set(values[2])
                self.form_vars['organizational_level'].set(values[3])
                self.form_vars['position_type'].set(values[4])
                self.form_vars['organizational_unit'].set(values[5])
                self.form_vars['position_holder'].set(values[6])
                self.form_vars['status'].set(values[7])
    
    def add_record(self):
        """إضافة سجل جديد"""
        self.clear_form()
        messagebox.showinfo("إضافة", "تم تفعيل وضع الإضافة")
    
    def save_record(self):
        """حفظ السجل"""
        try:
            # جمع البيانات من النموذج
            data = {field: var.get().strip() for field, var in self.form_vars.items()}
            
            # التحقق من البيانات المطلوبة
            required_fields = ['position_number', 'position_name', 'organizational_level', 'position_type', 'organizational_unit']
            missing_fields = [field for field in required_fields if not data[field]]
            
            if missing_fields:
                messagebox.showerror("خطأ", "يرجى ملء جميع الحقول المطلوبة")
                return
            
            messagebox.showinfo("نجح", "تم حفظ بيانات المنصب التنظيمي بنجاح")
            self.load_data()
            self.clear_form()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في حفظ البيانات: {e}")
    
    def edit_record(self):
        """تعديل السجل"""
        selection = self.tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى تحديد منصب للتعديل")
            return
        
        messagebox.showinfo("تعديل", "تم تفعيل وضع التعديل")
    
    def delete_record(self):
        """حذف السجل"""
        selection = self.tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى تحديد منصب للحذف")
            return
        
        if messagebox.askyesno("تأكيد", "هل أنت متأكد من حذف هذا المنصب؟"):
            self.tree.delete(selection[0])
            messagebox.showinfo("حذف", "تم حذف المنصب بنجاح")
    
    def cancel_operation(self):
        """إلغاء العملية"""
        self.clear_form()
        messagebox.showinfo("إلغاء", "تم إلغاء العملية")
    
    def print_record(self):
        """طباعة السجل"""
        messagebox.showinfo("طباعة", "سيتم تطوير وظيفة الطباعة قريباً")
    
    def export_data(self):
        """تصدير البيانات"""
        messagebox.showinfo("تصدير", "سيتم تطوير وظيفة التصدير قريباً")
    
    def view_organizational_chart(self):
        """عرض الهيكل التنظيمي"""
        messagebox.showinfo("عرض الهيكل", "سيتم تطوير عرض الهيكل التنظيمي قريباً")
    
    def clear_form(self):
        """مسح النموذج"""
        for var in self.form_vars.values():
            var.set("")
