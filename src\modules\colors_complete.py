#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
وحدة إدارة الألوان الكاملة مطابقة للبرنامج الأصلي
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog, colorchooser
import ttkbootstrap as ttk_bs
from ttkbootstrap.constants import *
from datetime import datetime

class ColorsCompleteModule:
    def __init__(self, parent_frame, db_manager):
        self.parent = parent_frame
        self.db_manager = db_manager
        self.current_record = None
        self.form_vars = {}
        
        # إنشاء الواجهة
        self.create_interface()
        
        # تحميل البيانات
        self.load_data()
    
    def create_interface(self):
        """إنشاء واجهة إدارة الألوان مطابقة للبرنامج الأصلي"""
        # مسح المحتوى السابق
        for widget in self.parent.winfo_children():
            widget.destroy()
        
        # الإطار الرئيسي
        main_frame = ttk_bs.Frame(self.parent)
        main_frame.pack(fill=BOTH, expand=True, padx=10, pady=10)
        
        # العنوان الرئيسي
        title_frame = ttk_bs.Frame(main_frame)
        title_frame.pack(fill=X, pady=(0, 10))
        
        title_label = ttk_bs.Label(
            title_frame,
            text="إدارة الألوان",
            font=("Tahoma", 16, "bold"),
            bootstyle="primary"
        )
        title_label.pack()
        
        # إنشاء شريط الأزرار
        self.create_buttons_bar(main_frame)
        
        # إنشاء منطقة الإدخال
        self.create_input_area(main_frame)
        
        # إنشاء جدول البيانات
        self.create_data_table(main_frame)
    
    def create_buttons_bar(self, parent):
        """إنشاء شريط الأزرار"""
        buttons_frame = ttk_bs.Frame(parent)
        buttons_frame.pack(fill=X, pady=(0, 10))
        
        # الأزرار مطابقة للبرنامج الأصلي
        buttons = [
            ("إضافة", "add", "success", "🆕"),
            ("حفظ", "save", "primary", "💾"),
            ("تعديل", "edit", "warning", "✏️"),
            ("حذف", "delete", "danger", "🗑️"),
            ("إلغاء", "cancel", "secondary", "❌"),
            ("طباعة", "print", "info", "🖨️"),
            ("تصدير", "export", "info", "📤"),
            ("معاينة", "preview", "info", "👁️")
        ]
        
        for text, action, style, icon in buttons:
            btn = ttk_bs.Button(
                buttons_frame,
                text=f"{icon} {text}",
                command=lambda a=action: self.handle_button_action(a),
                bootstyle=style,
                width=12
            )
            btn.pack(side=LEFT, padx=2)
    
    def create_input_area(self, parent):
        """إنشاء منطقة الإدخال مطابقة للبرنامج الأصلي"""
        input_frame = ttk_bs.LabelFrame(parent, text="بيانات اللون", padding=10)
        input_frame.pack(fill=X, pady=(0, 10))
        
        # الصف الأول من الحقول
        row1_frame = ttk_bs.Frame(input_frame)
        row1_frame.pack(fill=X, pady=5)
        
        # رقم اللون
        ttk_bs.Label(row1_frame, text="* رقم اللون:").pack(side=RIGHT, padx=5)
        self.form_vars['color_number'] = ttk_bs.StringVar()
        color_number_entry = ttk_bs.Entry(row1_frame, textvariable=self.form_vars['color_number'], width=15)
        color_number_entry.pack(side=RIGHT, padx=5)
        
        # اسم اللون
        ttk_bs.Label(row1_frame, text="* اسم اللون:").pack(side=RIGHT, padx=5)
        self.form_vars['color_name'] = ttk_bs.StringVar()
        color_name_entry = ttk_bs.Entry(row1_frame, textvariable=self.form_vars['color_name'], width=25)
        color_name_entry.pack(side=RIGHT, padx=5)
        
        # الصف الثاني من الحقول
        row2_frame = ttk_bs.Frame(input_frame)
        row2_frame.pack(fill=X, pady=5)
        
        # كود اللون (HEX)
        ttk_bs.Label(row2_frame, text="* كود اللون (HEX):").pack(side=RIGHT, padx=5)
        self.form_vars['hex_code'] = ttk_bs.StringVar()
        hex_entry = ttk_bs.Entry(row2_frame, textvariable=self.form_vars['hex_code'], width=15)
        hex_entry.pack(side=RIGHT, padx=5)
        
        # زر اختيار اللون
        color_btn = ttk_bs.Button(
            row2_frame,
            text="اختيار اللون",
            command=self.choose_color,
            bootstyle="info",
            width=12
        )
        color_btn.pack(side=RIGHT, padx=5)
        
        # عرض اللون
        self.color_display = tk.Frame(row2_frame, width=50, height=30, relief="solid", bd=1)
        self.color_display.pack(side=RIGHT, padx=5)
        
        # الصف الثالث من الحقول
        row3_frame = ttk_bs.Frame(input_frame)
        row3_frame.pack(fill=X, pady=5)
        
        # قيم RGB
        ttk_bs.Label(row3_frame, text="قيم RGB:").pack(side=RIGHT, padx=5)
        
        # R
        ttk_bs.Label(row3_frame, text="R:").pack(side=RIGHT, padx=2)
        self.form_vars['rgb_r'] = ttk_bs.StringVar()
        r_entry = ttk_bs.Entry(row3_frame, textvariable=self.form_vars['rgb_r'], width=5)
        r_entry.pack(side=RIGHT, padx=2)
        
        # G
        ttk_bs.Label(row3_frame, text="G:").pack(side=RIGHT, padx=2)
        self.form_vars['rgb_g'] = ttk_bs.StringVar()
        g_entry = ttk_bs.Entry(row3_frame, textvariable=self.form_vars['rgb_g'], width=5)
        g_entry.pack(side=RIGHT, padx=2)
        
        # B
        ttk_bs.Label(row3_frame, text="B:").pack(side=RIGHT, padx=2)
        self.form_vars['rgb_b'] = ttk_bs.StringVar()
        b_entry = ttk_bs.Entry(row3_frame, textvariable=self.form_vars['rgb_b'], width=5)
        b_entry.pack(side=RIGHT, padx=2)
        
        # الصف الرابع من الحقول
        row4_frame = ttk_bs.Frame(input_frame)
        row4_frame.pack(fill=X, pady=5)
        
        # فئة اللون
        ttk_bs.Label(row4_frame, text="* فئة اللون:").pack(side=RIGHT, padx=5)
        self.form_vars['color_category'] = ttk_bs.StringVar()
        category_combo = ttk_bs.Combobox(row4_frame, textvariable=self.form_vars['color_category'], width=20)
        category_combo['values'] = ("أساسي", "ثانوي", "متدرج", "مكمل", "محايد", "دافئ", "بارد")
        category_combo.pack(side=RIGHT, padx=5)
        
        # نوع الاستخدام
        ttk_bs.Label(row4_frame, text="* نوع الاستخدام:").pack(side=RIGHT, padx=5)
        self.form_vars['usage_type'] = ttk_bs.StringVar()
        usage_combo = ttk_bs.Combobox(row4_frame, textvariable=self.form_vars['usage_type'], width=20)
        usage_combo['values'] = ("خلفية", "نص", "حدود", "أزرار", "تحديد", "تحذير", "خطأ", "نجاح")
        usage_combo.pack(side=RIGHT, padx=5)
        
        # الصف الخامس من الحقول
        row5_frame = ttk_bs.Frame(input_frame)
        row5_frame.pack(fill=X, pady=5)
        
        # درجة الشفافية
        ttk_bs.Label(row5_frame, text="درجة الشفافية (%):").pack(side=RIGHT, padx=5)
        self.form_vars['opacity'] = ttk_bs.StringVar()
        opacity_entry = ttk_bs.Entry(row5_frame, textvariable=self.form_vars['opacity'], width=10)
        opacity_entry.pack(side=RIGHT, padx=5)
        
        # درجة السطوع
        ttk_bs.Label(row5_frame, text="درجة السطوع (%):").pack(side=RIGHT, padx=5)
        self.form_vars['brightness'] = ttk_bs.StringVar()
        brightness_entry = ttk_bs.Entry(row5_frame, textvariable=self.form_vars['brightness'], width=10)
        brightness_entry.pack(side=RIGHT, padx=5)
        
        # الصف السادس من الحقول
        row6_frame = ttk_bs.Frame(input_frame)
        row6_frame.pack(fill=X, pady=5)
        
        # درجة التشبع
        ttk_bs.Label(row6_frame, text="درجة التشبع (%):").pack(side=RIGHT, padx=5)
        self.form_vars['saturation'] = ttk_bs.StringVar()
        saturation_entry = ttk_bs.Entry(row6_frame, textvariable=self.form_vars['saturation'], width=10)
        saturation_entry.pack(side=RIGHT, padx=5)
        
        # درجة الصبغة
        ttk_bs.Label(row6_frame, text="درجة الصبغة:").pack(side=RIGHT, padx=5)
        self.form_vars['hue'] = ttk_bs.StringVar()
        hue_entry = ttk_bs.Entry(row6_frame, textvariable=self.form_vars['hue'], width=10)
        hue_entry.pack(side=RIGHT, padx=5)
        
        # الصف السابع من الحقول
        row7_frame = ttk_bs.Frame(input_frame)
        row7_frame.pack(fill=X, pady=5)
        
        # الحالة
        ttk_bs.Label(row7_frame, text="الحالة:").pack(side=RIGHT, padx=5)
        self.form_vars['status'] = ttk_bs.StringVar()
        status_combo = ttk_bs.Combobox(row7_frame, textvariable=self.form_vars['status'], width=12)
        status_combo['values'] = ("نشط", "غير نشط", "مؤقت")
        status_combo.pack(side=RIGHT, padx=5)
        
        # تاريخ الإضافة
        ttk_bs.Label(row7_frame, text="تاريخ الإضافة:").pack(side=RIGHT, padx=5)
        self.form_vars['creation_date'] = ttk_bs.StringVar()
        creation_date_entry = ttk_bs.Entry(row7_frame, textvariable=self.form_vars['creation_date'], width=12)
        creation_date_entry.pack(side=RIGHT, padx=5)
        
        # الصف الثامن - الوصف
        row8_frame = ttk_bs.Frame(input_frame)
        row8_frame.pack(fill=X, pady=5)
        
        ttk_bs.Label(row8_frame, text="الوصف:").pack(side=RIGHT, padx=5)
        self.form_vars['description'] = ttk_bs.StringVar()
        description_entry = ttk_bs.Entry(row8_frame, textvariable=self.form_vars['description'], width=50)
        description_entry.pack(side=RIGHT, padx=5, fill=X, expand=True)
        
        # الصف التاسع - الملاحظات
        row9_frame = ttk_bs.Frame(input_frame)
        row9_frame.pack(fill=X, pady=5)
        
        ttk_bs.Label(row9_frame, text="الملاحظات:").pack(side=RIGHT, padx=5)
        self.form_vars['notes'] = ttk_bs.StringVar()
        notes_entry = ttk_bs.Entry(row9_frame, textvariable=self.form_vars['notes'], width=50)
        notes_entry.pack(side=RIGHT, padx=5, fill=X, expand=True)
    
    def create_data_table(self, parent):
        """إنشاء جدول البيانات"""
        table_frame = ttk_bs.LabelFrame(parent, text="قائمة الألوان", padding=10)
        table_frame.pack(fill=BOTH, expand=True)
        
        # إنشاء Treeview
        columns = ("الرقم", "رقم اللون", "اسم اللون", "كود HEX", "فئة اللون", "نوع الاستخدام", "الشفافية", "الحالة")
        self.tree = ttk_bs.Treeview(table_frame, columns=columns, show="headings", height=8)
        
        # تعيين عناوين الأعمدة
        for col in columns:
            self.tree.heading(col, text=col)
            self.tree.column(col, width=100, anchor=CENTER)
        
        # شريط التمرير
        scrollbar = ttk_bs.Scrollbar(table_frame, orient=VERTICAL, command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)
        
        # تخطيط الجدول
        self.tree.pack(side=LEFT, fill=BOTH, expand=True)
        scrollbar.pack(side=RIGHT, fill=Y)
        
        # ربط حدث النقر
        self.tree.bind("<<TreeviewSelect>>", self.on_item_select)
    
    def choose_color(self):
        """اختيار لون من منتقي الألوان"""
        color = colorchooser.askcolor(title="اختيار لون")
        if color[1]:  # إذا تم اختيار لون
            hex_color = color[1]
            rgb_color = color[0]
            
            # تحديث الحقول
            self.form_vars['hex_code'].set(hex_color)
            self.form_vars['rgb_r'].set(str(int(rgb_color[0])))
            self.form_vars['rgb_g'].set(str(int(rgb_color[1])))
            self.form_vars['rgb_b'].set(str(int(rgb_color[2])))
            
            # تحديث عرض اللون
            self.color_display.configure(bg=hex_color)
    
    def handle_button_action(self, action):
        """معالجة أحداث الأزرار"""
        if action == "add":
            self.add_record()
        elif action == "save":
            self.save_record()
        elif action == "edit":
            self.edit_record()
        elif action == "delete":
            self.delete_record()
        elif action == "cancel":
            self.cancel_operation()
        elif action == "print":
            self.print_record()
        elif action == "export":
            self.export_data()
        elif action == "preview":
            self.preview_color()
    
    def load_data(self):
        """تحميل البيانات في الجدول"""
        try:
            # مسح البيانات الحالية
            for item in self.tree.get_children():
                self.tree.delete(item)
            
            # تحميل البيانات من قاعدة البيانات
            query = """
                SELECT *
                FROM colors
                WHERE is_active = 1
                ORDER BY color_category, color_name
            """
            records = self.db_manager.execute_query(query)
            
            for i, record in enumerate(records, 1):
                values = (
                    i,
                    record.get('color_number', ''),
                    record.get('color_name', ''),
                    record.get('hex_code', ''),
                    record.get('color_category', ''),
                    record.get('usage_type', ''),
                    record.get('opacity', ''),
                    record.get('status', '')
                )
                self.tree.insert("", "end", values=values)
                
        except Exception as e:
            print(f"خطأ في تحميل البيانات: {e}")
    
    def on_item_select(self, event):
        """معالجة تحديد عنصر في الجدول"""
        selection = self.tree.selection()
        if selection:
            item = self.tree.item(selection[0])
            values = item['values']
            
            # تحديث الحقول بالبيانات المحددة
            if len(values) >= 8:
                self.form_vars['color_number'].set(values[1])
                self.form_vars['color_name'].set(values[2])
                self.form_vars['hex_code'].set(values[3])
                self.form_vars['color_category'].set(values[4])
                self.form_vars['usage_type'].set(values[5])
                self.form_vars['opacity'].set(values[6])
                self.form_vars['status'].set(values[7])
                
                # تحديث عرض اللون
                try:
                    self.color_display.configure(bg=values[3])
                except:
                    pass
    
    def add_record(self):
        """إضافة سجل جديد"""
        self.clear_form()
        messagebox.showinfo("إضافة", "تم تفعيل وضع الإضافة")
    
    def save_record(self):
        """حفظ السجل"""
        try:
            # جمع البيانات من النموذج
            data = {field: var.get().strip() for field, var in self.form_vars.items()}
            
            # التحقق من البيانات المطلوبة
            required_fields = ['color_number', 'color_name', 'hex_code', 'color_category', 'usage_type']
            missing_fields = [field for field in required_fields if not data[field]]
            
            if missing_fields:
                messagebox.showerror("خطأ", "يرجى ملء جميع الحقول المطلوبة")
                return
            
            messagebox.showinfo("نجح", "تم حفظ بيانات اللون بنجاح")
            self.load_data()
            self.clear_form()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في حفظ البيانات: {e}")
    
    def edit_record(self):
        """تعديل السجل"""
        selection = self.tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى تحديد لون للتعديل")
            return
        
        messagebox.showinfo("تعديل", "تم تفعيل وضع التعديل")
    
    def delete_record(self):
        """حذف السجل"""
        selection = self.tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى تحديد لون للحذف")
            return
        
        if messagebox.askyesno("تأكيد", "هل أنت متأكد من حذف هذا اللون؟"):
            self.tree.delete(selection[0])
            messagebox.showinfo("حذف", "تم حذف اللون بنجاح")
    
    def cancel_operation(self):
        """إلغاء العملية"""
        self.clear_form()
        messagebox.showinfo("إلغاء", "تم إلغاء العملية")
    
    def print_record(self):
        """طباعة السجل"""
        messagebox.showinfo("طباعة", "سيتم تطوير وظيفة الطباعة قريباً")
    
    def export_data(self):
        """تصدير البيانات"""
        messagebox.showinfo("تصدير", "سيتم تطوير وظيفة التصدير قريباً")
    
    def preview_color(self):
        """معاينة اللون"""
        hex_code = self.form_vars['hex_code'].get()
        if hex_code:
            try:
                self.color_display.configure(bg=hex_code)
                messagebox.showinfo("معاينة", f"تم عرض اللون: {hex_code}")
            except:
                messagebox.showerror("خطأ", "كود اللون غير صحيح")
        else:
            messagebox.showwarning("تحذير", "يرجى إدخال كود اللون أولاً")
    
    def clear_form(self):
        """مسح النموذج"""
        for var in self.form_vars.values():
            var.set("")
        self.color_display.configure(bg="white")
