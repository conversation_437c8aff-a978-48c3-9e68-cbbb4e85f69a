# توثيق الشاشات الكاملة - نظام إدارة التموين العام

## نظرة عامة

تم إنشاء أربع شاشات كاملة مطابقة للبرنامج الأصلي (ملف exe) مع جميع الحقول والأزرار والارتباطات:

1. **شاشة المعلومات الأساسية** (`basic_info_complete.py`)
2. **شاشة المعدات** (`equipment_complete.py`)
3. **شاشة الأصناف** (`categories_complete.py`)
4. **شاشة المستخدمين** (`users_complete.py`)
5. **شاشة النسخ الاحتياطي** (`backup_complete.py`)

## الميزات المشتركة

### التبويبات العلوية
جميع الشاشات تحتوي على نفس التبويبات:
- القوة العمومية
- المعدات
- الأصناف
- الأسلحة
- المستخدمين
- النسخ الاحتياطي
- أدوات الاستعلام

### شريط الأزرار
كل شاشة تحتوي على الأزرار التالية:
- **إضافة** (🆕): لإضافة سجل جديد
- **حفظ** (💾): لحفظ البيانات
- **تعديل** (✏️): لتعديل السجل المحدد
- **حذف** (🗑️): لحذف السجل المحدد
- **إلغاء** (❌): لإلغاء العملية الحالية

### جدول البيانات
- عرض البيانات في جدول منظم
- إمكانية التحديد والتنقل
- شريط تمرير عمودي
- تحديث تلقائي للبيانات

## 1. شاشة المعلومات الأساسية

### الحقول المتوفرة:
- **الرقم العسكري** (مطلوب)
- **الاسم الكامل** (مطلوب)
- **الرتبة** (مطلوب)
- **المسمى الوظيفي** (مطلوب)
- **الوحدة** (مطلوب)
- **رقم الهوية** (مطلوب)
- **رقم الجوال**
- **تاريخ الميلاد**
- **تاريخ التجنيد**

### أعمدة الجدول:
- الرقم
- الرقم العسكري
- الاسم
- الرتبة
- المسمى الوظيفي
- الوحدة
- رقم الهوية
- رقم الجوال

### الوظائف:
- إضافة وتعديل وحذف بيانات الأفراد
- البحث والتصفية
- التنقل بين التبويبات
- ربط مع قاعدة البيانات

## 2. شاشة المعدات

### الحقول المتوفرة:
- **الرقم المتسلسل** (مطلوب)
- **اسم المعدة** (مطلوب)
- **النوع** (مطلوب)
- **الطراز**
- **الشركة المصنعة**
- **سنة الصنع**
- **الحالة** (مطلوب)
- **الموقع**
- **تاريخ الاستلام**

### أعمدة الجدول:
- الرقم
- اسم القوة العمومية
- اسم المعدة
- رقم المعدة
- النوع
- الحالة
- الموقع

### الوظائف:
- إدارة معدات القوة العمومية
- تتبع حالة المعدات
- تسجيل تواريخ الصيانة
- ربط المعدات بالوحدات

## 3. شاشة الأصناف

### الحقول المتوفرة:
- **رقم المعدة** (مطلوب)
- **اسم المعدة** (مطلوب)
- **رقم الصنف** (مطلوب)
- **اسم الصنف** (مطلوب)
- **التشكيل** (مطلوب)
- **الكمية**
- **الوحدة**
- **الحد الأدنى**
- **الملاحظات**

### أعمدة الجدول:
- الرقم
- رقم المعدة
- اسم المعدة
- اسم القوة العمومية
- رقم الصنف
- اسم الصنف
- التشكيل

### الوظائف:
- إدارة أصناف المعدات
- تصنيف المعدات حسب النوع
- ربط الأصناف بالمعدات
- إدارة المخزون

## 4. شاشة المستخدمين

### الحقول المتوفرة:
- **الرقم العسكري** (مطلوب)
- **الاسم الكامل** (مطلوب)
- **الرتبة** (مطلوب)
- **الوظيفة** (مطلوب)
- **الوحدة** (مطلوب)
- **رقم الهوية** (مطلوب)
- **رقم الجوال**
- **تاريخ الميلاد**
- **تاريخ التجنيد**
- **اسم المستخدم** (مطلوب)
- **كلمة المرور** (مطلوب)
- **دور المستخدم** (مطلوب)
- **الحالة**
- **العنوان**
- **الملاحظات**

### أعمدة الجدول:
- الرقم
- الرقم العسكري
- الاسم
- الرتبة
- الوظيفة
- الوحدة
- رقم الهوية
- رقم الجوال

### الوظائف:
- إدارة حسابات المستخدمين
- تعيين الأدوار والصلاحيات
- تشفير كلمات المرور
- إدارة حالة المستخدمين

## 5. شاشة النسخ الاحتياطي

### أقسام الشاشة:

#### قسم إنشاء النسخة الاحتياطية:
- **اختيار مسار الحفظ**: تصفح المجلدات
- **خيارات النسخ**:
  - تضمين البيانات
  - تضمين الإعدادات
  - تضمين التقارير
- **أزرار العمليات**:
  - إنشاء نسخة احتياطية
  - نسخ سريع
  - نسخ مجدول

#### قسم الاستعادة:
- **اختيار ملف النسخة**: تصفح الملفات
- **خيارات الاستعادة**:
  - استعادة البيانات
  - استعادة الإعدادات
  - إنشاء نسخة احتياطية قبل الاستعادة
- **أزرار العمليات**:
  - استعادة النسخة
  - معاينة النسخة

#### قسم تاريخ النسخ:
- **جدول التاريخ** يعرض:
  - التاريخ
  - الوقت
  - نوع العملية
  - المسار
  - الحجم
  - الحالة
- **أزرار الإدارة**:
  - مسح التاريخ
  - تصدير التاريخ
  - تحديث

### الوظائف:
- إنشاء نسخ احتياطية شاملة
- استعادة البيانات بأمان
- جدولة النسخ الاحتياطي
- تتبع تاريخ العمليات

## التنقل بين الشاشات

### طرق التنقل:
1. **التبويبات العلوية**: النقر على أي تبويب ينقل للشاشة المقابلة
2. **شريط الأدوات**: الأزرار الرئيسية في الواجهة
3. **القوائم العلوية**: قوائم ملف، جهات الإضافة، الإعدادات، التقارير، مساعدة

### الارتباطات:
- جميع الشاشات مترابطة
- البيانات متزامنة بين الشاشات
- التحديثات تنعكس فوراً
- حفظ حالة التنقل

## قاعدة البيانات

### الجداول المرتبطة:
- `force_basic_info`: بيانات القوة العمومية
- `equipment`: بيانات المعدات
- `categories`: بيانات الأصناف
- `personnel`: بيانات المستخدمين
- `ranks`: الرتب
- `job_titles`: المسميات الوظيفية
- `units`: الوحدات

### العلاقات:
- ربط المعدات بالقوة العمومية
- ربط الأصناف بالمعدات
- ربط المستخدمين بالرتب والوحدات
- مفاتيح خارجية للحفاظ على سلامة البيانات

## الأمان والحماية

### تشفير البيانات:
- كلمات المرور مشفرة بـ SHA-256
- حماية من SQL Injection
- التحقق من صحة البيانات

### صلاحيات المستخدمين:
- مدير: جميع الصلاحيات
- مستخدم: صلاحيات محدودة
- مشاهد: قراءة فقط

### النسخ الاحتياطي:
- نسخ تلقائية مجدولة
- تشفير ملفات النسخ
- استعادة آمنة للبيانات

## التطوير المستقبلي

### الميزات المخططة:
- شاشة الأسلحة
- أدوات الاستعلام المتقدمة
- تقارير مفصلة
- واجهة ويب
- تطبيق جوال

### التحسينات:
- تحسين الأداء
- واجهة مستخدم محسنة
- ميزات بحث متقدمة
- تصدير متعدد الصيغ
