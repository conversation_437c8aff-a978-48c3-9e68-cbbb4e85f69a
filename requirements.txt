# متطلبات نظام إدارة التموين العام والقوة العمومية

# واجهة المستخدم الرسومية
tkinter
ttkbootstrap>=1.10.1

# قاعدة البيانات
sqlite3

# معالجة البيانات
pandas>=2.0.0
numpy>=1.24.0

# التقارير والمستندات
reportlab>=4.0.0
openpyxl>=3.1.0
xlsxwriter>=3.1.0

# معالجة الصور
Pillow>=10.0.0

# الرسوم البيانية
matplotlib>=3.7.0

# التواريخ والأوقات
python-dateutil>=2.8.2

# العمليات العلمية
scipy>=1.10.0

# أدوات إضافية
psutil>=5.9.0
requests>=2.31.0

# تحويل إلى ملف تنفيذي
pyinstaller>=5.13.0

# أدوات التطوير والاختبار
pytest>=7.4.0
black>=23.0.0
flake8>=6.0.0

# مكتبات إضافية للأداء
lxml>=4.9.0
cffi>=1.15.0

# دعم الشبكات والاتصالات
dnspython>=2.4.0
gevent>=23.0.0

# معالجة JSON والبيانات المنظمة
jsonschema>=4.19.0

# أدوات النظام
wheel>=0.41.0
setuptools>=68.0.0

# مكتبات الأمان والتشفير
cryptography>=41.0.0

# دعم اللغات والترجمة
babel>=2.12.0

# أدوات إضافية للواجهة
tkinter-tooltip>=2.1.0
