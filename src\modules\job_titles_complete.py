#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
وحدة إدارة المسميات الوظيفية الكاملة مطابقة للبرنامج الأصلي
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import ttkbootstrap as ttk_bs
from ttkbootstrap.constants import *
from datetime import datetime

class JobTitlesCompleteModule:
    def __init__(self, parent_frame, db_manager):
        self.parent = parent_frame
        self.db_manager = db_manager
        self.current_record = None
        self.form_vars = {}
        
        # إنشاء الواجهة
        self.create_interface()
        
        # تحميل البيانات
        self.load_data()
    
    def create_interface(self):
        """إنشاء واجهة إدارة المسميات الوظيفية مطابقة للبرنامج الأصلي"""
        # مسح المحتوى السابق
        for widget in self.parent.winfo_children():
            widget.destroy()
        
        # الإطار الرئيسي
        main_frame = ttk_bs.Frame(self.parent)
        main_frame.pack(fill=BOTH, expand=True, padx=10, pady=10)
        
        # العنوان الرئيسي
        title_frame = ttk_bs.Frame(main_frame)
        title_frame.pack(fill=X, pady=(0, 10))
        
        title_label = ttk_bs.Label(
            title_frame,
            text="إدارة المسميات الوظيفية",
            font=("Tahoma", 16, "bold"),
            bootstyle="primary"
        )
        title_label.pack()
        
        # إنشاء شريط الأزرار
        self.create_buttons_bar(main_frame)
        
        # إنشاء منطقة الإدخال
        self.create_input_area(main_frame)
        
        # إنشاء جدول البيانات
        self.create_data_table(main_frame)
    
    def create_buttons_bar(self, parent):
        """إنشاء شريط الأزرار"""
        buttons_frame = ttk_bs.Frame(parent)
        buttons_frame.pack(fill=X, pady=(0, 10))
        
        # الأزرار مطابقة للبرنامج الأصلي
        buttons = [
            ("إضافة", "add", "success", "🆕"),
            ("حفظ", "save", "primary", "💾"),
            ("تعديل", "edit", "warning", "✏️"),
            ("حذف", "delete", "danger", "🗑️"),
            ("إلغاء", "cancel", "secondary", "❌"),
            ("طباعة", "print", "info", "🖨️"),
            ("تصدير", "export", "info", "📤")
        ]
        
        for text, action, style, icon in buttons:
            btn = ttk_bs.Button(
                buttons_frame,
                text=f"{icon} {text}",
                command=lambda a=action: self.handle_button_action(a),
                bootstyle=style,
                width=12
            )
            btn.pack(side=LEFT, padx=2)
    
    def create_input_area(self, parent):
        """إنشاء منطقة الإدخال مطابقة للبرنامج الأصلي"""
        input_frame = ttk_bs.LabelFrame(parent, text="بيانات المسمى الوظيفي", padding=10)
        input_frame.pack(fill=X, pady=(0, 10))
        
        # الصف الأول من الحقول
        row1_frame = ttk_bs.Frame(input_frame)
        row1_frame.pack(fill=X, pady=5)
        
        # رقم المسمى الوظيفي
        ttk_bs.Label(row1_frame, text="* رقم المسمى:").pack(side=RIGHT, padx=5)
        self.form_vars['job_number'] = ttk_bs.StringVar()
        job_number_entry = ttk_bs.Entry(row1_frame, textvariable=self.form_vars['job_number'], width=10)
        job_number_entry.pack(side=RIGHT, padx=5)
        
        # اسم المسمى الوظيفي
        ttk_bs.Label(row1_frame, text="* اسم المسمى الوظيفي:").pack(side=RIGHT, padx=5)
        self.form_vars['job_title'] = ttk_bs.StringVar()
        job_title_entry = ttk_bs.Entry(row1_frame, textvariable=self.form_vars['job_title'], width=30)
        job_title_entry.pack(side=RIGHT, padx=5)
        
        # الصف الثاني من الحقول
        row2_frame = ttk_bs.Frame(input_frame)
        row2_frame.pack(fill=X, pady=5)
        
        # فئة المسمى
        ttk_bs.Label(row2_frame, text="* فئة المسمى:").pack(side=RIGHT, padx=5)
        self.form_vars['job_category'] = ttk_bs.StringVar()
        category_combo = ttk_bs.Combobox(row2_frame, textvariable=self.form_vars['job_category'], width=20)
        category_combo['values'] = ("إداري", "فني", "قيادي", "استشاري", "تنفيذي", "إشرافي")
        category_combo.pack(side=RIGHT, padx=5)
        
        # مستوى المسمى
        ttk_bs.Label(row2_frame, text="* مستوى المسمى:").pack(side=RIGHT, padx=5)
        self.form_vars['job_level'] = ttk_bs.StringVar()
        level_combo = ttk_bs.Combobox(row2_frame, textvariable=self.form_vars['job_level'], width=15)
        level_combo['values'] = ("أول", "ثاني", "ثالث", "رابع", "خامس")
        level_combo.pack(side=RIGHT, padx=5)
        
        # الصف الثالث من الحقول
        row3_frame = ttk_bs.Frame(input_frame)
        row3_frame.pack(fill=X, pady=5)
        
        # القسم
        ttk_bs.Label(row3_frame, text="القسم:").pack(side=RIGHT, padx=5)
        self.form_vars['department'] = ttk_bs.StringVar()
        department_combo = ttk_bs.Combobox(row3_frame, textvariable=self.form_vars['department'], width=20)
        department_combo.pack(side=RIGHT, padx=5)
        
        # الإدارة
        ttk_bs.Label(row3_frame, text="الإدارة:").pack(side=RIGHT, padx=5)
        self.form_vars['administration'] = ttk_bs.StringVar()
        admin_combo = ttk_bs.Combobox(row3_frame, textvariable=self.form_vars['administration'], width=20)
        admin_combo.pack(side=RIGHT, padx=5)
        
        # الصف الرابع من الحقول
        row4_frame = ttk_bs.Frame(input_frame)
        row4_frame.pack(fill=X, pady=5)
        
        # المؤهل المطلوب
        ttk_bs.Label(row4_frame, text="المؤهل المطلوب:").pack(side=RIGHT, padx=5)
        self.form_vars['required_qualification'] = ttk_bs.StringVar()
        qualification_combo = ttk_bs.Combobox(row4_frame, textvariable=self.form_vars['required_qualification'], width=20)
        qualification_combo['values'] = ("ابتدائي", "متوسط", "ثانوي", "دبلوم", "بكالوريوس", "ماجستير", "دكتوراه")
        qualification_combo.pack(side=RIGHT, padx=5)
        
        # سنوات الخبرة المطلوبة
        ttk_bs.Label(row4_frame, text="سنوات الخبرة:").pack(side=RIGHT, padx=5)
        self.form_vars['required_experience'] = ttk_bs.StringVar()
        experience_entry = ttk_bs.Entry(row4_frame, textvariable=self.form_vars['required_experience'], width=10)
        experience_entry.pack(side=RIGHT, padx=5)
        
        # الصف الخامس من الحقول
        row5_frame = ttk_bs.Frame(input_frame)
        row5_frame.pack(fill=X, pady=5)
        
        # الراتب الأساسي
        ttk_bs.Label(row5_frame, text="الراتب الأساسي:").pack(side=RIGHT, padx=5)
        self.form_vars['basic_salary'] = ttk_bs.StringVar()
        salary_entry = ttk_bs.Entry(row5_frame, textvariable=self.form_vars['basic_salary'], width=15)
        salary_entry.pack(side=RIGHT, padx=5)
        
        # بدل الوظيفة
        ttk_bs.Label(row5_frame, text="بدل الوظيفة:").pack(side=RIGHT, padx=5)
        self.form_vars['job_allowance'] = ttk_bs.StringVar()
        allowance_entry = ttk_bs.Entry(row5_frame, textvariable=self.form_vars['job_allowance'], width=15)
        allowance_entry.pack(side=RIGHT, padx=5)
        
        # الصف السادس من الحقول
        row6_frame = ttk_bs.Frame(input_frame)
        row6_frame.pack(fill=X, pady=5)
        
        # الحالة
        ttk_bs.Label(row6_frame, text="الحالة:").pack(side=RIGHT, padx=5)
        self.form_vars['status'] = ttk_bs.StringVar()
        status_combo = ttk_bs.Combobox(row6_frame, textvariable=self.form_vars['status'], width=12)
        status_combo['values'] = ("نشط", "غير نشط", "ملغي")
        status_combo.pack(side=RIGHT, padx=5)
        
        # تاريخ الإنشاء
        ttk_bs.Label(row6_frame, text="تاريخ الإنشاء:").pack(side=RIGHT, padx=5)
        self.form_vars['creation_date'] = ttk_bs.StringVar()
        creation_date_entry = ttk_bs.Entry(row6_frame, textvariable=self.form_vars['creation_date'], width=12)
        creation_date_entry.pack(side=RIGHT, padx=5)
        
        # الصف السابع - الوصف
        row7_frame = ttk_bs.Frame(input_frame)
        row7_frame.pack(fill=X, pady=5)
        
        ttk_bs.Label(row7_frame, text="الوصف:").pack(side=RIGHT, padx=5)
        self.form_vars['description'] = ttk_bs.StringVar()
        description_entry = ttk_bs.Entry(row7_frame, textvariable=self.form_vars['description'], width=50)
        description_entry.pack(side=RIGHT, padx=5, fill=X, expand=True)
        
        # الصف الثامن - المهام والمسؤوليات
        row8_frame = ttk_bs.Frame(input_frame)
        row8_frame.pack(fill=X, pady=5)
        
        ttk_bs.Label(row8_frame, text="المهام والمسؤوليات:").pack(side=RIGHT, padx=5)
        self.form_vars['responsibilities'] = ttk_bs.StringVar()
        responsibilities_entry = ttk_bs.Entry(row8_frame, textvariable=self.form_vars['responsibilities'], width=50)
        responsibilities_entry.pack(side=RIGHT, padx=5, fill=X, expand=True)
        
        # الصف التاسع - الملاحظات
        row9_frame = ttk_bs.Frame(input_frame)
        row9_frame.pack(fill=X, pady=5)
        
        ttk_bs.Label(row9_frame, text="الملاحظات:").pack(side=RIGHT, padx=5)
        self.form_vars['notes'] = ttk_bs.StringVar()
        notes_entry = ttk_bs.Entry(row9_frame, textvariable=self.form_vars['notes'], width=50)
        notes_entry.pack(side=RIGHT, padx=5, fill=X, expand=True)
        
        # تحميل البيانات في القوائم المنسدلة
        self.load_combo_data(department_combo, admin_combo)
    
    def create_data_table(self, parent):
        """إنشاء جدول البيانات"""
        table_frame = ttk_bs.LabelFrame(parent, text="قائمة المسميات الوظيفية", padding=10)
        table_frame.pack(fill=BOTH, expand=True)
        
        # إنشاء Treeview
        columns = ("الرقم", "رقم المسمى", "اسم المسمى الوظيفي", "فئة المسمى", "مستوى المسمى", "القسم", "المؤهل المطلوب", "الحالة")
        self.tree = ttk_bs.Treeview(table_frame, columns=columns, show="headings", height=10)
        
        # تعيين عناوين الأعمدة
        for col in columns:
            self.tree.heading(col, text=col)
            self.tree.column(col, width=120, anchor=CENTER)
        
        # شريط التمرير
        scrollbar = ttk_bs.Scrollbar(table_frame, orient=VERTICAL, command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)
        
        # تخطيط الجدول
        self.tree.pack(side=LEFT, fill=BOTH, expand=True)
        scrollbar.pack(side=RIGHT, fill=Y)
        
        # ربط حدث النقر
        self.tree.bind("<<TreeviewSelect>>", self.on_item_select)
    
    def handle_button_action(self, action):
        """معالجة أحداث الأزرار"""
        if action == "add":
            self.add_record()
        elif action == "save":
            self.save_record()
        elif action == "edit":
            self.edit_record()
        elif action == "delete":
            self.delete_record()
        elif action == "cancel":
            self.cancel_operation()
        elif action == "print":
            self.print_record()
        elif action == "export":
            self.export_data()
    
    def load_combo_data(self, department_combo, admin_combo):
        """تحميل بيانات القوائم المنسدلة"""
        try:
            # تحميل الأقسام
            departments = ["قسم الإدارة", "قسم المالية", "قسم الموارد البشرية", "قسم التقنية", "قسم العمليات"]
            department_combo['values'] = departments
            
            # تحميل الإدارات
            administrations = ["إدارة التخطيط", "إدارة التنفيذ", "إدارة المتابعة", "إدارة التطوير"]
            admin_combo['values'] = administrations
            
        except Exception as e:
            print(f"خطأ في تحميل بيانات القوائم: {e}")
    
    def load_data(self):
        """تحميل البيانات في الجدول"""
        try:
            # مسح البيانات الحالية
            for item in self.tree.get_children():
                self.tree.delete(item)
            
            # تحميل البيانات من قاعدة البيانات
            query = """
                SELECT *
                FROM job_titles
                WHERE is_active = 1
                ORDER BY job_level, id
            """
            records = self.db_manager.execute_query(query)
            
            for i, record in enumerate(records, 1):
                values = (
                    i,
                    record.get('job_number', ''),
                    record.get('job_title', ''),
                    record.get('job_category', ''),
                    record.get('job_level', ''),
                    record.get('department', ''),
                    record.get('required_qualification', ''),
                    record.get('status', '')
                )
                self.tree.insert("", "end", values=values)
                
        except Exception as e:
            print(f"خطأ في تحميل البيانات: {e}")
    
    def on_item_select(self, event):
        """معالجة تحديد عنصر في الجدول"""
        selection = self.tree.selection()
        if selection:
            item = self.tree.item(selection[0])
            values = item['values']
            
            # تحديث الحقول بالبيانات المحددة
            if len(values) >= 8:
                self.form_vars['job_number'].set(values[1])
                self.form_vars['job_title'].set(values[2])
                self.form_vars['job_category'].set(values[3])
                self.form_vars['job_level'].set(values[4])
                self.form_vars['department'].set(values[5])
                self.form_vars['required_qualification'].set(values[6])
                self.form_vars['status'].set(values[7])
    
    def add_record(self):
        """إضافة سجل جديد"""
        self.clear_form()
        messagebox.showinfo("إضافة", "تم تفعيل وضع الإضافة")
    
    def save_record(self):
        """حفظ السجل"""
        try:
            # جمع البيانات من النموذج
            data = {field: var.get().strip() for field, var in self.form_vars.items()}
            
            # التحقق من البيانات المطلوبة
            required_fields = ['job_number', 'job_title', 'job_category', 'job_level']
            missing_fields = [field for field in required_fields if not data[field]]
            
            if missing_fields:
                messagebox.showerror("خطأ", "يرجى ملء جميع الحقول المطلوبة")
                return
            
            # إنشاء استعلام الإدراج
            query = """
                INSERT INTO job_titles 
                (job_number, job_title, job_category, job_level, department, administration,
                 required_qualification, required_experience, basic_salary, job_allowance,
                 status, creation_date, description, responsibilities, notes, is_active)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 1)
            """
            
            params = tuple(data[field] for field in [
                'job_number', 'job_title', 'job_category', 'job_level', 'department',
                'administration', 'required_qualification', 'required_experience',
                'basic_salary', 'job_allowance', 'status', 'creation_date',
                'description', 'responsibilities', 'notes'
            ])
            
            # تنفيذ الاستعلام
            self.db_manager.execute_query(query, params)
            messagebox.showinfo("نجح", "تم حفظ بيانات المسمى الوظيفي بنجاح")
            
            # إعادة تحميل البيانات
            self.load_data()
            self.clear_form()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في حفظ البيانات: {e}")
    
    def edit_record(self):
        """تعديل السجل"""
        selection = self.tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى تحديد مسمى وظيفي للتعديل")
            return
        
        messagebox.showinfo("تعديل", "تم تفعيل وضع التعديل")
    
    def delete_record(self):
        """حذف السجل"""
        selection = self.tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى تحديد مسمى وظيفي للحذف")
            return
        
        if messagebox.askyesno("تأكيد", "هل أنت متأكد من حذف هذا المسمى الوظيفي؟"):
            self.tree.delete(selection[0])
            messagebox.showinfo("حذف", "تم حذف المسمى الوظيفي بنجاح")
    
    def cancel_operation(self):
        """إلغاء العملية"""
        self.clear_form()
        messagebox.showinfo("إلغاء", "تم إلغاء العملية")
    
    def print_record(self):
        """طباعة السجل"""
        messagebox.showinfo("طباعة", "سيتم تطوير وظيفة الطباعة قريباً")
    
    def export_data(self):
        """تصدير البيانات"""
        messagebox.showinfo("تصدير", "سيتم تطوير وظيفة التصدير قريباً")
    
    def clear_form(self):
        """مسح النموذج"""
        for var in self.form_vars.values():
            var.set("")
