#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
مدير البيانات الأساسية
"""

import tkinter as tk
from tkinter import messagebox, ttk
import ttkbootstrap as ttk_bs
from ttkbootstrap.constants import *
from typing import Dict, Any, List, Optional
import os


class BaseDataManager:
    """فئة إدارة البيانات الأساسية"""
    
    def __init__(self, parent_frame, db_manager, logger, table_name: str, 
                 title: str, columns: List[Dict[str, Any]]):
        """
        تهيئة مدير البيانات الأساسية
        
        Args:
            parent_frame: الإطار الأب
            db_manager: مدير قاعدة البيانات
            logger: نظام السجلات
            table_name: اسم الجدول
            title: عنوان الوحدة
            columns: تعريف الأعمدة
        """
        self.parent_frame = parent_frame
        self.db_manager = db_manager
        self.logger = logger
        self.table_name = table_name
        self.title = title
        self.columns = columns
        
        self.main_frame = None
        self.tree = None
        self.search_var = None
        self.selected_record = None
        
        # إنشاء الواجهة
        self.create_interface()
        
        # تحميل البيانات
        self.load_data()
    
    def create_interface(self) -> None:
        """إنشاء واجهة المستخدم"""
        
        # الإطار الرئيسي
        self.main_frame = ttk_bs.Frame(self.parent_frame)
        self.main_frame.pack(fill=BOTH, expand=True, padx=10, pady=10)
        
        # عنوان الوحدة
        title_label = ttk_bs.Label(
            self.main_frame,
            text=self.title,
            font=("Tahoma", 16, "bold"),
            bootstyle="primary"
        )
        title_label.pack(pady=(0, 20))
        
        # شريط الأدوات
        self.create_toolbar()
        
        # منطقة البحث
        self.create_search_area()
        
        # جدول البيانات
        self.create_data_table()
        
        # شريط الحالة
        self.create_status_bar()
    
    def create_toolbar(self) -> None:
        """إنشاء شريط الأدوات"""
        toolbar = ttk_bs.Frame(self.main_frame)
        toolbar.pack(fill=X, pady=(0, 10))
        
        # أزرار العمليات
        buttons = [
            ("إضافة", self.add_record, "success"),
            ("تعديل", self.edit_record, "primary"),
            ("حذف", self.delete_record, "danger"),
            ("تحديث", self.refresh_data, "info"),
            ("تصدير", self.export_data, "secondary")
        ]
        
        for text, command, style in buttons:
            btn = ttk_bs.Button(
                toolbar,
                text=text,
                command=command,
                bootstyle=style,
                width=10
            )
            btn.pack(side=LEFT, padx=2)
    
    def create_search_area(self) -> None:
        """إنشاء منطقة البحث"""
        search_frame = ttk_bs.LabelFrame(
            self.main_frame,
            text="البحث والتصفية",
            padding=10
        )
        search_frame.pack(fill=X, pady=(0, 10))
        
        # متغير البحث
        self.search_var = tk.StringVar()
        self.search_var.trace('w', self.on_search_change)
        
        # حقل البحث
        search_label = ttk_bs.Label(search_frame, text="البحث:")
        search_label.pack(side=LEFT, padx=(0, 5))
        
        search_entry = ttk_bs.Entry(
            search_frame,
            textvariable=self.search_var,
            width=30
        )
        search_entry.pack(side=LEFT, padx=(0, 10))
        
        # زر البحث
        search_btn = ttk_bs.Button(
            search_frame,
            text="بحث",
            command=self.search_data,
            bootstyle="outline-primary",
            width=8
        )
        search_btn.pack(side=LEFT, padx=2)
        
        # زر مسح البحث
        clear_btn = ttk_bs.Button(
            search_frame,
            text="مسح",
            command=self.clear_search,
            bootstyle="outline-secondary",
            width=8
        )
        clear_btn.pack(side=LEFT, padx=2)
    
    def create_data_table(self) -> None:
        """إنشاء جدول البيانات"""
        table_frame = ttk_bs.Frame(self.main_frame)
        table_frame.pack(fill=BOTH, expand=True, pady=(0, 10))
        
        # إعداد الأعمدة
        column_ids = [col['id'] for col in self.columns]
        
        # إنشاء Treeview
        self.tree = ttk.Treeview(
            table_frame,
            columns=column_ids,
            show='headings',
            height=15
        )
        
        # تعيين عناوين الأعمدة
        for col in self.columns:
            self.tree.heading(col['id'], text=col['text'])
            self.tree.column(col['id'], width=col.get('width', 100), anchor=col.get('anchor', 'center'))
        
        # شريط التمرير العمودي
        v_scrollbar = ttk.Scrollbar(table_frame, orient=VERTICAL, command=self.tree.yview)
        self.tree.configure(yscrollcommand=v_scrollbar.set)
        
        # شريط التمرير الأفقي
        h_scrollbar = ttk.Scrollbar(table_frame, orient=HORIZONTAL, command=self.tree.xview)
        self.tree.configure(xscrollcommand=h_scrollbar.set)
        
        # تخطيط الجدول
        self.tree.grid(row=0, column=0, sticky="nsew")
        v_scrollbar.grid(row=0, column=1, sticky="ns")
        h_scrollbar.grid(row=1, column=0, sticky="ew")
        
        # تكوين الشبكة
        table_frame.grid_rowconfigure(0, weight=1)
        table_frame.grid_columnconfigure(0, weight=1)
        
        # ربط الأحداث
        self.tree.bind('<<TreeviewSelect>>', self.on_select)
        self.tree.bind('<Double-1>', lambda e: self.edit_record())
    
    def create_status_bar(self) -> None:
        """إنشاء شريط الحالة"""
        self.status_frame = ttk_bs.Frame(self.main_frame)
        self.status_frame.pack(fill=X)
        
        self.status_label = ttk_bs.Label(
            self.status_frame,
            text="جاهز",
            bootstyle="secondary"
        )
        self.status_label.pack(side=LEFT)
        
        self.count_label = ttk_bs.Label(
            self.status_frame,
            text="",
            bootstyle="secondary"
        )
        self.count_label.pack(side=RIGHT)
    
    def load_data(self, search_term: str = "") -> None:
        """تحميل البيانات"""
        try:
            # مسح البيانات الحالية
            for item in self.tree.get_children():
                self.tree.delete(item)
            
            # الحصول على البيانات
            if search_term:
                # البحث في الأعمدة القابلة للبحث
                search_columns = [col['id'] for col in self.columns if col.get('searchable', True)]
                records = self.db_manager.search_records(self.table_name, search_term, search_columns)
            else:
                records = self.db_manager.get_table_data(self.table_name)
            
            # إدراج البيانات في الجدول
            for record in records:
                values = []
                for col in self.columns:
                    value = record.get(col['id'], '')
                    # تنسيق القيم حسب النوع
                    if col.get('type') == 'date' and value:
                        try:
                            from datetime import datetime
                            dt = datetime.fromisoformat(value.replace('Z', '+00:00'))
                            value = dt.strftime('%Y-%m-%d %H:%M')
                        except:
                            pass
                    elif col.get('type') == 'boolean':
                        value = "نعم" if value else "لا"
                    
                    values.append(str(value))
                
                self.tree.insert('', 'end', values=values, tags=(record['id'],))
            
            # تحديث العداد
            count = len(records)
            self.count_label.config(text=f"إجمالي السجلات: {count}")
            self.status_label.config(text="تم تحميل البيانات بنجاح")
            
        except Exception as e:
            self.logger.error(f"خطأ في تحميل بيانات {self.table_name}: {e}")
            messagebox.showerror("خطأ", f"فشل في تحميل البيانات: {e}")
            self.status_label.config(text="خطأ في تحميل البيانات")
    
    def on_select(self, event) -> None:
        """معالج تحديد السجل"""
        selection = self.tree.selection()
        if selection:
            item = self.tree.item(selection[0])
            record_id = item['tags'][0] if item['tags'] else None
            if record_id:
                self.selected_record = self.db_manager.get_record_by_id(self.table_name, record_id)
    
    def on_search_change(self, *args) -> None:
        """معالج تغيير البحث"""
        # تأخير البحث لتحسين الأداء
        if hasattr(self, '_search_timer'):
            self.main_frame.after_cancel(self._search_timer)
        
        self._search_timer = self.main_frame.after(500, self.search_data)
    
    def search_data(self) -> None:
        """البحث في البيانات"""
        search_term = self.search_var.get().strip()
        self.load_data(search_term)
    
    def clear_search(self) -> None:
        """مسح البحث"""
        self.search_var.set("")
        self.load_data()
    
    def refresh_data(self) -> None:
        """تحديث البيانات"""
        self.load_data(self.search_var.get().strip())
        self.status_label.config(text="تم تحديث البيانات")
    
    def add_record(self) -> None:
        """إضافة سجل جديد"""
        dialog = RecordDialog(
            self.main_frame,
            self.columns,
            "إضافة سجل جديد",
            None
        )
        
        if dialog.result:
            try:
                # إدراج السجل
                record_id = self.db_manager.insert_record(self.table_name, dialog.result)
                
                # تحديث البيانات
                self.refresh_data()
                
                # تسجيل العملية
                self.logger.log_user_action(
                    "المستخدم",
                    f"إضافة {self.title}",
                    f"تم إضافة سجل جديد برقم {record_id}"
                )
                
                messagebox.showinfo("نجح", "تم إضافة السجل بنجاح")
                
            except Exception as e:
                self.logger.error(f"خطأ في إضافة سجل في {self.table_name}: {e}")
                messagebox.showerror("خطأ", f"فشل في إضافة السجل: {e}")
    
    def edit_record(self) -> None:
        """تعديل السجل المحدد"""
        if not self.selected_record:
            messagebox.showwarning("تحذير", "يرجى تحديد سجل للتعديل")
            return
        
        dialog = RecordDialog(
            self.main_frame,
            self.columns,
            "تعديل السجل",
            self.selected_record
        )
        
        if dialog.result:
            try:
                # تحديث السجل
                success = self.db_manager.update_record(
                    self.table_name,
                    self.selected_record['id'],
                    dialog.result
                )
                
                if success:
                    # تحديث البيانات
                    self.refresh_data()
                    
                    # تسجيل العملية
                    self.logger.log_user_action(
                        "المستخدم",
                        f"تعديل {self.title}",
                        f"تم تعديل السجل رقم {self.selected_record['id']}"
                    )
                    
                    messagebox.showinfo("نجح", "تم تعديل السجل بنجاح")
                else:
                    messagebox.showerror("خطأ", "فشل في تعديل السجل")
                
            except Exception as e:
                self.logger.error(f"خطأ في تعديل سجل في {self.table_name}: {e}")
                messagebox.showerror("خطأ", f"فشل في تعديل السجل: {e}")
    
    def delete_record(self) -> None:
        """حذف السجل المحدد"""
        if not self.selected_record:
            messagebox.showwarning("تحذير", "يرجى تحديد سجل للحذف")
            return
        
        # تأكيد الحذف
        if not messagebox.askyesno("تأكيد الحذف", "هل تريد حذف السجل المحدد؟"):
            return
        
        try:
            # حذف السجل
            success = self.db_manager.delete_record(self.table_name, self.selected_record['id'])
            
            if success:
                # تحديث البيانات
                self.refresh_data()
                
                # تسجيل العملية
                self.logger.log_user_action(
                    "المستخدم",
                    f"حذف {self.title}",
                    f"تم حذف السجل رقم {self.selected_record['id']}"
                )
                
                messagebox.showinfo("نجح", "تم حذف السجل بنجاح")
                self.selected_record = None
            else:
                messagebox.showerror("خطأ", "فشل في حذف السجل")
            
        except Exception as e:
            self.logger.error(f"خطأ في حذف سجل من {self.table_name}: {e}")
            messagebox.showerror("خطأ", f"فشل في حذف السجل: {e}")
    
    def export_data(self) -> None:
        """تصدير البيانات"""
        messagebox.showinfo("تصدير", "سيتم تطوير ميزة التصدير قريباً")


class RecordDialog:
    """حوار إدخال/تعديل السجل"""
    
    def __init__(self, parent, columns: List[Dict[str, Any]], title: str, record_data: Optional[Dict[str, Any]]):
        """
        تهيئة الحوار
        
        Args:
            parent: النافذة الأب
            columns: تعريف الأعمدة
            title: عنوان الحوار
            record_data: بيانات السجل للتعديل (None للإضافة)
        """
        self.parent = parent
        self.columns = [col for col in columns if col.get('editable', True)]
        self.title = title
        self.record_data = record_data
        self.result = None
        self.fields = {}
        
        # إنشاء الحوار
        self.create_dialog()
    
    def create_dialog(self) -> None:
        """إنشاء الحوار"""
        # النافذة
        self.dialog = tk.Toplevel(self.parent)
        self.dialog.title(self.title)
        self.dialog.geometry("400x500")
        self.dialog.resizable(False, False)
        self.dialog.transient(self.parent)
        self.dialog.grab_set()
        
        # توسيط النافذة
        self.center_dialog()
        
        # الإطار الرئيسي
        main_frame = ttk_bs.Frame(self.dialog, padding=20)
        main_frame.pack(fill=BOTH, expand=True)
        
        # إطار الحقول
        fields_frame = ttk_bs.Frame(main_frame)
        fields_frame.pack(fill=BOTH, expand=True, pady=(0, 20))
        
        # إنشاء الحقول
        self.create_fields(fields_frame)
        
        # إطار الأزرار
        buttons_frame = ttk_bs.Frame(main_frame)
        buttons_frame.pack(fill=X)
        
        # أزرار الحوار
        save_btn = ttk_bs.Button(
            buttons_frame,
            text="حفظ",
            command=self.save_record,
            bootstyle="success",
            width=12
        )
        save_btn.pack(side=RIGHT, padx=(10, 0))
        
        cancel_btn = ttk_bs.Button(
            buttons_frame,
            text="إلغاء",
            command=self.cancel,
            bootstyle="secondary",
            width=12
        )
        cancel_btn.pack(side=RIGHT)
        
        # ربط مفاتيح الاختصار
        self.dialog.bind('<Return>', lambda e: self.save_record())
        self.dialog.bind('<Escape>', lambda e: self.cancel())
        
        # انتظار إغلاق الحوار
        self.dialog.wait_window()
    
    def center_dialog(self) -> None:
        """توسيط الحوار"""
        self.dialog.update_idletasks()
        
        # الحصول على أبعاد الشاشة
        screen_width = self.dialog.winfo_screenwidth()
        screen_height = self.dialog.winfo_screenheight()
        
        # الحصول على أبعاد الحوار
        dialog_width = self.dialog.winfo_width()
        dialog_height = self.dialog.winfo_height()
        
        # حساب الموضع
        x = (screen_width - dialog_width) // 2
        y = (screen_height - dialog_height) // 2
        
        # تعيين الموضع
        self.dialog.geometry(f"{dialog_width}x{dialog_height}+{x}+{y}")
    
    def create_fields(self, parent) -> None:
        """إنشاء حقول الإدخال"""
        row = 0
        
        for col in self.columns:
            if col['id'] in ['id', 'created_at', 'updated_at']:
                continue
            
            # تسمية الحقل
            label = ttk_bs.Label(
                parent,
                text=f"{col['text']}:",
                font=("Tahoma", 10)
            )
            label.grid(row=row, column=0, sticky=W, pady=(0, 5), padx=(0, 10))
            
            # حقل الإدخال
            field_type = col.get('type', 'text')
            
            if field_type == 'boolean':
                # خانة اختيار
                var = tk.BooleanVar()
                field = ttk_bs.Checkbutton(parent, variable=var)
                if self.record_data:
                    var.set(bool(self.record_data.get(col['id'], False)))
                self.fields[col['id']] = var
                
            elif field_type == 'choice' and 'choices' in col:
                # قائمة منسدلة
                var = tk.StringVar()
                field = ttk_bs.Combobox(
                    parent,
                    textvariable=var,
                    values=col['choices'],
                    state="readonly",
                    width=25
                )
                if self.record_data:
                    var.set(self.record_data.get(col['id'], ''))
                self.fields[col['id']] = var
                
            else:
                # حقل نص
                var = tk.StringVar()
                field = ttk_bs.Entry(parent, textvariable=var, width=28)
                if self.record_data:
                    var.set(str(self.record_data.get(col['id'], '')))
                self.fields[col['id']] = var
            
            field.grid(row=row+1, column=0, sticky=EW, pady=(0, 15))
            row += 2
        
        # تكوين الشبكة
        parent.columnconfigure(0, weight=1)
    
    def save_record(self) -> None:
        """حفظ السجل"""
        try:
            # جمع البيانات
            data = {}
            for field_id, field_var in self.fields.items():
                value = field_var.get()
                
                # التحقق من الحقول المطلوبة
                col = next((c for c in self.columns if c['id'] == field_id), None)
                if col and col.get('required', False) and not value:
                    messagebox.showerror("خطأ", f"الحقل '{col['text']}' مطلوب")
                    return
                
                data[field_id] = value
            
            self.result = data
            self.dialog.destroy()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في حفظ البيانات: {e}")
    
    def cancel(self) -> None:
        """إلغاء الحوار"""
        self.result = None
        self.dialog.destroy()
