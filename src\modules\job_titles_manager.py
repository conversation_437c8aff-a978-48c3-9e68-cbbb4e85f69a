#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
مدير المسميات الوظيفية
"""

from .base_data_manager import BaseDataManager


class JobTitlesManager(BaseDataManager):
    """فئة إدارة المسميات الوظيفية"""
    
    def __init__(self, parent_frame, db_manager, logger):
        """
        تهيئة مدير المسميات الوظيفية
        
        Args:
            parent_frame: الإطار الأب
            db_manager: مدير قاعدة البيانات
            logger: نظام السجلات
        """
        
        # تعريف أعمدة جدول المسميات الوظيفية
        columns = [
            {
                'id': 'id',
                'text': 'المعرف',
                'width': 80,
                'anchor': 'center',
                'editable': False,
                'searchable': False
            },
            {
                'id': 'name',
                'text': 'المسمى الوظيفي',
                'width': 300,
                'anchor': 'w',
                'editable': True,
                'searchable': True,
                'required': True,
                'type': 'text'
            },
            {
                'id': 'description',
                'text': 'الوصف',
                'width': 350,
                'anchor': 'w',
                'editable': True,
                'searchable': True,
                'required': False,
                'type': 'text'
            },
            {
                'id': 'created_at',
                'text': 'تاريخ الإنشاء',
                'width': 150,
                'anchor': 'center',
                'editable': False,
                'searchable': False,
                'type': 'date'
            }
        ]
        
        # استدعاء الفئة الأساسية
        super().__init__(
            parent_frame=parent_frame,
            db_manager=db_manager,
            logger=logger,
            table_name="job_titles",
            title="إدارة المسميات الوظيفية",
            columns=columns
        )


def create_job_titles_manager(parent_frame, db_manager, logger):
    """إنشاء مدير المسميات الوظيفية"""
    return JobTitlesManager(parent_frame, db_manager, logger)
