#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
نظام إدارة التموين العام والقوة العمومية
الملف الرئيسي للتطبيق

هذا النظام يوفر إدارة شاملة للتموين العام والقوة العمومية
يشمل إدارة الأفراد والمعدات والعربات والأسلحة والتقارير
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox
import ttkbootstrap as ttk_bs
from ttkbootstrap.constants import *

# إضافة مسار src إلى Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# استيراد الوحدات المطلوبة
try:
    from database.database_manager import DatabaseManager
    from ui.login_window import LoginWindow
    from ui.main_window import MainWindow
    from utils.config import Config
    from utils.logger import Logger
except ImportError as e:
    print(f"خطأ في استيراد الوحدات: {e}")
    sys.exit(1)


class SupplyManagementApp:
    """الفئة الرئيسية للتطبيق"""
    
    def __init__(self):
        """تهيئة التطبيق"""
        self.db_manager = None
        self.main_window = None
        self.config = Config()
        self.logger = Logger()
        
        # تهيئة قاعدة البيانات
        self.init_database()
        
    def init_database(self):
        """تهيئة قاعدة البيانات"""
        try:
            db_path = os.path.join('database', 'supply_management.db')
            self.db_manager = DatabaseManager(db_path)
            self.logger.info("تم تهيئة قاعدة البيانات بنجاح")
        except Exception as e:
            self.logger.error(f"خطأ في تهيئة قاعدة البيانات: {e}")
            messagebox.showerror(
                "خطأ في قاعدة البيانات",
                f"فشل في تهيئة قاعدة البيانات:\n{e}"
            )
            sys.exit(1)
    
    def start_application(self):
        """بدء تشغيل التطبيق"""
        try:
            # عرض نافذة تسجيل الدخول
            login_window = LoginWindow(
                db_manager=self.db_manager,
                on_success_callback=self.on_login_success
            )
            login_window.show()
            
        except Exception as e:
            self.logger.error(f"خطأ في بدء التطبيق: {e}")
            messagebox.showerror(
                "خطأ في التطبيق",
                f"فشل في بدء التطبيق:\n{e}"
            )
            sys.exit(1)
    
    def on_login_success(self, user_data):
        """معالج نجاح تسجيل الدخول"""
        try:
            self.logger.info(f"تم تسجيل دخول المستخدم: {user_data.get('username', 'غير معروف')}")
            
            # إنشاء النافذة الرئيسية
            root = ttk_bs.Window(
                title="نظام إدارة التموين العام والقوة العمومية",
                themename="cosmo",
                size=(1400, 800),
                position=(100, 50)
            )
            
            # إعداد الأيقونة
            try:
                icon_path = os.path.join('assets', 'icon.ico')
                if os.path.exists(icon_path):
                    root.iconbitmap(icon_path)
            except Exception:
                pass
            
            # إنشاء النافذة الرئيسية
            self.main_window = MainWindow(
                root=root,
                db_manager=self.db_manager,
                user_data=user_data,
                config=self.config,
                logger=self.logger
            )
            
            # بدء حلقة الأحداث الرئيسية
            root.mainloop()
            
        except Exception as e:
            self.logger.error(f"خطأ في إنشاء النافذة الرئيسية: {e}")
            messagebox.showerror(
                "خطأ في النافذة الرئيسية",
                f"فشل في إنشاء النافذة الرئيسية:\n{e}"
            )
    
    def cleanup(self):
        """تنظيف الموارد عند إغلاق التطبيق"""
        try:
            if self.db_manager:
                self.db_manager.close()
                self.logger.info("تم إغلاق قاعدة البيانات")
        except Exception as e:
            self.logger.error(f"خطأ في تنظيف الموارد: {e}")


def main():
    """الدالة الرئيسية"""
    try:
        # إعداد الترميز للوحة التحكم
        if sys.platform.startswith('win'):
            os.system('chcp 65001 > nul')
        
        print("=" * 50)
        print("نظام إدارة التموين العام والقوة العمومية")
        print("=" * 50)
        print("جاري تشغيل النظام...")
        
        # إنشاء وتشغيل التطبيق
        app = SupplyManagementApp()
        
        try:
            app.start_application()
        finally:
            # تنظيف الموارد
            app.cleanup()
            
    except KeyboardInterrupt:
        print("\nتم إيقاف التطبيق بواسطة المستخدم")
        sys.exit(0)
    except Exception as e:
        print(f"خطأ عام في التطبيق: {e}")
        messagebox.showerror(
            "خطأ عام",
            f"حدث خطأ غير متوقع:\n{e}"
        )
        sys.exit(1)


if __name__ == "__main__":
    main()
