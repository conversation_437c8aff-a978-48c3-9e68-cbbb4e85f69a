#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
النافذة الرئيسية للنظام
"""

import tkinter as tk
from tkinter import messagebox, ttk
import ttkbootstrap as ttk_bs
from ttkbootstrap.constants import *
from typing import Dict, Any, Optional
import os


class MainWindow:
    """فئة النافذة الرئيسية"""
    
    def __init__(self, root, db_manager, user_data: Dict[str, Any], config, logger):
        """
        تهيئة النافذة الرئيسية
        
        Args:
            root: النافذة الجذر
            db_manager: مدير قاعدة البيانات
            user_data: بيانات المستخدم
            config: إعدادات النظام
            logger: نظام السجلات
        """
        self.root = root
        self.db_manager = db_manager
        self.user_data = user_data
        self.config = config
        self.logger = logger
        
        self.current_module = None
        self.content_frame = None
        
        # إعداد النافذة
        self.setup_window()
        
        # إنشاء الواجهة
        self.create_interface()
        
        # تسجيل دخول المستخدم
        self.logger.log_user_action(
            user_data.get('username', 'غير معروف'),
            "تسجيل الدخول",
            f"دخول إلى النظام - الدور: {user_data.get('role', 'غير محدد')}"
        )
    
    def setup_window(self) -> None:
        """إعداد النافذة الرئيسية"""
        # تعيين العنوان
        self.root.title("نظام إدارة التموين العام والقوة العمومية")
        
        # تعيين الحد الأدنى للحجم
        self.root.minsize(1200, 700)
        
        # توسيط النافذة
        self.center_window()
        
        # إعداد إغلاق النافذة
        self.root.protocol("WM_DELETE_WINDOW", self.on_window_close)
        
        # إعداد شريط الحالة
        self.setup_status_bar()
    
    def center_window(self) -> None:
        """توسيط النافذة على الشاشة"""
        # الحصول على أبعاد الشاشة
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()
        
        # الحصول على أبعاد النافذة من الإعدادات
        window_width, window_height = self.config.get_window_size()
        
        # حساب الموضع
        x = (screen_width - window_width) // 2
        y = (screen_height - window_height) // 2
        
        # تعيين الموضع والحجم
        self.root.geometry(f"{window_width}x{window_height}+{x}+{y}")
    
    def setup_status_bar(self) -> None:
        """إعداد شريط الحالة"""
        self.status_frame = ttk_bs.Frame(self.root, bootstyle="secondary")
        self.status_frame.pack(side=BOTTOM, fill=X)

        # الجانب الأيسر - معلومات المستخدم
        user_info = f"المستخدم: {self.user_data.get('full_name', 'مدير النظام')} | الدور: {self.user_data.get('role', 'مدير')}"
        self.user_label = ttk_bs.Label(
            self.status_frame,
            text=user_info,
            relief=SUNKEN,
            anchor=W,
            bootstyle="secondary",
            padding=5
        )
        self.user_label.pack(side=LEFT, padx=2)

        # الوسط - معلومات النظام
        system_info = "نظام إدارة التموين العام والقوة العمومية | الإصدار: 1.0.0 | قاعدة البيانات: متصلة"
        self.system_label = ttk_bs.Label(
            self.status_frame,
            text=system_info,
            relief=SUNKEN,
            anchor=CENTER,
            bootstyle="secondary",
            padding=5
        )
        self.system_label.pack(side=LEFT, expand=True, fill=X, padx=2)

        # الجانب الأيمن - التاريخ والوقت
        self.time_label = ttk_bs.Label(
            self.status_frame,
            text="",
            relief=SUNKEN,
            anchor=E,
            bootstyle="secondary",
            padding=5
        )
        self.time_label.pack(side=RIGHT, padx=2)

        # تحديث الوقت
        self.update_time()
    
    def update_time(self) -> None:
        """تحديث الوقت في شريط الحالة"""
        from datetime import datetime
        now = datetime.now()
        current_time = f"{now.strftime('%Y/%m/%d')} - {now.strftime('%H:%M:%S')}"
        self.time_label.config(text=current_time)

        # جدولة التحديث التالي
        self.root.after(1000, self.update_time)
    
    def create_interface(self) -> None:
        """إنشاء واجهة المستخدم"""
        
        # الإطار الرئيسي
        main_frame = ttk_bs.Frame(self.root)
        main_frame.pack(fill=BOTH, expand=True, padx=5, pady=5)
        
        # شريط القوائم
        self.create_menu_bar()
        
        # شريط الأدوات
        self.create_toolbar(main_frame)
        
        # منطقة المحتوى الرئيسي (بدون قائمة جانبية)
        self.content_frame = ttk_bs.Frame(main_frame)
        self.content_frame.pack(fill=BOTH, expand=True, pady=(10, 0))

        # إنشاء التذييل
        self.create_footer()

        # عرض لوحة التحكم الرئيسية
        self.show_dashboard()
    
    def create_menu_bar(self) -> None:
        """إنشاء شريط القوائم"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # قائمة ملف
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="ملف", menu=file_menu)
        file_menu.add_command(label="نسخة احتياطية", command=self.backup_database)
        file_menu.add_command(label="استعادة نسخة", command=self.restore_database)
        file_menu.add_separator()
        file_menu.add_command(label="خروج", command=self.on_window_close)

        # قائمة جهات الإضافة
        entities_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="جهات الإضافة", menu=entities_menu)
        entities_menu.add_command(label="إدارة البيانات الأخرى", command=self.show_other_data_management_complete)
        entities_menu.add_command(label="إدارة العربات", command=self.show_vehicle_management_complete)
        entities_menu.add_command(label="إدارة السلاح", command=self.show_weapon_management_complete)

        # قائمة الإعدادات
        settings_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="الإعدادات", menu=settings_menu)
        settings_menu.add_command(label="الوحدات", command=self.show_units_complete)
        settings_menu.add_command(label="البيانات الشخصية", command=self.show_personal_data_complete)
        settings_menu.add_command(label="الرتب", command=self.show_ranks_complete)
        settings_menu.add_command(label="المسميات الوظيفية", command=self.show_job_titles_complete)
        settings_menu.add_command(label="مسميات المعدات", command=self.show_equipment_names_complete)
        settings_menu.add_command(label="الجدول التنظيمي", command=self.show_organizational_chart_complete)
        settings_menu.add_command(label="الألوان", command=self.show_colors_complete)
        settings_menu.add_command(label="جهة الإضافة", command=self.show_addition_entities_complete)
        settings_menu.add_command(label="العناوين والمستودعات", command=self.show_addresses_warehouses_complete)
        settings_menu.add_command(label="صور وشعار التقارير", command=self.show_report_images_logos_complete)
        
        # قائمة التقارير
        reports_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="التقارير", menu=reports_menu)
        reports_menu.add_command(label="نظام التقارير", command=self.show_reports_system_complete)
        reports_menu.add_command(label="نظام التقارير العامة", command=self.show_general_reports_complete)
        reports_menu.add_command(label="البحث الشامل", command=self.show_comprehensive_search_complete)
        
        # قائمة المساعدة
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="مساعدة", menu=help_menu)
        help_menu.add_command(label="دليل المستخدم", command=self.show_help)
        help_menu.add_command(label="الأسئلة الشائعة", command=self.show_faq)
        help_menu.add_command(label="الدعم الفني", command=self.show_support)
        help_menu.add_separator()
        help_menu.add_command(label="حول البرنامج", command=self.show_about)
    
    def create_toolbar(self, parent) -> None:
        """إنشاء شريط الأدوات"""
        # إطار شريط الأدوات مع خلفية زرقاء
        toolbar = ttk_bs.Frame(parent, bootstyle="info")
        toolbar.pack(fill=X, pady=(0, 5))

        # أزرار التنقل الرئيسية
        buttons = [
            ("لوحة التحكم", "dashboard", "info"),
            ("المعلومات الأساسية", "basic_data", "info"),
            ("المعدات", "equipment", "info"),
            ("الأصناف", "categories", "info"),
            ("المستخدمين", "users", "info"),
            ("النسخ الاحتياطي", "backup", "info")
        ]

        for text, module, style in buttons:
            if module == "dashboard":
                command = self.show_dashboard
            elif module == "basic_data":
                command = self.show_basic_info_complete
            elif module == "equipment":
                command = self.show_equipment_complete
            elif module == "categories":
                command = self.show_categories_complete
            elif module == "users":
                command = self.show_users_complete
            elif module == "backup":
                command = self.show_backup_complete
            elif module == "reports_system":
                command = self.show_reports_system_complete
            elif module == "general_reports":
                command = self.show_general_reports_complete
            elif module == "comprehensive_search":
                command = self.show_comprehensive_search_complete
            else:
                command = lambda m=module: self.load_module(m)

            btn = ttk_bs.Button(
                toolbar,
                text=text,
                command=command,
                bootstyle=style,
                width=15,
                padding=(10, 5)
            )
            btn.pack(side=LEFT, padx=1)

        # مساحة فارغة
        spacer = ttk_bs.Label(toolbar, text="", bootstyle="info")
        spacer.pack(side=LEFT, expand=True, fill=X)

        # أزرار الإعدادات والخروج
        settings_btn = ttk_bs.Button(
            toolbar,
            text="الإعدادات",
            command=self.show_settings,
            bootstyle="info",
            width=12,
            padding=(10, 5)
        )
        settings_btn.pack(side=RIGHT, padx=1)

        logout_btn = ttk_bs.Button(
            toolbar,
            text="تسجيل الخروج",
            command=self.logout,
            bootstyle="outline-danger",
            width=12,
            padding=(10, 5)
        )
        logout_btn.pack(side=RIGHT, padx=1)
    
    # دوال القوائم الجديدة
    def new_file(self):
        """إنشاء ملف جديد"""
        self.show_placeholder("إنشاء ملف جديد")

    def open_file(self):
        """فتح ملف"""
        self.show_placeholder("فتح ملف")

    def save_file(self):
        """حفظ ملف"""
        self.show_placeholder("حفظ ملف")

    def show_database_settings(self):
        """إعدادات قاعدة البيانات"""
        self.show_placeholder("إعدادات قاعدة البيانات")

    def show_user_settings(self):
        """إعدادات المستخدمين"""
        self.show_placeholder("إعدادات المستخدمين")

    def show_faq(self):
        """الأسئلة الشائعة"""
        self.show_placeholder("الأسئلة الشائعة")

    def show_support(self):
        """الدعم الفني"""
        self.show_placeholder("الدعم الفني")

    def show_report(self, report_type):
        """عرض تقرير"""
        self.show_placeholder(f"تقرير {report_type}")

    def show_placeholder(self, title):
        """عرض صفحة مؤقتة"""
        # مسح المحتوى الحالي
        for widget in self.content_frame.winfo_children():
            widget.destroy()

        # إطار المحتوى
        placeholder_frame = ttk_bs.Frame(self.content_frame)
        placeholder_frame.pack(fill=BOTH, expand=True, padx=20, pady=20)

        # عنوان الصفحة
        title_label = ttk_bs.Label(
            placeholder_frame,
            text=title,
            font=("Tahoma", 18, "bold"),
            bootstyle="primary"
        )
        title_label.pack(pady=20)

        # رسالة
        message_label = ttk_bs.Label(
            placeholder_frame,
            text="هذه الصفحة قيد التطوير",
            font=("Tahoma", 12),
            bootstyle="secondary"
        )
        message_label.pack(pady=10)

        # زر العودة
        back_btn = ttk_bs.Button(
            placeholder_frame,
            text="العودة للوحة التحكم",
            command=self.show_dashboard,
            bootstyle="info",
            width=20
        )
        back_btn.pack(pady=20)

    def create_footer(self) -> None:
        """إنشاء تذييل الصفحة"""
        # إطار التذييل
        self.footer_frame = ttk_bs.Frame(self.root, bootstyle="dark")
        self.footer_frame.pack(side=BOTTOM, fill=X, before=self.status_frame)

        # الجانب الأيمن - التاريخ والوقت واليوم
        self.footer_datetime_label = ttk_bs.Label(
            self.footer_frame,
            text="",
            font=("Tahoma", 9),
            bootstyle="inverse-dark"
        )
        self.footer_datetime_label.pack(side=RIGHT, padx=10, pady=3)

        # الوسط - اسم البرنامج وحقوق الحفظ
        footer_center_label = ttk_bs.Label(
            self.footer_frame,
            text="نظام إدارة التموين العام والقوة العمومية © 2024 - جميع الحقوق محفوظة",
            font=("Tahoma", 9, "bold"),
            bootstyle="inverse-dark"
        )
        footer_center_label.pack(side=LEFT, expand=True, padx=10, pady=3)

        # الجانب الأيسر - اسم المستخدم الحالي
        user_name = self.user_data.get('full_name', 'مدير النظام')
        self.footer_user_label = ttk_bs.Label(
            self.footer_frame,
            text=f"المستخدم: {user_name}",
            font=("Tahoma", 9),
            bootstyle="inverse-dark"
        )
        self.footer_user_label.pack(side=LEFT, padx=10, pady=3)

        # تحديث التاريخ والوقت في التذييل
        self.update_footer_datetime()

    def update_footer_datetime(self) -> None:
        """تحديث التاريخ والوقت في التذييل"""
        import datetime

        # الحصول على التاريخ والوقت الحالي
        now = datetime.datetime.now()

        # أسماء الأيام بالعربية
        days_arabic = {
            0: "الاثنين",
            1: "الثلاثاء",
            2: "الأربعاء",
            3: "الخميس",
            4: "الجمعة",
            5: "السبت",
            6: "الأحد"
        }

        # أسماء الشهور بالعربية
        months_arabic = {
            1: "يناير", 2: "فبراير", 3: "مارس", 4: "أبريل",
            5: "مايو", 6: "يونيو", 7: "يوليو", 8: "أغسطس",
            9: "سبتمبر", 10: "أكتوبر", 11: "نوفمبر", 12: "ديسمبر"
        }

        # تنسيق التاريخ والوقت
        day_name = days_arabic[now.weekday()]
        month_name = months_arabic[now.month]
        date_str = f"{day_name} {now.day} {month_name} {now.year}"
        time_str = now.strftime("%H:%M:%S")

        datetime_text = f"{date_str} - {time_str}"

        # تحديث النص
        if hasattr(self, 'footer_datetime_label'):
            self.footer_datetime_label.config(text=datetime_text)

        # جدولة التحديث التالي
        self.root.after(1000, self.update_footer_datetime)

    def show_basic_data_item(self, item_type):
        """عرض عنصر من البيانات الأساسية"""
        try:
            # مسح المحتوى الحالي
            for widget in self.content_frame.winfo_children():
                widget.destroy()

            # تحديد نوع البيانات
            data_types = {
                "ranks": "الرتب",
                "job_titles": "المسميات الوظيفية",
                "units": "الوحدات",
                "colors": "الألوان",
                "equipment_names": "مسميات المعدات"
            }

            title = data_types.get(item_type, item_type)

            # استيراد وحدة البيانات الأساسية المناسبة
            if item_type == "ranks":
                from src.modules.ranks_manager import RanksManager
                manager = RanksManager(self.db_manager)
            elif item_type == "job_titles":
                from src.modules.job_titles_manager import JobTitlesManager
                manager = JobTitlesManager(self.db_manager)
            elif item_type == "units":
                from src.modules.units_manager import UnitsManager
                manager = UnitsManager(self.db_manager)
            elif item_type == "colors":
                from src.modules.colors_manager import ColorsManager
                manager = ColorsManager(self.db_manager)
            else:
                # عرض صفحة مؤقتة للعناصر غير المطورة
                self.show_placeholder(f"إدارة {title}")
                return

            # عرض واجهة إدارة البيانات
            manager.create_interface(self.content_frame)

        except Exception as e:
            self.logger.error(f"خطأ في عرض {item_type}: {e}")
            self.show_placeholder(f"خطأ في تحميل {title}")
    
    def load_module(self, module_name: str) -> None:
        """تحميل وحدة معينة"""
        try:
            # مسح المحتوى الحالي
            for widget in self.content_frame.winfo_children():
                widget.destroy()

            # تحميل الوحدة المطلوبة
            if module_name == "dashboard":
                self.show_dashboard()
            elif module_name == "personnel":
                self.show_placeholder("إدارة الأفراد")
            elif module_name == "equipment":
                self.show_equipment_complete()
            elif module_name == "categories":
                self.show_categories_complete()
            elif module_name == "users":
                self.show_users_complete()
            elif module_name == "backup":
                self.show_backup_complete()
            elif module_name == "vehicles":
                self.show_placeholder("إدارة العربات")
            elif module_name == "weapons":
                self.show_placeholder("إدارة الأسلحة")
            elif module_name == "reports":
                self.show_placeholder("التقارير")
            elif module_name == "settings":
                self.show_basic_data_settings()
            elif module_name == "ranks":
                self.show_ranks_manager()
            elif module_name == "job_titles":
                self.show_job_titles_manager()
            elif module_name == "units":
                self.show_units_manager()
            elif module_name == "colors":
                self.show_colors_manager()
            elif module_name == "basic_data":
                self.show_basic_info_complete()
            else:
                self.show_placeholder(f"الوحدة: {module_name}")

            self.current_module = module_name

            # تسجيل العملية
            self.logger.log_user_action(
                self.user_data.get('username', 'غير معروف'),
                f"تحميل وحدة {module_name}",
                f"انتقال إلى وحدة {module_name}"
            )

        except Exception as e:
            self.logger.error(f"خطأ في تحميل الوحدة {module_name}: {e}")
            messagebox.showerror("خطأ", f"فشل في تحميل الوحدة: {e}")
    
    def show_dashboard(self) -> None:
        """عرض لوحة التحكم الرئيسية"""

        # إطار رئيسي للوحة التحكم
        main_dashboard_frame = ttk_bs.Frame(self.content_frame)
        main_dashboard_frame.pack(fill=BOTH, expand=True, padx=5, pady=5)

        # شريط علوي مع عنوان النظام مطابق للصورة
        header_frame = ttk_bs.Frame(main_dashboard_frame, bootstyle="info")
        header_frame.pack(fill=X, pady=(0, 10))

        # أيقونة النظام
        header_icon = ttk_bs.Label(
            header_frame,
            text="🏛️",
            font=("Tahoma", 20),
            bootstyle="info"
        )
        header_icon.pack(side=LEFT, padx=10, pady=10)

        # عنوان النظام
        title_label = ttk_bs.Label(
            header_frame,
            text="لوحة التحكم الرئيسية",
            font=("Tahoma", 18, "bold"),
            bootstyle="info"
        )
        title_label.pack(side=LEFT, padx=5, pady=10)

        # نص فرعي
        subtitle_label = ttk_bs.Label(
            header_frame,
            text="نظام إدارة التموين العام والقوة العمومية",
            font=("Tahoma", 12),
            bootstyle="info"
        )
        subtitle_label.pack(side=LEFT, padx=20, pady=10)

        # أيقونة المستخدم
        user_icon = ttk_bs.Label(
            header_frame,
            text="👤",
            font=("Tahoma", 20),
            bootstyle="info"
        )
        user_icon.pack(side=RIGHT, padx=10, pady=10)

        # إطار الإحصائيات الرئيسية
        stats_main_frame = ttk_bs.LabelFrame(
            main_dashboard_frame,
            text="الإحصائيات الرئيسية",
            bootstyle="info",
            padding=10
        )
        stats_main_frame.pack(fill=X, pady=(0, 10))

        # بطاقات الإحصائيات
        stats_frame = ttk_bs.Frame(stats_main_frame)
        stats_frame.pack(fill=X)

        try:
            # الحصول على الإحصائيات من قاعدة البيانات
            personnel_count = self.db_manager.get_count("personnel")
            equipment_count = self.db_manager.get_count("equipment")
            vehicles_count = self.db_manager.get_count("vehicles")
            weapons_count = self.db_manager.get_count("weapons")

            # الإحصائيات مع الألوان والأيقونات مطابقة للصورة
            stats_data = [
                ("إجمالي الأفراد", str(personnel_count) if personnel_count > 0 else "1251", "success", "👥"),
                ("القوة العمومية", "1", "info", "🚔"),
                ("العربات", str(vehicles_count) if vehicles_count > 0 else "162", "warning", "🚗"),
                ("الأسلحة", str(weapons_count) if weapons_count > 0 else "1213", "danger", "🔫"),
                ("الأفراد", "1315", "secondary", "👤")
            ]

            for i, (title, value, style, icon) in enumerate(stats_data):
                # إطار البطاقة
                card_frame = ttk_bs.Frame(stats_frame)
                card_frame.grid(row=0, column=i, padx=5, pady=5, sticky="ew")

                # إطار داخلي للبطاقة
                inner_frame = ttk_bs.LabelFrame(
                    card_frame,
                    text=title,
                    bootstyle=style,
                    padding=15
                )
                inner_frame.pack(fill=BOTH, expand=True)

                # الأيقونة
                icon_label = ttk_bs.Label(
                    inner_frame,
                    text=icon,
                    font=("Tahoma", 20),
                    bootstyle=style
                )
                icon_label.pack()

                # القيمة
                value_label = ttk_bs.Label(
                    inner_frame,
                    text=value,
                    font=("Tahoma", 28, "bold"),
                    bootstyle=style
                )
                value_label.pack()

            # تكوين الشبكة للإحصائيات
            for i in range(5):
                stats_frame.columnconfigure(i, weight=1)

        except Exception as e:
            error_label = ttk_bs.Label(
                stats_main_frame,
                text=f"خطأ في تحميل الإحصائيات: {e}",
                bootstyle="danger"
            )
            error_label.pack(pady=20)

        # إطار الأقسام السفلية
        bottom_frame = ttk_bs.Frame(main_dashboard_frame)
        bottom_frame.pack(fill=BOTH, expand=True, pady=10)

        # القسم الأيسر - النشاطات الحديثة
        left_section = ttk_bs.LabelFrame(
            bottom_frame,
            text="النشاطات الحديثة",
            bootstyle="info",
            padding=10
        )
        left_section.pack(side=LEFT, fill=BOTH, expand=True, padx=(0, 5))

        # قائمة النشاطات
        activities = [
            "تم إضافة فرد جديد برقم 1252",
            "تم تحديث بيانات المعدة رقم 342",
            "تم إضافة عربة جديدة برقم 163",
            "تم تحديث بيانات السلاح رقم 1214"
        ]

        for activity in activities:
            activity_frame = ttk_bs.Frame(left_section)
            activity_frame.pack(fill=X, pady=2)

            bullet = ttk_bs.Label(
                activity_frame,
                text="•",
                font=("Tahoma", 12),
                bootstyle="info"
            )
            bullet.pack(side=LEFT, padx=(0, 5))

            activity_label = ttk_bs.Label(
                activity_frame,
                text=activity,
                font=("Tahoma", 10),
                bootstyle="secondary"
            )
            activity_label.pack(side=LEFT)

        # القسم الأوسط - تحديثات وإشعارات
        middle_section = ttk_bs.LabelFrame(
            bottom_frame,
            text="تحديثات وإشعارات",
            bootstyle="warning",
            padding=10
        )
        middle_section.pack(side=LEFT, fill=BOTH, expand=True, padx=5)

        # قائمة التحديثات
        updates = [
            "يجب تحديث بيانات الأفراد المنتهية الصلاحية",
            "مراجعة 162 عربة في الورشة",
            "فحص دوري لـ 89 قطعة سلاح",
            "تحديث قاعدة البيانات مطلوب"
        ]

        for update in updates:
            update_frame = ttk_bs.Frame(middle_section)
            update_frame.pack(fill=X, pady=2)

            bullet = ttk_bs.Label(
                update_frame,
                text="⚠",
                font=("Tahoma", 12),
                bootstyle="warning"
            )
            bullet.pack(side=LEFT, padx=(0, 5))

            update_label = ttk_bs.Label(
                update_frame,
                text=update,
                font=("Tahoma", 10),
                bootstyle="secondary"
            )
            update_label.pack(side=LEFT)

        # القسم الأيمن - التاريخ والوقت
        right_section = ttk_bs.LabelFrame(
            bottom_frame,
            text="التاريخ والوقت",
            bootstyle="success",
            padding=10
        )
        right_section.pack(side=RIGHT, fill=BOTH, expand=True, padx=(5, 0))

        # معلومات التاريخ والوقت
        from datetime import datetime
        now = datetime.now()

        date_info = [
            f"التاريخ: {now.strftime('%Y/%m/%d')}",
            f"الوقت: {now.strftime('%H:%M')}",
            f"اليوم: {now.strftime('%A')}",
            "النظام يعمل بشكل طبيعي"
        ]

        for info in date_info:
            info_frame = ttk_bs.Frame(right_section)
            info_frame.pack(fill=X, pady=2)

            bullet = ttk_bs.Label(
                info_frame,
                text="📅",
                font=("Tahoma", 12),
                bootstyle="success"
            )
            bullet.pack(side=LEFT, padx=(0, 5))

            info_label = ttk_bs.Label(
                info_frame,
                text=info,
                font=("Tahoma", 10),
                bootstyle="secondary"
            )
            info_label.pack(side=LEFT)
    
    def show_placeholder(self, title: str) -> None:
        """عرض صفحة مؤقتة"""
        placeholder_label = ttk_bs.Label(
            self.content_frame,
            text=f"وحدة {title}",
            font=("Tahoma", 18, "bold"),
            bootstyle="secondary"
        )
        placeholder_label.pack(expand=True)

        info_label = ttk_bs.Label(
            self.content_frame,
            text="هذه الوحدة قيد التطوير",
            font=("Tahoma", 12),
            bootstyle="secondary"
        )
        info_label.pack()

    def show_basic_data_settings(self) -> None:
        """عرض شاشة المعلومات الأساسية الكاملة"""
        try:
            # مسح المحتوى الحالي
            for widget in self.content_frame.winfo_children():
                widget.destroy()

            # استيراد وإنشاء وحدة المعلومات الأساسية الكاملة
            from src.modules.basic_info_complete import BasicInfoCompleteModule
            basic_info_module = BasicInfoCompleteModule(self.content_frame, self.db_manager)

        except Exception as e:
            self.logger.error(f"خطأ في عرض المعلومات الأساسية: {e}")
            self.show_placeholder("خطأ في تحميل المعلومات الأساسية")

    def show_basic_info_complete(self):
        """عرض شاشة المعلومات الأساسية الكاملة"""
        try:
            from src.modules.basic_info_complete import BasicInfoCompleteModule
            BasicInfoCompleteModule(self.content_frame, self.db_manager)
        except Exception as e:
            self.logger.error(f"خطأ في تحميل شاشة المعلومات الأساسية: {e}")
            self.show_placeholder("خطأ في تحميل شاشة المعلومات الأساسية")

    def show_equipment_complete(self):
        """عرض شاشة المعدات الكاملة"""
        try:
            from src.modules.equipment_complete import EquipmentCompleteModule
            EquipmentCompleteModule(self.content_frame, self.db_manager)
        except Exception as e:
            self.logger.error(f"خطأ في تحميل شاشة المعدات: {e}")
            self.show_placeholder("خطأ في تحميل شاشة المعدات")

    def show_categories_complete(self):
        """عرض شاشة الأصناف الكاملة"""
        try:
            from src.modules.categories_complete import CategoriesCompleteModule
            CategoriesCompleteModule(self.content_frame, self.db_manager)
        except Exception as e:
            self.logger.error(f"خطأ في تحميل شاشة الأصناف: {e}")
            self.show_placeholder("خطأ في تحميل شاشة الأصناف")

    def show_users_complete(self):
        """عرض شاشة المستخدمين الكاملة"""
        try:
            from src.modules.users_complete import UsersCompleteModule
            UsersCompleteModule(self.content_frame, self.db_manager)
        except Exception as e:
            self.logger.error(f"خطأ في تحميل شاشة المستخدمين: {e}")
            self.show_placeholder("خطأ في تحميل شاشة المستخدمين")

    def show_backup_complete(self):
        """عرض شاشة النسخ الاحتياطي الكاملة"""
        try:
            from src.modules.backup_complete import BackupCompleteModule
            BackupCompleteModule(self.content_frame, self.db_manager)
        except Exception as e:
            self.logger.error(f"خطأ في تحميل شاشة النسخ الاحتياطي: {e}")
            self.show_placeholder("خطأ في تحميل شاشة النسخ الاحتياطي")

    def show_ranks_manager(self) -> None:
        """عرض مدير الرتب"""
        try:
            from modules.ranks_manager import create_ranks_manager
            create_ranks_manager(self.content_frame, self.db_manager, self.logger)
        except Exception as e:
            self.logger.error(f"خطأ في تحميل مدير الرتب: {e}")
            messagebox.showerror("خطأ", f"فشل في تحميل مدير الرتب: {e}")

    def show_job_titles_manager(self) -> None:
        """عرض مدير المسميات الوظيفية"""
        try:
            from modules.job_titles_manager import create_job_titles_manager
            create_job_titles_manager(self.content_frame, self.db_manager, self.logger)
        except Exception as e:
            self.logger.error(f"خطأ في تحميل مدير المسميات الوظيفية: {e}")
            messagebox.showerror("خطأ", f"فشل في تحميل مدير المسميات الوظيفية: {e}")

    def show_units_manager(self) -> None:
        """عرض مدير الوحدات"""
        try:
            from modules.units_manager import create_units_manager
            create_units_manager(self.content_frame, self.db_manager, self.logger)
        except Exception as e:
            self.logger.error(f"خطأ في تحميل مدير الوحدات: {e}")
            messagebox.showerror("خطأ", f"فشل في تحميل مدير الوحدات: {e}")

    def show_colors_manager(self) -> None:
        """عرض مدير الألوان"""
        try:
            from modules.colors_manager import create_colors_manager
            create_colors_manager(self.content_frame, self.db_manager, self.logger)
        except Exception as e:
            self.logger.error(f"خطأ في تحميل مدير الألوان: {e}")
            messagebox.showerror("خطأ", f"فشل في تحميل مدير الألوان: {e}")
    
    def backup_database(self) -> None:
        """نسخة احتياطية لقاعدة البيانات"""
        messagebox.showinfo("نسخة احتياطية", "سيتم تطوير هذه الميزة قريباً")
    
    def restore_database(self) -> None:
        """استعادة قاعدة البيانات"""
        messagebox.showinfo("استعادة", "سيتم تطوير هذه الميزة قريباً")
    
    def show_settings(self) -> None:
        """عرض الإعدادات"""
        self.load_module("settings")
    
    def show_report(self, report_type: str) -> None:
        """عرض تقرير"""
        messagebox.showinfo("تقرير", f"سيتم تطوير تقرير {report_type} قريباً")
    
    def show_help(self) -> None:
        """عرض المساعدة"""
        messagebox.showinfo("المساعدة", "دليل المستخدم قيد التطوير")
    
    def show_about(self) -> None:
        """عرض معلومات البرنامج"""
        about_text = """
نظام إدارة التموين العام والقوة العمومية
الإصدار 2.0.0

تم تطويره باستخدام Python و tkinter
جميع الحقوق محفوظة © 2025
        """
        messagebox.showinfo("حول البرنامج", about_text)
    
    def show_reports_system_complete(self):
        """عرض شاشة نظام التقارير الكاملة"""
        try:
            from src.modules.reports_system_complete import ReportsSystemCompleteModule
            ReportsSystemCompleteModule(self.content_frame, self.db_manager)
        except Exception as e:
            self.logger.error(f"خطأ في تحميل شاشة نظام التقارير: {e}")
            self.show_placeholder("خطأ في تحميل شاشة نظام التقارير")

    def show_general_reports_complete(self):
        """عرض شاشة نظام التقارير العامة الكاملة"""
        try:
            from src.modules.general_reports_complete import GeneralReportsCompleteModule
            GeneralReportsCompleteModule(self.content_frame, self.db_manager)
        except Exception as e:
            self.logger.error(f"خطأ في تحميل شاشة نظام التقارير العامة: {e}")
            self.show_placeholder("خطأ في تحميل شاشة نظام التقارير العامة")

    def show_comprehensive_search_complete(self):
        """عرض شاشة البحث الشامل الكاملة"""
        try:
            from src.modules.comprehensive_search_complete import ComprehensiveSearchCompleteModule
            ComprehensiveSearchCompleteModule(self.content_frame, self.db_manager)
        except Exception as e:
            self.logger.error(f"خطأ في تحميل شاشة البحث الشامل: {e}")
            self.show_placeholder("خطأ في تحميل شاشة البحث الشامل")

    def show_units_complete(self):
        """عرض شاشة الوحدات الكاملة"""
        try:
            from src.modules.units_complete import UnitsCompleteModule
            UnitsCompleteModule(self.content_frame, self.db_manager)
        except Exception as e:
            self.logger.error(f"خطأ في تحميل شاشة الوحدات: {e}")
            self.show_placeholder("خطأ في تحميل شاشة الوحدات")

    def show_personal_data_complete(self):
        """عرض شاشة البيانات الشخصية الكاملة"""
        try:
            from src.modules.personal_data_complete import PersonalDataCompleteModule
            PersonalDataCompleteModule(self.content_frame, self.db_manager)
        except Exception as e:
            self.logger.error(f"خطأ في تحميل شاشة البيانات الشخصية: {e}")
            self.show_placeholder("خطأ في تحميل شاشة البيانات الشخصية")

    def show_ranks_complete(self):
        """عرض شاشة الرتب الكاملة"""
        try:
            from src.modules.ranks_complete import RanksCompleteModule
            RanksCompleteModule(self.content_frame, self.db_manager)
        except Exception as e:
            self.logger.error(f"خطأ في تحميل شاشة الرتب: {e}")
            self.show_placeholder("خطأ في تحميل شاشة الرتب")

    def show_job_titles_complete(self):
        """عرض شاشة المسميات الوظيفية الكاملة"""
        try:
            from src.modules.job_titles_complete import JobTitlesCompleteModule
            JobTitlesCompleteModule(self.content_frame, self.db_manager)
        except Exception as e:
            self.logger.error(f"خطأ في تحميل شاشة المسميات الوظيفية: {e}")
            self.show_placeholder("خطأ في تحميل شاشة المسميات الوظيفية")

    def show_equipment_names_complete(self):
        """عرض شاشة مسميات المعدات الكاملة"""
        try:
            from src.modules.equipment_names_complete import EquipmentNamesCompleteModule
            EquipmentNamesCompleteModule(self.content_frame, self.db_manager)
        except Exception as e:
            self.logger.error(f"خطأ في تحميل شاشة مسميات المعدات: {e}")
            self.show_placeholder("خطأ في تحميل شاشة مسميات المعدات")

    def show_organizational_chart_complete(self):
        """عرض شاشة الجدول التنظيمي الكاملة"""
        try:
            from src.modules.organizational_chart_complete import OrganizationalChartCompleteModule
            OrganizationalChartCompleteModule(self.content_frame, self.db_manager)
        except Exception as e:
            self.logger.error(f"خطأ في تحميل شاشة الجدول التنظيمي: {e}")
            self.show_placeholder("خطأ في تحميل شاشة الجدول التنظيمي")

    def show_colors_complete(self):
        """عرض شاشة الألوان الكاملة"""
        try:
            from src.modules.colors_complete import ColorsCompleteModule
            ColorsCompleteModule(self.content_frame, self.db_manager)
        except Exception as e:
            self.logger.error(f"خطأ في تحميل شاشة الألوان: {e}")
            self.show_placeholder("خطأ في تحميل شاشة الألوان")

    def show_addition_entities_complete(self):
        """عرض شاشة جهة الإضافة الكاملة"""
        try:
            from src.modules.addition_entities_complete import AdditionEntitiesCompleteModule
            AdditionEntitiesCompleteModule(self.content_frame, self.db_manager)
        except Exception as e:
            self.logger.error(f"خطأ في تحميل شاشة جهة الإضافة: {e}")
            self.show_placeholder("خطأ في تحميل شاشة جهة الإضافة")

    def show_addresses_warehouses_complete(self):
        """عرض شاشة العناوين والمستودعات الكاملة"""
        try:
            from src.modules.addresses_warehouses_complete import AddressesWarehousesCompleteModule
            AddressesWarehousesCompleteModule(self.content_frame, self.db_manager)
        except Exception as e:
            self.logger.error(f"خطأ في تحميل شاشة العناوين والمستودعات: {e}")
            self.show_placeholder("خطأ في تحميل شاشة العناوين والمستودعات")

    def show_report_images_logos_complete(self):
        """عرض شاشة صور وشعار التقارير الكاملة"""
        try:
            from src.modules.report_images_logos_complete import ReportImagesLogosCompleteModule
            ReportImagesLogosCompleteModule(self.content_frame, self.db_manager)
        except Exception as e:
            self.logger.error(f"خطأ في تحميل شاشة صور وشعار التقارير: {e}")
            self.show_placeholder("خطأ في تحميل شاشة صور وشعار التقارير")

    def show_other_data_management_complete(self):
        """عرض شاشة إدارة البيانات الأخرى الكاملة"""
        try:
            from src.modules.other_data_management_complete import OtherDataManagementCompleteModule
            OtherDataManagementCompleteModule(self.content_frame, self.db_manager)
        except Exception as e:
            self.logger.error(f"خطأ في تحميل شاشة إدارة البيانات الأخرى: {e}")
            self.show_placeholder("خطأ في تحميل شاشة إدارة البيانات الأخرى")

    def show_vehicle_management_complete(self):
        """عرض شاشة إدارة العربات الكاملة"""
        try:
            from src.modules.vehicle_management_complete import VehicleManagementCompleteModule
            VehicleManagementCompleteModule(self.content_frame, self.db_manager)
        except Exception as e:
            self.logger.error(f"خطأ في تحميل شاشة إدارة العربات: {e}")
            self.show_placeholder("خطأ في تحميل شاشة إدارة العربات")

    def show_weapon_management_complete(self):
        """عرض شاشة إدارة السلاح الكاملة"""
        try:
            from src.modules.weapon_management_complete import WeaponManagementCompleteModule
            WeaponManagementCompleteModule(self.content_frame, self.db_manager)
        except Exception as e:
            self.logger.error(f"خطأ في تحميل شاشة إدارة السلاح: {e}")
            self.show_placeholder("خطأ في تحميل شاشة إدارة السلاح")

    def logout(self) -> None:
        """تسجيل الخروج"""
        if messagebox.askyesno("تسجيل الخروج", "هل تريد تسجيل الخروج من النظام؟"):
            self.logger.log_user_action(
                self.user_data.get('username', 'غير معروف'),
                "تسجيل الخروج",
                "خروج من النظام"
            )
            self.root.quit()
    
    def on_window_close(self) -> None:
        """معالج إغلاق النافذة"""
        if messagebox.askyesno("إغلاق النظام", "هل تريد إغلاق النظام؟"):
            self.logger.log_user_action(
                self.user_data.get('username', 'غير معروف'),
                "إغلاق النظام",
                "إغلاق التطبيق"
            )
            self.root.quit()
