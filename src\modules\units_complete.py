#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
وحدة إدارة الوحدات الكاملة مطابقة للبرنامج الأصلي
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import ttkbootstrap as ttk_bs
from ttkbootstrap.constants import *
from datetime import datetime

class UnitsCompleteModule:
    def __init__(self, parent_frame, db_manager):
        self.parent = parent_frame
        self.db_manager = db_manager
        self.current_record = None
        self.form_vars = {}
        
        # إنشاء الواجهة
        self.create_interface()
        
        # تحميل البيانات
        self.load_data()
    
    def create_interface(self):
        """إنشاء واجهة إدارة الوحدات مطابقة للبرنامج الأصلي"""
        # مسح المحتوى السابق
        for widget in self.parent.winfo_children():
            widget.destroy()
        
        # الإطار الرئيسي
        main_frame = ttk_bs.Frame(self.parent)
        main_frame.pack(fill=BOTH, expand=True, padx=10, pady=10)
        
        # العنوان الرئيسي
        title_frame = ttk_bs.Frame(main_frame)
        title_frame.pack(fill=X, pady=(0, 10))
        
        title_label = ttk_bs.Label(
            title_frame,
            text="إدارة الوحدات",
            font=("Tahoma", 16, "bold"),
            bootstyle="primary"
        )
        title_label.pack()
        
        # إنشاء شريط الأزرار
        self.create_buttons_bar(main_frame)
        
        # إنشاء منطقة الإدخال
        self.create_input_area(main_frame)
        
        # إنشاء جدول البيانات
        self.create_data_table(main_frame)
    
    def create_buttons_bar(self, parent):
        """إنشاء شريط الأزرار"""
        buttons_frame = ttk_bs.Frame(parent)
        buttons_frame.pack(fill=X, pady=(0, 10))
        
        # الأزرار مطابقة للبرنامج الأصلي
        buttons = [
            ("إضافة", "add", "success", "🆕"),
            ("حفظ", "save", "primary", "💾"),
            ("تعديل", "edit", "warning", "✏️"),
            ("حذف", "delete", "danger", "🗑️"),
            ("إلغاء", "cancel", "secondary", "❌"),
            ("طباعة", "print", "info", "🖨️"),
            ("تصدير", "export", "info", "📤")
        ]
        
        for text, action, style, icon in buttons:
            btn = ttk_bs.Button(
                buttons_frame,
                text=f"{icon} {text}",
                command=lambda a=action: self.handle_button_action(a),
                bootstyle=style,
                width=12
            )
            btn.pack(side=LEFT, padx=2)
    
    def create_input_area(self, parent):
        """إنشاء منطقة الإدخال مطابقة للبرنامج الأصلي"""
        input_frame = ttk_bs.LabelFrame(parent, text="بيانات الوحدة", padding=10)
        input_frame.pack(fill=X, pady=(0, 10))
        
        # الصف الأول من الحقول
        row1_frame = ttk_bs.Frame(input_frame)
        row1_frame.pack(fill=X, pady=5)
        
        # رقم الوحدة
        ttk_bs.Label(row1_frame, text="* رقم الوحدة:").pack(side=RIGHT, padx=5)
        self.form_vars['unit_number'] = ttk_bs.StringVar()
        unit_number_entry = ttk_bs.Entry(row1_frame, textvariable=self.form_vars['unit_number'], width=15)
        unit_number_entry.pack(side=RIGHT, padx=5)
        
        # اسم الوحدة
        ttk_bs.Label(row1_frame, text="* اسم الوحدة:").pack(side=RIGHT, padx=5)
        self.form_vars['unit_name'] = ttk_bs.StringVar()
        unit_name_entry = ttk_bs.Entry(row1_frame, textvariable=self.form_vars['unit_name'], width=30)
        unit_name_entry.pack(side=RIGHT, padx=5)
        
        # الصف الثاني من الحقول
        row2_frame = ttk_bs.Frame(input_frame)
        row2_frame.pack(fill=X, pady=5)
        
        # نوع الوحدة
        ttk_bs.Label(row2_frame, text="* نوع الوحدة:").pack(side=RIGHT, padx=5)
        self.form_vars['unit_type'] = ttk_bs.StringVar()
        unit_type_combo = ttk_bs.Combobox(row2_frame, textvariable=self.form_vars['unit_type'], width=20)
        unit_type_combo['values'] = ("قيادة", "لواء", "كتيبة", "سرية", "فصيلة", "مجموعة", "إدارة", "قسم")
        unit_type_combo.pack(side=RIGHT, padx=5)
        
        # الوحدة الأم
        ttk_bs.Label(row2_frame, text="الوحدة الأم:").pack(side=RIGHT, padx=5)
        self.form_vars['parent_unit'] = ttk_bs.StringVar()
        parent_unit_combo = ttk_bs.Combobox(row2_frame, textvariable=self.form_vars['parent_unit'], width=25)
        parent_unit_combo.pack(side=RIGHT, padx=5)
        
        # الصف الثالث من الحقول
        row3_frame = ttk_bs.Frame(input_frame)
        row3_frame.pack(fill=X, pady=5)
        
        # قائد الوحدة
        ttk_bs.Label(row3_frame, text="قائد الوحدة:").pack(side=RIGHT, padx=5)
        self.form_vars['unit_commander'] = ttk_bs.StringVar()
        commander_entry = ttk_bs.Entry(row3_frame, textvariable=self.form_vars['unit_commander'], width=25)
        commander_entry.pack(side=RIGHT, padx=5)
        
        # رتبة القائد
        ttk_bs.Label(row3_frame, text="رتبة القائد:").pack(side=RIGHT, padx=5)
        self.form_vars['commander_rank'] = ttk_bs.StringVar()
        rank_combo = ttk_bs.Combobox(row3_frame, textvariable=self.form_vars['commander_rank'], width=15)
        rank_combo.pack(side=RIGHT, padx=5)
        
        # الصف الرابع من الحقول
        row4_frame = ttk_bs.Frame(input_frame)
        row4_frame.pack(fill=X, pady=5)
        
        # الموقع
        ttk_bs.Label(row4_frame, text="الموقع:").pack(side=RIGHT, padx=5)
        self.form_vars['location'] = ttk_bs.StringVar()
        location_entry = ttk_bs.Entry(row4_frame, textvariable=self.form_vars['location'], width=30)
        location_entry.pack(side=RIGHT, padx=5)
        
        # رقم الهاتف
        ttk_bs.Label(row4_frame, text="رقم الهاتف:").pack(side=RIGHT, padx=5)
        self.form_vars['phone'] = ttk_bs.StringVar()
        phone_entry = ttk_bs.Entry(row4_frame, textvariable=self.form_vars['phone'], width=15)
        phone_entry.pack(side=RIGHT, padx=5)
        
        # الصف الخامس من الحقول
        row5_frame = ttk_bs.Frame(input_frame)
        row5_frame.pack(fill=X, pady=5)
        
        # تاريخ التأسيس
        ttk_bs.Label(row5_frame, text="تاريخ التأسيس:").pack(side=RIGHT, padx=5)
        self.form_vars['establishment_date'] = ttk_bs.StringVar()
        date_entry = ttk_bs.Entry(row5_frame, textvariable=self.form_vars['establishment_date'], width=12)
        date_entry.pack(side=RIGHT, padx=5)
        
        # الحالة
        ttk_bs.Label(row5_frame, text="الحالة:").pack(side=RIGHT, padx=5)
        self.form_vars['status'] = ttk_bs.StringVar()
        status_combo = ttk_bs.Combobox(row5_frame, textvariable=self.form_vars['status'], width=12)
        status_combo['values'] = ("نشطة", "غير نشطة", "مؤقتة", "ملغاة")
        status_combo.pack(side=RIGHT, padx=5)
        
        # الصف السادس - الملاحظات
        row6_frame = ttk_bs.Frame(input_frame)
        row6_frame.pack(fill=X, pady=5)
        
        ttk_bs.Label(row6_frame, text="الملاحظات:").pack(side=RIGHT, padx=5)
        self.form_vars['notes'] = ttk_bs.StringVar()
        notes_entry = ttk_bs.Entry(row6_frame, textvariable=self.form_vars['notes'], width=60)
        notes_entry.pack(side=RIGHT, padx=5, fill=X, expand=True)
        
        # تحميل البيانات في القوائم المنسدلة
        self.load_combo_data(parent_unit_combo, rank_combo)
    
    def create_data_table(self, parent):
        """إنشاء جدول البيانات"""
        table_frame = ttk_bs.LabelFrame(parent, text="قائمة الوحدات", padding=10)
        table_frame.pack(fill=BOTH, expand=True)
        
        # إنشاء Treeview
        columns = ("الرقم", "رقم الوحدة", "اسم الوحدة", "نوع الوحدة", "الوحدة الأم", "قائد الوحدة", "الموقع", "الحالة")
        self.tree = ttk_bs.Treeview(table_frame, columns=columns, show="headings", height=12)
        
        # تعيين عناوين الأعمدة
        for col in columns:
            self.tree.heading(col, text=col)
            self.tree.column(col, width=120, anchor=CENTER)
        
        # شريط التمرير
        scrollbar = ttk_bs.Scrollbar(table_frame, orient=VERTICAL, command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)
        
        # تخطيط الجدول
        self.tree.pack(side=LEFT, fill=BOTH, expand=True)
        scrollbar.pack(side=RIGHT, fill=Y)
        
        # ربط حدث النقر
        self.tree.bind("<<TreeviewSelect>>", self.on_item_select)
    
    def handle_button_action(self, action):
        """معالجة أحداث الأزرار"""
        if action == "add":
            self.add_record()
        elif action == "save":
            self.save_record()
        elif action == "edit":
            self.edit_record()
        elif action == "delete":
            self.delete_record()
        elif action == "cancel":
            self.cancel_operation()
        elif action == "print":
            self.print_record()
        elif action == "export":
            self.export_data()
    
    def load_combo_data(self, parent_unit_combo, rank_combo):
        """تحميل بيانات القوائم المنسدلة"""
        try:
            # تحميل الوحدات الأم
            units = self.db_manager.execute_query("SELECT unit_name FROM units ORDER BY id")
            unit_names = [unit['unit_name'] for unit in units] if units else []
            parent_unit_combo['values'] = unit_names
            
            # تحميل الرتب
            ranks = self.db_manager.execute_query("SELECT name FROM ranks ORDER BY id")
            rank_names = [rank['name'] for rank in ranks] if ranks else []
            rank_combo['values'] = rank_names
            
        except Exception as e:
            print(f"خطأ في تحميل بيانات القوائم: {e}")
    
    def load_data(self):
        """تحميل البيانات في الجدول"""
        try:
            # مسح البيانات الحالية
            for item in self.tree.get_children():
                self.tree.delete(item)
            
            # تحميل البيانات من قاعدة البيانات
            query = """
                SELECT u.*, p.unit_name as parent_name
                FROM units u
                LEFT JOIN units p ON u.parent_unit_id = p.id
                WHERE u.is_active = 1
                ORDER BY u.id
            """
            records = self.db_manager.execute_query(query)
            
            for i, record in enumerate(records, 1):
                values = (
                    i,
                    record.get('unit_number', ''),
                    record.get('unit_name', ''),
                    record.get('unit_type', ''),
                    record.get('parent_name', ''),
                    record.get('unit_commander', ''),
                    record.get('location', ''),
                    record.get('status', '')
                )
                self.tree.insert("", "end", values=values)
                
        except Exception as e:
            print(f"خطأ في تحميل البيانات: {e}")
    
    def on_item_select(self, event):
        """معالجة تحديد عنصر في الجدول"""
        selection = self.tree.selection()
        if selection:
            item = self.tree.item(selection[0])
            values = item['values']
            
            # تحديث الحقول بالبيانات المحددة
            if len(values) >= 8:
                self.form_vars['unit_number'].set(values[1])
                self.form_vars['unit_name'].set(values[2])
                self.form_vars['unit_type'].set(values[3])
                self.form_vars['parent_unit'].set(values[4])
                self.form_vars['unit_commander'].set(values[5])
                self.form_vars['location'].set(values[6])
                self.form_vars['status'].set(values[7])
    
    def add_record(self):
        """إضافة سجل جديد"""
        self.clear_form()
        messagebox.showinfo("إضافة", "تم تفعيل وضع الإضافة")
    
    def save_record(self):
        """حفظ السجل"""
        try:
            # جمع البيانات من النموذج
            data = {
                'unit_number': self.form_vars['unit_number'].get().strip(),
                'unit_name': self.form_vars['unit_name'].get().strip(),
                'unit_type': self.form_vars['unit_type'].get().strip(),
                'parent_unit': self.form_vars['parent_unit'].get().strip(),
                'unit_commander': self.form_vars['unit_commander'].get().strip(),
                'commander_rank': self.form_vars['commander_rank'].get().strip(),
                'location': self.form_vars['location'].get().strip(),
                'phone': self.form_vars['phone'].get().strip(),
                'establishment_date': self.form_vars['establishment_date'].get().strip(),
                'status': self.form_vars['status'].get().strip(),
                'notes': self.form_vars['notes'].get().strip()
            }
            
            # التحقق من البيانات المطلوبة
            required_fields = ['unit_number', 'unit_name', 'unit_type']
            missing_fields = [field for field in required_fields if not data[field]]
            
            if missing_fields:
                messagebox.showerror("خطأ", "يرجى ملء جميع الحقول المطلوبة")
                return
            
            # إنشاء استعلام الإدراج
            query = """
                INSERT INTO units 
                (unit_number, unit_name, unit_type, unit_commander, commander_rank,
                 location, phone, establishment_date, status, notes, is_active)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 1)
            """
            
            params = (
                data['unit_number'], data['unit_name'], data['unit_type'],
                data['unit_commander'], data['commander_rank'], data['location'],
                data['phone'], data['establishment_date'], data['status'], data['notes']
            )
            
            # تنفيذ الاستعلام
            self.db_manager.execute_query(query, params)
            messagebox.showinfo("نجح", "تم حفظ بيانات الوحدة بنجاح")
            
            # إعادة تحميل البيانات
            self.load_data()
            self.clear_form()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في حفظ البيانات: {e}")
    
    def edit_record(self):
        """تعديل السجل"""
        selection = self.tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى تحديد وحدة للتعديل")
            return
        
        messagebox.showinfo("تعديل", "تم تفعيل وضع التعديل")
    
    def delete_record(self):
        """حذف السجل"""
        selection = self.tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى تحديد وحدة للحذف")
            return
        
        if messagebox.askyesno("تأكيد", "هل أنت متأكد من حذف هذه الوحدة؟"):
            self.tree.delete(selection[0])
            messagebox.showinfo("حذف", "تم حذف الوحدة بنجاح")
    
    def cancel_operation(self):
        """إلغاء العملية"""
        self.clear_form()
        messagebox.showinfo("إلغاء", "تم إلغاء العملية")
    
    def print_record(self):
        """طباعة السجل"""
        messagebox.showinfo("طباعة", "سيتم تطوير وظيفة الطباعة قريباً")
    
    def export_data(self):
        """تصدير البيانات"""
        messagebox.showinfo("تصدير", "سيتم تطوير وظيفة التصدير قريباً")
    
    def clear_form(self):
        """مسح النموذج"""
        for var in self.form_vars.values():
            var.set("")
