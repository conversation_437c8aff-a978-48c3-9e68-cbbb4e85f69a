# شاشة المعلومات الأساسية - التوثيق الكامل

## 📋 نظرة عامة

تم إنشاء شاشة المعلومات الأساسية مطابقة تماماً للصورة المرفقة من البرنامج الأصلي. الشاشة تحتوي على جميع الحقول والأزرار والتبويبات والارتباطات المطلوبة.

## 🎨 مكونات الشاشة

### 1. التبويبات العلوية
```
📋 التبويبات المتاحة:
├── القوة العمومية (النشط حالياً)
├── المعدات
├── الأصناف  
├── الأسلحة
├── المستخدمين
└── أدوات الاستعلام
```

### 2. شريط الأزرار
```
🔘 الأزرار المتاحة:
├── 🆕 إضافة - إضافة سجل جديد
├── 💾 حفظ - حفظ البيانات المدخلة
├── ✏️ تعديل - تعديل السجل المحدد
├── 🗑️ حذف - حذف السجل المحدد
└── ❌ إلغاء - إلغاء العملية الحالية
```

### 3. منطقة الإدخال

#### الصف الأول:
- **رقم القوة العمومية*** (مطلوب)
- **رقم الوحدة*** (مطلوب)  
- **اسم القوة العمومية*** (مطلوب)

#### الصف الثاني:
- **اسم آمر الوحدة**
- **رتبة آمر الوحدة** (قائمة منسدلة مربوطة بجدول الرتب)
- **وظيفة آمر الوحدة**

#### الصف الثالث:
- **اسم ضابط التموين**
- **رتبة ضابط التموين** (قائمة منسدلة مربوطة بجدول الرتب)
- **وظيفة ضابط التموين**

### 4. جدول البيانات

```
📊 أعمدة الجدول:
├── الرقم
├── اسم القوة العمومية
├── رقم الوحدة
├── اسم الوحدة
├── اسم القائد
└── ضابط التموين
```

## 🔗 الارتباطات مع قاعدة البيانات

### جدول المعلومات الأساسية (force_basic_info)
```sql
CREATE TABLE force_basic_info (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    force_name TEXT NOT NULL,
    force_number TEXT,
    unit_number TEXT NOT NULL,
    unit_name TEXT,
    commander_name TEXT,
    commander_rank TEXT,
    commander_position TEXT,
    supply_name TEXT,
    supply_rank TEXT,
    supply_position TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

### الارتباطات الخارجية:
- **جدول الرتب (ranks)**: لتحميل قوائم الرتب المنسدلة
- **جدول الوحدات (units)**: للربط مع الوحدات التنظيمية
- **جدول الأفراد (personnel)**: للربط مع بيانات الأفراد

## ⚙️ الوظائف المتاحة

### 1. إضافة سجل جديد
```python
def add_record(self):
    """إضافة سجل جديد"""
    - مسح النموذج
    - تفعيل وضع الإضافة
    - عرض رسالة تأكيد
```

### 2. حفظ البيانات
```python
def save_record(self):
    """حفظ السجل"""
    - التحقق من البيانات المطلوبة
    - إدراج البيانات في قاعدة البيانات
    - إعادة تحميل الجدول
    - مسح النموذج
```

### 3. تعديل السجل
```python
def edit_record(self):
    """تعديل السجل"""
    - التحقق من تحديد سجل
    - تحميل البيانات في النموذج
    - تفعيل وضع التعديل
```

### 4. حذف السجل
```python
def delete_record(self):
    """حذف السجل"""
    - التحقق من تحديد سجل
    - طلب تأكيد الحذف
    - حذف السجل من قاعدة البيانات
    - تحديث الجدول
```

### 5. إلغاء العملية
```python
def cancel_operation(self):
    """إلغاء العملية"""
    - مسح النموذج
    - إعادة تعيين الحالة
    - عرض رسالة إلغاء
```

## 📱 التفاعل مع المستخدم

### التحقق من البيانات:
- **الحقول المطلوبة**: اسم القوة العمومية، رقم الوحدة
- **رسائل الخطأ**: عرض رسائل واضحة عند فقدان البيانات
- **رسائل النجاح**: تأكيد العمليات الناجحة

### التنقل:
- **النقر على الجدول**: تحديد السجل وتحميل بياناته
- **التبويبات**: التنقل بين الشاشات المختلفة
- **الأزرار**: تنفيذ العمليات المختلفة

## 🔧 الملفات المرتبطة

### الملف الرئيسي:
```
src/modules/basic_info_complete.py
```

### الملفات المساعدة:
```
src/ui/main_window.py (التكامل مع النافذة الرئيسية)
src/database/database_manager.py (إدارة قاعدة البيانات)
```

### ملفات الاختبار:
```
test_basic_info_screen.py (اختبار الشاشة)
```

## 🚀 كيفية الاستخدام

### 1. تشغيل النظام:
```bash
cd نظام_التموين_الجديد
python main.py
```

### 2. الوصول للشاشة:
- النقر على زر "المعلومات الأساسية" في شريط الأدوات
- أو من خلال القوائم العلوية

### 3. إضافة بيانات جديدة:
1. النقر على زر "إضافة"
2. ملء الحقول المطلوبة
3. النقر على زر "حفظ"

### 4. تعديل البيانات:
1. تحديد السجل من الجدول
2. النقر على زر "تعديل"
3. تعديل البيانات
4. النقر على زر "حفظ"

### 5. حذف البيانات:
1. تحديد السجل من الجدول
2. النقر على زر "حذف"
3. تأكيد الحذف

## ✅ المميزات المطبقة

- ✅ **التصميم مطابق للصورة**: جميع العناصر في مواضعها الصحيحة
- ✅ **الحقول كاملة**: جميع الحقول المطلوبة موجودة
- ✅ **الأزرار فعالة**: جميع الأزرار تعمل بشكل صحيح
- ✅ **الارتباطات صحيحة**: ربط مع قاعدة البيانات والجداول الأخرى
- ✅ **التحقق من البيانات**: فحص البيانات المطلوبة
- ✅ **رسائل المستخدم**: رسائل واضحة للنجاح والخطأ
- ✅ **التبويبات**: إمكانية التنقل بين الشاشات
- ✅ **الجدول التفاعلي**: عرض وتحديد البيانات
- ✅ **القوائم المنسدلة**: ربط مع جدول الرتب
- ✅ **التصميم العربي**: دعم كامل للغة العربية

## 🔄 التحديثات المستقبلية

- إضافة وظائف البحث والتصفية
- ربط التبويبات الأخرى بشاشاتها المخصصة
- إضافة تصدير البيانات
- تحسين التحقق من البيانات
- إضافة المزيد من التقارير
