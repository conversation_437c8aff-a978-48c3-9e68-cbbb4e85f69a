#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
نافذة تسجيل الدخول
"""

import tkinter as tk
from tkinter import messagebox, ttk
import ttkbootstrap as ttk_bs
from ttkbootstrap.constants import *
from typing import Callable, Optional, Dict, Any
from datetime import datetime
import threading
import os


class LoginWindow:
    """فئة نافذة تسجيل الدخول"""
    
    def __init__(self, db_manager, on_success_callback: Callable[[Dict[str, Any]], None]):
        """
        تهيئة نافذة تسجيل الدخول
        
        Args:
            db_manager: مدير قاعدة البيانات
            on_success_callback: دالة استدعاء عند نجاح تسجيل الدخول
        """
        self.db_manager = db_manager
        self.on_success_callback = on_success_callback
        self.window = None
        self.username_var = None
        self.password_var = None
        self.remember_var = None
        self.login_attempts = 0
        self.max_attempts = 3
        
    def show(self) -> None:
        """عرض نافذة تسجيل الدخول"""
        self.create_window()
        self.create_widgets()
        self.center_window()
        self.window.mainloop()
    
    def create_window(self) -> None:
        """إنشاء النافذة الرئيسية"""
        self.window = ttk_bs.Window(
            title="تسجيل الدخول - نظام إدارة التموين العام",
            themename="cosmo",
            size=(450, 350),
            resizable=(False, False)
        )
        
        # إعداد الأيقونة
        try:
            icon_path = "assets/icon.ico"
            if os.path.exists(icon_path):
                self.window.iconbitmap(icon_path)
        except Exception:
            pass
        
        # إعداد إغلاق النافذة
        self.window.protocol("WM_DELETE_WINDOW", self.on_window_close)
    
    def create_widgets(self) -> None:
        """إنشاء عناصر الواجهة"""
        
        # الإطار الرئيسي
        main_frame = ttk_bs.Frame(self.window, padding=30)
        main_frame.pack(fill=BOTH, expand=True)
        
        # شعار النظام
        logo_frame = ttk_bs.Frame(main_frame)
        logo_frame.pack(pady=(0, 20))
        
        # عنوان النظام
        title_label = ttk_bs.Label(
            logo_frame,
            text="نظام إدارة التموين العام والقوة العمومية",
            font=("Tahoma", 14, "bold"),
            bootstyle="primary"
        )
        title_label.pack()
        
        subtitle_label = ttk_bs.Label(
            logo_frame,
            text="تسجيل الدخول",
            font=("Tahoma", 12),
            bootstyle="secondary"
        )
        subtitle_label.pack(pady=(5, 0))
        
        # إطار تسجيل الدخول
        login_frame = ttk_bs.LabelFrame(
            main_frame,
            text="بيانات تسجيل الدخول",
            padding=20,
            bootstyle="primary"
        )
        login_frame.pack(fill=X, pady=(0, 20))
        
        # متغيرات الإدخال
        self.username_var = tk.StringVar()
        self.password_var = tk.StringVar()
        self.remember_var = tk.BooleanVar()
        
        # حقل اسم المستخدم
        username_label = ttk_bs.Label(
            login_frame,
            text="اسم المستخدم:",
            font=("Tahoma", 10)
        )
        username_label.grid(row=0, column=0, sticky=W, pady=(0, 5))
        
        self.username_entry = ttk_bs.Entry(
            login_frame,
            textvariable=self.username_var,
            font=("Tahoma", 11),
            width=25
        )
        self.username_entry.grid(row=1, column=0, sticky=EW, pady=(0, 15))
        
        # حقل كلمة المرور
        password_label = ttk_bs.Label(
            login_frame,
            text="كلمة المرور:",
            font=("Tahoma", 10)
        )
        password_label.grid(row=2, column=0, sticky=W, pady=(0, 5))
        
        self.password_entry = ttk_bs.Entry(
            login_frame,
            textvariable=self.password_var,
            font=("Tahoma", 11),
            width=25,
            show="*"
        )
        self.password_entry.grid(row=3, column=0, sticky=EW, pady=(0, 15))
        
        # خيار تذكر بيانات الدخول
        remember_check = ttk_bs.Checkbutton(
            login_frame,
            text="تذكر بيانات الدخول",
            variable=self.remember_var,
            bootstyle="primary"
        )
        remember_check.grid(row=4, column=0, sticky=W, pady=(0, 10))
        
        # تكوين الشبكة
        login_frame.columnconfigure(0, weight=1)
        
        # إطار الأزرار
        buttons_frame = ttk_bs.Frame(main_frame)
        buttons_frame.pack(fill=X)
        
        # زر تسجيل الدخول
        self.login_button = ttk_bs.Button(
            buttons_frame,
            text="تسجيل الدخول",
            command=self.login,
            bootstyle="success",
            width=15
        )
        self.login_button.pack(side=RIGHT, padx=(10, 0))
        
        # زر الإلغاء
        cancel_button = ttk_bs.Button(
            buttons_frame,
            text="إلغاء",
            command=self.on_window_close,
            bootstyle="secondary",
            width=15
        )
        cancel_button.pack(side=RIGHT)
        
        # ربط مفاتيح الاختصار
        self.window.bind('<Return>', lambda e: self.login())
        self.window.bind('<Escape>', lambda e: self.on_window_close())
        
        # تركيز على حقل اسم المستخدم
        self.username_entry.focus()
        
        # تحميل بيانات الدخول المحفوظة
        self.load_saved_credentials()
    
    def center_window(self) -> None:
        """توسيط النافذة على الشاشة"""
        self.window.update_idletasks()
        
        # الحصول على أبعاد الشاشة
        screen_width = self.window.winfo_screenwidth()
        screen_height = self.window.winfo_screenheight()
        
        # الحصول على أبعاد النافذة
        window_width = self.window.winfo_width()
        window_height = self.window.winfo_height()
        
        # حساب الموضع
        x = (screen_width - window_width) // 2
        y = (screen_height - window_height) // 2
        
        # تعيين الموضع
        self.window.geometry(f"{window_width}x{window_height}+{x}+{y}")
    
    def login(self) -> None:
        """تسجيل الدخول"""
        username = self.username_var.get().strip()
        password = self.password_var.get().strip()
        
        # التحقق من صحة البيانات
        if not username:
            messagebox.showerror("خطأ", "يرجى إدخال اسم المستخدم")
            self.username_entry.focus()
            return
        
        if not password:
            messagebox.showerror("خطأ", "يرجى إدخال كلمة المرور")
            self.password_entry.focus()
            return
        
        # تعطيل زر تسجيل الدخول أثناء المعالجة
        self.login_button.config(state="disabled", text="جاري التحقق...")
        
        # تشغيل عملية التحقق في خيط منفصل
        threading.Thread(target=self._authenticate_user, args=(username, password), daemon=True).start()
    
    def _authenticate_user(self, username: str, password: str) -> None:
        """التحقق من صحة بيانات المستخدم"""
        try:
            # البحث عن المستخدم في قاعدة البيانات
            users = self.db_manager.search_records(
                "users", 
                username, 
                ["username"]
            )
            
            if not users:
                self._handle_login_failure("اسم المستخدم غير صحيح")
                return
            
            user = users[0]
            
            # التحقق من حالة المستخدم
            if not user.get('is_active', True):
                self._handle_login_failure("حساب المستخدم معطل")
                return
            
            # التحقق من كلمة المرور
            if not self.db_manager.verify_password(password, user['password_hash']):
                self._handle_login_failure("كلمة المرور غير صحيحة")
                return
            
            # تحديث آخر تسجيل دخول
            self.db_manager.update_record(
                "users",
                user['id'],
                {"last_login": datetime.now().isoformat()}
            )
            
            # حفظ بيانات الدخول إذا كان مطلوباً
            if self.remember_var.get():
                self.save_credentials(username)
            else:
                self.clear_saved_credentials()
            
            # إخفاء نافذة تسجيل الدخول
            self.window.after(0, self._handle_login_success, user)
            
        except Exception as e:
            self.window.after(0, self._handle_login_failure, f"خطأ في النظام: {e}")
    
    def _handle_login_success(self, user: Dict[str, Any]) -> None:
        """معالجة نجاح تسجيل الدخول"""
        try:
            # إخفاء النافذة
            self.window.withdraw()
            
            # استدعاء دالة النجاح
            self.on_success_callback(user)
            
            # إغلاق النافذة
            self.window.destroy()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تسجيل الدخول: {e}")
            self.login_button.config(state="normal", text="تسجيل الدخول")
    
    def _handle_login_failure(self, error_message: str) -> None:
        """معالجة فشل تسجيل الدخول"""
        self.login_attempts += 1
        
        # إعادة تفعيل زر تسجيل الدخول
        self.login_button.config(state="normal", text="تسجيل الدخول")
        
        # عرض رسالة الخطأ
        if self.login_attempts >= self.max_attempts:
            messagebox.showerror(
                "تم تجاوز عدد المحاولات",
                f"تم تجاوز العدد المسموح من محاولات تسجيل الدخول ({self.max_attempts})\n"
                "سيتم إغلاق النظام"
            )
            self.on_window_close()
        else:
            remaining = self.max_attempts - self.login_attempts
            messagebox.showerror(
                "خطأ في تسجيل الدخول",
                f"{error_message}\n"
                f"المحاولات المتبقية: {remaining}"
            )
        
        # مسح كلمة المرور
        self.password_var.set("")
        self.password_entry.focus()
    
    def load_saved_credentials(self) -> None:
        """تحميل بيانات الدخول المحفوظة"""
        try:
            import json
            
            config_file = "login_config.json"
            if os.path.exists(config_file):
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    
                    if config.get('remember_login', False):
                        self.username_var.set(config.get('username', ''))
                        self.remember_var.set(True)
                        
                        # تركيز على كلمة المرور إذا كان اسم المستخدم محفوظ
                        if self.username_var.get():
                            self.password_entry.focus()
        except Exception:
            pass
    
    def save_credentials(self, username: str) -> None:
        """حفظ بيانات الدخول"""
        try:
            import json
            
            config = {
                'username': username,
                'remember_login': True
            }
            
            with open("login_config.json", 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
        except Exception:
            pass
    
    def clear_saved_credentials(self) -> None:
        """مسح بيانات الدخول المحفوظة"""
        try:
            import os
            if os.path.exists("login_config.json"):
                os.remove("login_config.json")
        except Exception:
            pass
    
    def on_window_close(self) -> None:
        """معالج إغلاق النافذة"""
        self.window.quit()
        self.window.destroy()
