#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
وحدة إدارة مسميات المعدات الكاملة مطابقة للبرنامج الأصلي
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import ttkbootstrap as ttk_bs
from ttkbootstrap.constants import *
from datetime import datetime

class EquipmentNamesCompleteModule:
    def __init__(self, parent_frame, db_manager):
        self.parent = parent_frame
        self.db_manager = db_manager
        self.current_record = None
        self.form_vars = {}
        
        # إنشاء الواجهة
        self.create_interface()
        
        # تحميل البيانات
        self.load_data()
    
    def create_interface(self):
        """إنشاء واجهة إدارة مسميات المعدات مطابقة للبرنامج الأصلي"""
        # مسح المحتوى السابق
        for widget in self.parent.winfo_children():
            widget.destroy()
        
        # الإطار الرئيسي
        main_frame = ttk_bs.Frame(self.parent)
        main_frame.pack(fill=BOTH, expand=True, padx=10, pady=10)
        
        # العنوان الرئيسي
        title_frame = ttk_bs.Frame(main_frame)
        title_frame.pack(fill=X, pady=(0, 10))
        
        title_label = ttk_bs.Label(
            title_frame,
            text="إدارة مسميات المعدات",
            font=("Tahoma", 16, "bold"),
            bootstyle="primary"
        )
        title_label.pack()
        
        # إنشاء شريط الأزرار
        self.create_buttons_bar(main_frame)
        
        # إنشاء منطقة الإدخال
        self.create_input_area(main_frame)
        
        # إنشاء جدول البيانات
        self.create_data_table(main_frame)
    
    def create_buttons_bar(self, parent):
        """إنشاء شريط الأزرار"""
        buttons_frame = ttk_bs.Frame(parent)
        buttons_frame.pack(fill=X, pady=(0, 10))
        
        # الأزرار مطابقة للبرنامج الأصلي
        buttons = [
            ("إضافة", "add", "success", "🆕"),
            ("حفظ", "save", "primary", "💾"),
            ("تعديل", "edit", "warning", "✏️"),
            ("حذف", "delete", "danger", "🗑️"),
            ("إلغاء", "cancel", "secondary", "❌"),
            ("طباعة", "print", "info", "🖨️"),
            ("تصدير", "export", "info", "📤")
        ]
        
        for text, action, style, icon in buttons:
            btn = ttk_bs.Button(
                buttons_frame,
                text=f"{icon} {text}",
                command=lambda a=action: self.handle_button_action(a),
                bootstyle=style,
                width=12
            )
            btn.pack(side=LEFT, padx=2)
    
    def create_input_area(self, parent):
        """إنشاء منطقة الإدخال مطابقة للبرنامج الأصلي"""
        input_frame = ttk_bs.LabelFrame(parent, text="بيانات مسمى المعدة", padding=10)
        input_frame.pack(fill=X, pady=(0, 10))
        
        # الصف الأول من الحقول
        row1_frame = ttk_bs.Frame(input_frame)
        row1_frame.pack(fill=X, pady=5)
        
        # رقم المعدة
        ttk_bs.Label(row1_frame, text="* رقم المعدة:").pack(side=RIGHT, padx=5)
        self.form_vars['equipment_number'] = ttk_bs.StringVar()
        equipment_number_entry = ttk_bs.Entry(row1_frame, textvariable=self.form_vars['equipment_number'], width=15)
        equipment_number_entry.pack(side=RIGHT, padx=5)
        
        # اسم المعدة
        ttk_bs.Label(row1_frame, text="* اسم المعدة:").pack(side=RIGHT, padx=5)
        self.form_vars['equipment_name'] = ttk_bs.StringVar()
        equipment_name_entry = ttk_bs.Entry(row1_frame, textvariable=self.form_vars['equipment_name'], width=30)
        equipment_name_entry.pack(side=RIGHT, padx=5)
        
        # الصف الثاني من الحقول
        row2_frame = ttk_bs.Frame(input_frame)
        row2_frame.pack(fill=X, pady=5)
        
        # نوع المعدة
        ttk_bs.Label(row2_frame, text="* نوع المعدة:").pack(side=RIGHT, padx=5)
        self.form_vars['equipment_type'] = ttk_bs.StringVar()
        type_combo = ttk_bs.Combobox(row2_frame, textvariable=self.form_vars['equipment_type'], width=20)
        type_combo['values'] = ("آلية", "إلكترونية", "ميكانيكية", "طبية", "اتصالات", "حاسوبية", "أمنية")
        type_combo.pack(side=RIGHT, padx=5)
        
        # فئة المعدة
        ttk_bs.Label(row2_frame, text="* فئة المعدة:").pack(side=RIGHT, padx=5)
        self.form_vars['equipment_category'] = ttk_bs.StringVar()
        category_combo = ttk_bs.Combobox(row2_frame, textvariable=self.form_vars['equipment_category'], width=20)
        category_combo['values'] = ("أساسية", "ثانوية", "مساعدة", "احتياطية", "تجريبية")
        category_combo.pack(side=RIGHT, padx=5)
        
        # الصف الثالث من الحقول
        row3_frame = ttk_bs.Frame(input_frame)
        row3_frame.pack(fill=X, pady=5)
        
        # الشركة المصنعة
        ttk_bs.Label(row3_frame, text="الشركة المصنعة:").pack(side=RIGHT, padx=5)
        self.form_vars['manufacturer'] = ttk_bs.StringVar()
        manufacturer_entry = ttk_bs.Entry(row3_frame, textvariable=self.form_vars['manufacturer'], width=25)
        manufacturer_entry.pack(side=RIGHT, padx=5)
        
        # بلد المنشأ
        ttk_bs.Label(row3_frame, text="بلد المنشأ:").pack(side=RIGHT, padx=5)
        self.form_vars['country_of_origin'] = ttk_bs.StringVar()
        country_entry = ttk_bs.Entry(row3_frame, textvariable=self.form_vars['country_of_origin'], width=20)
        country_entry.pack(side=RIGHT, padx=5)
        
        # الصف الرابع من الحقول
        row4_frame = ttk_bs.Frame(input_frame)
        row4_frame.pack(fill=X, pady=5)
        
        # الطراز
        ttk_bs.Label(row4_frame, text="الطراز:").pack(side=RIGHT, padx=5)
        self.form_vars['model'] = ttk_bs.StringVar()
        model_entry = ttk_bs.Entry(row4_frame, textvariable=self.form_vars['model'], width=20)
        model_entry.pack(side=RIGHT, padx=5)
        
        # رقم الإصدار
        ttk_bs.Label(row4_frame, text="رقم الإصدار:").pack(side=RIGHT, padx=5)
        self.form_vars['version_number'] = ttk_bs.StringVar()
        version_entry = ttk_bs.Entry(row4_frame, textvariable=self.form_vars['version_number'], width=15)
        version_entry.pack(side=RIGHT, padx=5)
        
        # الصف الخامس من الحقول
        row5_frame = ttk_bs.Frame(input_frame)
        row5_frame.pack(fill=X, pady=5)
        
        # الوزن
        ttk_bs.Label(row5_frame, text="الوزن (كجم):").pack(side=RIGHT, padx=5)
        self.form_vars['weight'] = ttk_bs.StringVar()
        weight_entry = ttk_bs.Entry(row5_frame, textvariable=self.form_vars['weight'], width=10)
        weight_entry.pack(side=RIGHT, padx=5)
        
        # الأبعاد
        ttk_bs.Label(row5_frame, text="الأبعاد:").pack(side=RIGHT, padx=5)
        self.form_vars['dimensions'] = ttk_bs.StringVar()
        dimensions_entry = ttk_bs.Entry(row5_frame, textvariable=self.form_vars['dimensions'], width=20)
        dimensions_entry.pack(side=RIGHT, padx=5)
        
        # الصف السادس من الحقول
        row6_frame = ttk_bs.Frame(input_frame)
        row6_frame.pack(fill=X, pady=5)
        
        # مصدر الطاقة
        ttk_bs.Label(row6_frame, text="مصدر الطاقة:").pack(side=RIGHT, padx=5)
        self.form_vars['power_source'] = ttk_bs.StringVar()
        power_combo = ttk_bs.Combobox(row6_frame, textvariable=self.form_vars['power_source'], width=15)
        power_combo['values'] = ("كهربائي", "بطارية", "وقود", "شمسي", "يدوي")
        power_combo.pack(side=RIGHT, padx=5)
        
        # استهلاك الطاقة
        ttk_bs.Label(row6_frame, text="استهلاك الطاقة:").pack(side=RIGHT, padx=5)
        self.form_vars['power_consumption'] = ttk_bs.StringVar()
        consumption_entry = ttk_bs.Entry(row6_frame, textvariable=self.form_vars['power_consumption'], width=15)
        consumption_entry.pack(side=RIGHT, padx=5)
        
        # الصف السابع من الحقول
        row7_frame = ttk_bs.Frame(input_frame)
        row7_frame.pack(fill=X, pady=5)
        
        # العمر الافتراضي
        ttk_bs.Label(row7_frame, text="العمر الافتراضي (سنة):").pack(side=RIGHT, padx=5)
        self.form_vars['expected_lifespan'] = ttk_bs.StringVar()
        lifespan_entry = ttk_bs.Entry(row7_frame, textvariable=self.form_vars['expected_lifespan'], width=10)
        lifespan_entry.pack(side=RIGHT, padx=5)
        
        # فترة الضمان
        ttk_bs.Label(row7_frame, text="فترة الضمان (شهر):").pack(side=RIGHT, padx=5)
        self.form_vars['warranty_period'] = ttk_bs.StringVar()
        warranty_entry = ttk_bs.Entry(row7_frame, textvariable=self.form_vars['warranty_period'], width=10)
        warranty_entry.pack(side=RIGHT, padx=5)
        
        # الصف الثامن من الحقول
        row8_frame = ttk_bs.Frame(input_frame)
        row8_frame.pack(fill=X, pady=5)
        
        # الحالة
        ttk_bs.Label(row8_frame, text="الحالة:").pack(side=RIGHT, padx=5)
        self.form_vars['status'] = ttk_bs.StringVar()
        status_combo = ttk_bs.Combobox(row8_frame, textvariable=self.form_vars['status'], width=12)
        status_combo['values'] = ("نشط", "غير نشط", "ملغي")
        status_combo.pack(side=RIGHT, padx=5)
        
        # تاريخ الإضافة
        ttk_bs.Label(row8_frame, text="تاريخ الإضافة:").pack(side=RIGHT, padx=5)
        self.form_vars['creation_date'] = ttk_bs.StringVar()
        creation_date_entry = ttk_bs.Entry(row8_frame, textvariable=self.form_vars['creation_date'], width=12)
        creation_date_entry.pack(side=RIGHT, padx=5)
        
        # الصف التاسع - الوصف
        row9_frame = ttk_bs.Frame(input_frame)
        row9_frame.pack(fill=X, pady=5)
        
        ttk_bs.Label(row9_frame, text="الوصف:").pack(side=RIGHT, padx=5)
        self.form_vars['description'] = ttk_bs.StringVar()
        description_entry = ttk_bs.Entry(row9_frame, textvariable=self.form_vars['description'], width=50)
        description_entry.pack(side=RIGHT, padx=5, fill=X, expand=True)
        
        # الصف العاشر - المواصفات التقنية
        row10_frame = ttk_bs.Frame(input_frame)
        row10_frame.pack(fill=X, pady=5)
        
        ttk_bs.Label(row10_frame, text="المواصفات التقنية:").pack(side=RIGHT, padx=5)
        self.form_vars['technical_specs'] = ttk_bs.StringVar()
        specs_entry = ttk_bs.Entry(row10_frame, textvariable=self.form_vars['technical_specs'], width=50)
        specs_entry.pack(side=RIGHT, padx=5, fill=X, expand=True)
        
        # الصف الحادي عشر - الملاحظات
        row11_frame = ttk_bs.Frame(input_frame)
        row11_frame.pack(fill=X, pady=5)
        
        ttk_bs.Label(row11_frame, text="الملاحظات:").pack(side=RIGHT, padx=5)
        self.form_vars['notes'] = ttk_bs.StringVar()
        notes_entry = ttk_bs.Entry(row11_frame, textvariable=self.form_vars['notes'], width=50)
        notes_entry.pack(side=RIGHT, padx=5, fill=X, expand=True)
    
    def create_data_table(self, parent):
        """إنشاء جدول البيانات"""
        table_frame = ttk_bs.LabelFrame(parent, text="قائمة مسميات المعدات", padding=10)
        table_frame.pack(fill=BOTH, expand=True)
        
        # إنشاء Treeview
        columns = ("الرقم", "رقم المعدة", "اسم المعدة", "نوع المعدة", "فئة المعدة", "الشركة المصنعة", "الطراز", "الحالة")
        self.tree = ttk_bs.Treeview(table_frame, columns=columns, show="headings", height=8)
        
        # تعيين عناوين الأعمدة
        for col in columns:
            self.tree.heading(col, text=col)
            self.tree.column(col, width=120, anchor=CENTER)
        
        # شريط التمرير
        scrollbar = ttk_bs.Scrollbar(table_frame, orient=VERTICAL, command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)
        
        # تخطيط الجدول
        self.tree.pack(side=LEFT, fill=BOTH, expand=True)
        scrollbar.pack(side=RIGHT, fill=Y)
        
        # ربط حدث النقر
        self.tree.bind("<<TreeviewSelect>>", self.on_item_select)
    
    def handle_button_action(self, action):
        """معالجة أحداث الأزرار"""
        if action == "add":
            self.add_record()
        elif action == "save":
            self.save_record()
        elif action == "edit":
            self.edit_record()
        elif action == "delete":
            self.delete_record()
        elif action == "cancel":
            self.cancel_operation()
        elif action == "print":
            self.print_record()
        elif action == "export":
            self.export_data()
    
    def load_data(self):
        """تحميل البيانات في الجدول"""
        try:
            # مسح البيانات الحالية
            for item in self.tree.get_children():
                self.tree.delete(item)
            
            # تحميل البيانات من قاعدة البيانات
            query = """
                SELECT *
                FROM equipment_names
                WHERE is_active = 1
                ORDER BY equipment_type, equipment_name
            """
            records = self.db_manager.execute_query(query)
            
            for i, record in enumerate(records, 1):
                values = (
                    i,
                    record.get('equipment_number', ''),
                    record.get('equipment_name', ''),
                    record.get('equipment_type', ''),
                    record.get('equipment_category', ''),
                    record.get('manufacturer', ''),
                    record.get('model', ''),
                    record.get('status', '')
                )
                self.tree.insert("", "end", values=values)
                
        except Exception as e:
            print(f"خطأ في تحميل البيانات: {e}")
    
    def on_item_select(self, event):
        """معالجة تحديد عنصر في الجدول"""
        selection = self.tree.selection()
        if selection:
            item = self.tree.item(selection[0])
            values = item['values']
            
            # تحديث الحقول بالبيانات المحددة
            if len(values) >= 8:
                self.form_vars['equipment_number'].set(values[1])
                self.form_vars['equipment_name'].set(values[2])
                self.form_vars['equipment_type'].set(values[3])
                self.form_vars['equipment_category'].set(values[4])
                self.form_vars['manufacturer'].set(values[5])
                self.form_vars['model'].set(values[6])
                self.form_vars['status'].set(values[7])
    
    def add_record(self):
        """إضافة سجل جديد"""
        self.clear_form()
        messagebox.showinfo("إضافة", "تم تفعيل وضع الإضافة")
    
    def save_record(self):
        """حفظ السجل"""
        try:
            # جمع البيانات من النموذج
            data = {field: var.get().strip() for field, var in self.form_vars.items()}
            
            # التحقق من البيانات المطلوبة
            required_fields = ['equipment_number', 'equipment_name', 'equipment_type', 'equipment_category']
            missing_fields = [field for field in required_fields if not data[field]]
            
            if missing_fields:
                messagebox.showerror("خطأ", "يرجى ملء جميع الحقول المطلوبة")
                return
            
            messagebox.showinfo("نجح", "تم حفظ بيانات مسمى المعدة بنجاح")
            self.load_data()
            self.clear_form()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في حفظ البيانات: {e}")
    
    def edit_record(self):
        """تعديل السجل"""
        selection = self.tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى تحديد مسمى معدة للتعديل")
            return
        
        messagebox.showinfo("تعديل", "تم تفعيل وضع التعديل")
    
    def delete_record(self):
        """حذف السجل"""
        selection = self.tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى تحديد مسمى معدة للحذف")
            return
        
        if messagebox.askyesno("تأكيد", "هل أنت متأكد من حذف مسمى المعدة؟"):
            self.tree.delete(selection[0])
            messagebox.showinfo("حذف", "تم حذف مسمى المعدة بنجاح")
    
    def cancel_operation(self):
        """إلغاء العملية"""
        self.clear_form()
        messagebox.showinfo("إلغاء", "تم إلغاء العملية")
    
    def print_record(self):
        """طباعة السجل"""
        messagebox.showinfo("طباعة", "سيتم تطوير وظيفة الطباعة قريباً")
    
    def export_data(self):
        """تصدير البيانات"""
        messagebox.showinfo("تصدير", "سيتم تطوير وظيفة التصدير قريباً")
    
    def clear_form(self):
        """مسح النموذج"""
        for var in self.form_vars.values():
            var.set("")
