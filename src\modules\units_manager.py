#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
مدير الوحدات
"""

from .base_data_manager import BaseDataManager


class UnitsManager(BaseDataManager):
    """فئة إدارة الوحدات"""
    
    def __init__(self, parent_frame, db_manager, logger):
        """
        تهيئة مدير الوحدات
        
        Args:
            parent_frame: الإطار الأب
            db_manager: مدير قاعدة البيانات
            logger: نظام السجلات
        """
        
        # تعريف أعمدة جدول الوحدات
        columns = [
            {
                'id': 'id',
                'text': 'المعرف',
                'width': 80,
                'anchor': 'center',
                'editable': False,
                'searchable': False
            },
            {
                'id': 'name',
                'text': 'اسم الوحدة',
                'width': 250,
                'anchor': 'w',
                'editable': True,
                'searchable': True,
                'required': True,
                'type': 'text'
            },
            {
                'id': 'code',
                'text': 'رمز الوحدة',
                'width': 120,
                'anchor': 'center',
                'editable': True,
                'searchable': True,
                'required': False,
                'type': 'text'
            },
            {
                'id': 'location',
                'text': 'الموقع',
                'width': 200,
                'anchor': 'w',
                'editable': True,
                'searchable': True,
                'required': False,
                'type': 'text'
            },
            {
                'id': 'phone',
                'text': 'الهاتف',
                'width': 150,
                'anchor': 'center',
                'editable': True,
                'searchable': True,
                'required': False,
                'type': 'text'
            },
            {
                'id': 'created_at',
                'text': 'تاريخ الإنشاء',
                'width': 150,
                'anchor': 'center',
                'editable': False,
                'searchable': False,
                'type': 'date'
            }
        ]
        
        # استدعاء الفئة الأساسية
        super().__init__(
            parent_frame=parent_frame,
            db_manager=db_manager,
            logger=logger,
            table_name="units",
            title="إدارة الوحدات",
            columns=columns
        )


def create_units_manager(parent_frame, db_manager, logger):
    """إنشاء مدير الوحدات"""
    return UnitsManager(parent_frame, db_manager, logger)
