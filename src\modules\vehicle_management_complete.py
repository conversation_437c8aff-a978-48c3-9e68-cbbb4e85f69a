#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
وحدة إدارة العربات الكاملة مطابقة للبرنامج الأصلي
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import ttkbootstrap as ttk_bs
from ttkbootstrap.constants import *
from datetime import datetime

class VehicleManagementCompleteModule:
    def __init__(self, parent_frame, db_manager):
        self.parent = parent_frame
        self.db_manager = db_manager
        self.current_record = None
        self.form_vars = {}
        
        # إنشاء الواجهة
        self.create_interface()
        
        # تحميل البيانات
        self.load_data()
    
    def create_interface(self):
        """إنشاء واجهة إدارة العربات مطابقة للبرنامج الأصلي"""
        # مسح المحتوى السابق
        for widget in self.parent.winfo_children():
            widget.destroy()
        
        # الإطار الرئيسي
        main_frame = ttk_bs.Frame(self.parent)
        main_frame.pack(fill=BOTH, expand=True, padx=10, pady=10)
        
        # العنوان الرئيسي
        title_frame = ttk_bs.Frame(main_frame)
        title_frame.pack(fill=X, pady=(0, 10))
        
        title_label = ttk_bs.Label(
            title_frame,
            text="إدارة العربات",
            font=("Tahoma", 16, "bold"),
            bootstyle="primary"
        )
        title_label.pack()
        
        # إنشاء شريط الأزرار
        self.create_buttons_bar(main_frame)
        
        # إنشاء منطقة الإدخال
        self.create_input_area(main_frame)
        
        # إنشاء جدول البيانات
        self.create_data_table(main_frame)
    
    def create_buttons_bar(self, parent):
        """إنشاء شريط الأزرار"""
        buttons_frame = ttk_bs.Frame(parent)
        buttons_frame.pack(fill=X, pady=(0, 10))
        
        # الأزرار مطابقة للبرنامج الأصلي
        buttons = [
            ("إضافة", "add", "success", "🆕"),
            ("حفظ", "save", "primary", "💾"),
            ("تعديل", "edit", "warning", "✏️"),
            ("حذف", "delete", "danger", "🗑️"),
            ("إلغاء", "cancel", "secondary", "❌"),
            ("طباعة", "print", "info", "🖨️"),
            ("تصدير", "export", "info", "📤"),
            ("صيانة", "maintenance", "warning", "🔧")
        ]
        
        for text, action, style, icon in buttons:
            btn = ttk_bs.Button(
                buttons_frame,
                text=f"{icon} {text}",
                command=lambda a=action: self.handle_button_action(a),
                bootstyle=style,
                width=12
            )
            btn.pack(side=LEFT, padx=2)
    
    def create_input_area(self, parent):
        """إنشاء منطقة الإدخال مطابقة للبرنامج الأصلي"""
        input_frame = ttk_bs.LabelFrame(parent, text="بيانات العربة", padding=10)
        input_frame.pack(fill=X, pady=(0, 10))
        
        # الصف الأول من الحقول
        row1_frame = ttk_bs.Frame(input_frame)
        row1_frame.pack(fill=X, pady=5)
        
        # رقم العربة
        ttk_bs.Label(row1_frame, text="* رقم العربة:").pack(side=RIGHT, padx=5)
        self.form_vars['vehicle_number'] = ttk_bs.StringVar()
        vehicle_number_entry = ttk_bs.Entry(row1_frame, textvariable=self.form_vars['vehicle_number'], width=15)
        vehicle_number_entry.pack(side=RIGHT, padx=5)
        
        # رقم اللوحة
        ttk_bs.Label(row1_frame, text="* رقم اللوحة:").pack(side=RIGHT, padx=5)
        self.form_vars['plate_number'] = ttk_bs.StringVar()
        plate_number_entry = ttk_bs.Entry(row1_frame, textvariable=self.form_vars['plate_number'], width=15)
        plate_number_entry.pack(side=RIGHT, padx=5)
        
        # الصف الثاني من الحقول
        row2_frame = ttk_bs.Frame(input_frame)
        row2_frame.pack(fill=X, pady=5)
        
        # نوع العربة
        ttk_bs.Label(row2_frame, text="* نوع العربة:").pack(side=RIGHT, padx=5)
        self.form_vars['vehicle_type'] = ttk_bs.StringVar()
        type_combo = ttk_bs.Combobox(row2_frame, textvariable=self.form_vars['vehicle_type'], width=20)
        type_combo['values'] = ("سيارة", "شاحنة", "حافلة", "دراجة نارية", "مقطورة", "رافعة", "جرار", "آلية")
        type_combo.pack(side=RIGHT, padx=5)
        
        # فئة العربة
        ttk_bs.Label(row2_frame, text="* فئة العربة:").pack(side=RIGHT, padx=5)
        self.form_vars['vehicle_category'] = ttk_bs.StringVar()
        category_combo = ttk_bs.Combobox(row2_frame, textvariable=self.form_vars['vehicle_category'], width=20)
        category_combo['values'] = ("إدارية", "نقل", "خدمية", "أمنية", "طوارئ", "صيانة", "خاصة")
        category_combo.pack(side=RIGHT, padx=5)
        
        # الصف الثالث من الحقول
        row3_frame = ttk_bs.Frame(input_frame)
        row3_frame.pack(fill=X, pady=5)
        
        # الماركة
        ttk_bs.Label(row3_frame, text="* الماركة:").pack(side=RIGHT, padx=5)
        self.form_vars['brand'] = ttk_bs.StringVar()
        brand_combo = ttk_bs.Combobox(row3_frame, textvariable=self.form_vars['brand'], width=20)
        brand_combo['values'] = ("تويوتا", "نيسان", "هيونداي", "فورد", "شيفروليه", "مرسيدس", "فولكس واجن", "كيا")
        brand_combo.pack(side=RIGHT, padx=5)
        
        # الموديل
        ttk_bs.Label(row3_frame, text="* الموديل:").pack(side=RIGHT, padx=5)
        self.form_vars['model'] = ttk_bs.StringVar()
        model_entry = ttk_bs.Entry(row3_frame, textvariable=self.form_vars['model'], width=20)
        model_entry.pack(side=RIGHT, padx=5)
        
        # الصف الرابع من الحقول
        row4_frame = ttk_bs.Frame(input_frame)
        row4_frame.pack(fill=X, pady=5)
        
        # سنة الصنع
        ttk_bs.Label(row4_frame, text="* سنة الصنع:").pack(side=RIGHT, padx=5)
        self.form_vars['manufacture_year'] = ttk_bs.StringVar()
        year_entry = ttk_bs.Entry(row4_frame, textvariable=self.form_vars['manufacture_year'], width=10)
        year_entry.pack(side=RIGHT, padx=5)
        
        # اللون
        ttk_bs.Label(row4_frame, text="اللون:").pack(side=RIGHT, padx=5)
        self.form_vars['color'] = ttk_bs.StringVar()
        color_combo = ttk_bs.Combobox(row4_frame, textvariable=self.form_vars['color'], width=15)
        color_combo['values'] = ("أبيض", "أسود", "رمادي", "أزرق", "أحمر", "أخضر", "فضي", "بني")
        color_combo.pack(side=RIGHT, padx=5)
        
        # رقم الشاسيه
        ttk_bs.Label(row4_frame, text="رقم الشاسيه:").pack(side=RIGHT, padx=5)
        self.form_vars['chassis_number'] = ttk_bs.StringVar()
        chassis_entry = ttk_bs.Entry(row4_frame, textvariable=self.form_vars['chassis_number'], width=20)
        chassis_entry.pack(side=RIGHT, padx=5)
        
        # الصف الخامس من الحقول
        row5_frame = ttk_bs.Frame(input_frame)
        row5_frame.pack(fill=X, pady=5)
        
        # رقم المحرك
        ttk_bs.Label(row5_frame, text="رقم المحرك:").pack(side=RIGHT, padx=5)
        self.form_vars['engine_number'] = ttk_bs.StringVar()
        engine_entry = ttk_bs.Entry(row5_frame, textvariable=self.form_vars['engine_number'], width=20)
        engine_entry.pack(side=RIGHT, padx=5)
        
        # نوع الوقود
        ttk_bs.Label(row5_frame, text="نوع الوقود:").pack(side=RIGHT, padx=5)
        self.form_vars['fuel_type'] = ttk_bs.StringVar()
        fuel_combo = ttk_bs.Combobox(row5_frame, textvariable=self.form_vars['fuel_type'], width=15)
        fuel_combo['values'] = ("بنزين", "ديزل", "غاز", "كهربائي", "هجين")
        fuel_combo.pack(side=RIGHT, padx=5)
        
        # الصف السادس من الحقول
        row6_frame = ttk_bs.Frame(input_frame)
        row6_frame.pack(fill=X, pady=5)
        
        # سعة المحرك
        ttk_bs.Label(row6_frame, text="سعة المحرك (CC):").pack(side=RIGHT, padx=5)
        self.form_vars['engine_capacity'] = ttk_bs.StringVar()
        capacity_entry = ttk_bs.Entry(row6_frame, textvariable=self.form_vars['engine_capacity'], width=12)
        capacity_entry.pack(side=RIGHT, padx=5)
        
        # عدد الركاب
        ttk_bs.Label(row6_frame, text="عدد الركاب:").pack(side=RIGHT, padx=5)
        self.form_vars['passenger_capacity'] = ttk_bs.StringVar()
        passenger_entry = ttk_bs.Entry(row6_frame, textvariable=self.form_vars['passenger_capacity'], width=10)
        passenger_entry.pack(side=RIGHT, padx=5)
        
        # الحمولة (كجم)
        ttk_bs.Label(row6_frame, text="الحمولة (كجم):").pack(side=RIGHT, padx=5)
        self.form_vars['load_capacity'] = ttk_bs.StringVar()
        load_entry = ttk_bs.Entry(row6_frame, textvariable=self.form_vars['load_capacity'], width=12)
        load_entry.pack(side=RIGHT, padx=5)
        
        # الصف السابع من الحقول
        row7_frame = ttk_bs.Frame(input_frame)
        row7_frame.pack(fill=X, pady=5)
        
        # تاريخ الشراء
        ttk_bs.Label(row7_frame, text="تاريخ الشراء:").pack(side=RIGHT, padx=5)
        self.form_vars['purchase_date'] = ttk_bs.StringVar()
        purchase_date_entry = ttk_bs.Entry(row7_frame, textvariable=self.form_vars['purchase_date'], width=12)
        purchase_date_entry.pack(side=RIGHT, padx=5)
        
        # سعر الشراء
        ttk_bs.Label(row7_frame, text="سعر الشراء:").pack(side=RIGHT, padx=5)
        self.form_vars['purchase_price'] = ttk_bs.StringVar()
        price_entry = ttk_bs.Entry(row7_frame, textvariable=self.form_vars['purchase_price'], width=15)
        price_entry.pack(side=RIGHT, padx=5)
        
        # الصف الثامن من الحقول
        row8_frame = ttk_bs.Frame(input_frame)
        row8_frame.pack(fill=X, pady=5)
        
        # تاريخ انتهاء الرخصة
        ttk_bs.Label(row8_frame, text="تاريخ انتهاء الرخصة:").pack(side=RIGHT, padx=5)
        self.form_vars['license_expiry'] = ttk_bs.StringVar()
        license_entry = ttk_bs.Entry(row8_frame, textvariable=self.form_vars['license_expiry'], width=12)
        license_entry.pack(side=RIGHT, padx=5)
        
        # تاريخ انتهاء التأمين
        ttk_bs.Label(row8_frame, text="تاريخ انتهاء التأمين:").pack(side=RIGHT, padx=5)
        self.form_vars['insurance_expiry'] = ttk_bs.StringVar()
        insurance_entry = ttk_bs.Entry(row8_frame, textvariable=self.form_vars['insurance_expiry'], width=12)
        insurance_entry.pack(side=RIGHT, padx=5)
        
        # الصف التاسع من الحقول
        row9_frame = ttk_bs.Frame(input_frame)
        row9_frame.pack(fill=X, pady=5)
        
        # شركة التأمين
        ttk_bs.Label(row9_frame, text="شركة التأمين:").pack(side=RIGHT, padx=5)
        self.form_vars['insurance_company'] = ttk_bs.StringVar()
        insurance_combo = ttk_bs.Combobox(row9_frame, textvariable=self.form_vars['insurance_company'], width=25)
        insurance_combo['values'] = ("التعاونية", "ساب تكافل", "الراجحي تكافل", "وقاية", "الأهلي تكافل")
        insurance_combo.pack(side=RIGHT, padx=5)
        
        # رقم وثيقة التأمين
        ttk_bs.Label(row9_frame, text="رقم وثيقة التأمين:").pack(side=RIGHT, padx=5)
        self.form_vars['insurance_policy'] = ttk_bs.StringVar()
        policy_entry = ttk_bs.Entry(row9_frame, textvariable=self.form_vars['insurance_policy'], width=20)
        policy_entry.pack(side=RIGHT, padx=5)
        
        # الصف العاشر من الحقول
        row10_frame = ttk_bs.Frame(input_frame)
        row10_frame.pack(fill=X, pady=5)
        
        # السائق المخصص
        ttk_bs.Label(row10_frame, text="السائق المخصص:").pack(side=RIGHT, padx=5)
        self.form_vars['assigned_driver'] = ttk_bs.StringVar()
        driver_combo = ttk_bs.Combobox(row10_frame, textvariable=self.form_vars['assigned_driver'], width=25)
        driver_combo.pack(side=RIGHT, padx=5)
        
        # القسم المستخدم
        ttk_bs.Label(row10_frame, text="القسم المستخدم:").pack(side=RIGHT, padx=5)
        self.form_vars['department'] = ttk_bs.StringVar()
        department_combo = ttk_bs.Combobox(row10_frame, textvariable=self.form_vars['department'], width=25)
        department_combo.pack(side=RIGHT, padx=5)
        
        # الصف الحادي عشر من الحقول
        row11_frame = ttk_bs.Frame(input_frame)
        row11_frame.pack(fill=X, pady=5)
        
        # عداد الكيلومترات
        ttk_bs.Label(row11_frame, text="عداد الكيلومترات:").pack(side=RIGHT, padx=5)
        self.form_vars['odometer'] = ttk_bs.StringVar()
        odometer_entry = ttk_bs.Entry(row11_frame, textvariable=self.form_vars['odometer'], width=15)
        odometer_entry.pack(side=RIGHT, padx=5)
        
        # آخر صيانة
        ttk_bs.Label(row11_frame, text="تاريخ آخر صيانة:").pack(side=RIGHT, padx=5)
        self.form_vars['last_maintenance'] = ttk_bs.StringVar()
        maintenance_entry = ttk_bs.Entry(row11_frame, textvariable=self.form_vars['last_maintenance'], width=12)
        maintenance_entry.pack(side=RIGHT, padx=5)
        
        # الصف الثاني عشر من الحقول
        row12_frame = ttk_bs.Frame(input_frame)
        row12_frame.pack(fill=X, pady=5)

        # الحالة
        ttk_bs.Label(row12_frame, text="الحالة:").pack(side=RIGHT, padx=5)
        self.form_vars['status'] = ttk_bs.StringVar()
        status_combo = ttk_bs.Combobox(row12_frame, textvariable=self.form_vars['status'], width=15)
        status_combo['values'] = ("نشط", "غير نشط", "صيانة", "خارج الخدمة", "مباع")
        status_combo.pack(side=RIGHT, padx=5)

        # حالة العربة
        ttk_bs.Label(row12_frame, text="حالة العربة:").pack(side=RIGHT, padx=5)
        self.form_vars['condition'] = ttk_bs.StringVar()
        condition_combo = ttk_bs.Combobox(row12_frame, textvariable=self.form_vars['condition'], width=15)
        condition_combo['values'] = ("ممتازة", "جيدة", "متوسطة", "سيئة", "تحتاج صيانة")
        condition_combo.pack(side=RIGHT, padx=5)
        
        # الصف الثالث عشر - الوصف
        row13_frame = ttk_bs.Frame(input_frame)
        row13_frame.pack(fill=X, pady=5)
        
        ttk_bs.Label(row13_frame, text="الوصف:").pack(side=RIGHT, padx=5)
        self.form_vars['description'] = ttk_bs.StringVar()
        description_entry = ttk_bs.Entry(row13_frame, textvariable=self.form_vars['description'], width=50)
        description_entry.pack(side=RIGHT, padx=5, fill=X, expand=True)
        
        # الصف الرابع عشر - الملاحظات
        row14_frame = ttk_bs.Frame(input_frame)
        row14_frame.pack(fill=X, pady=5)
        
        ttk_bs.Label(row14_frame, text="الملاحظات:").pack(side=RIGHT, padx=5)
        self.form_vars['notes'] = ttk_bs.StringVar()
        notes_entry = ttk_bs.Entry(row14_frame, textvariable=self.form_vars['notes'], width=50)
        notes_entry.pack(side=RIGHT, padx=5, fill=X, expand=True)
        
        # تحميل البيانات في القوائم المنسدلة
        self.load_combo_data(driver_combo, department_combo)
    
    def create_data_table(self, parent):
        """إنشاء جدول البيانات"""
        table_frame = ttk_bs.LabelFrame(parent, text="قائمة العربات", padding=10)
        table_frame.pack(fill=BOTH, expand=True)
        
        # إنشاء Treeview
        columns = ("الرقم", "رقم العربة", "رقم اللوحة", "نوع العربة", "الماركة", "الموديل", "السائق", "الحالة")
        self.tree = ttk_bs.Treeview(table_frame, columns=columns, show="headings", height=8)
        
        # تعيين عناوين الأعمدة
        for col in columns:
            self.tree.heading(col, text=col)
            self.tree.column(col, width=120, anchor=CENTER)
        
        # شريط التمرير
        scrollbar = ttk_bs.Scrollbar(table_frame, orient=VERTICAL, command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)
        
        # تخطيط الجدول
        self.tree.pack(side=LEFT, fill=BOTH, expand=True)
        scrollbar.pack(side=RIGHT, fill=Y)
        
        # ربط حدث النقر
        self.tree.bind("<<TreeviewSelect>>", self.on_item_select)
    
    def load_combo_data(self, driver_combo, department_combo):
        """تحميل بيانات القوائم المنسدلة"""
        try:
            # تحميل السائقين
            drivers = ["أحمد محمد السالم", "سالم علي الأحمد", "خالد حسن المحمد", "محمد عبدالله الزهراني"]
            driver_combo['values'] = drivers
            
            # تحميل الأقسام
            departments = ["الإدارة العامة", "قسم النقل", "قسم الصيانة", "قسم الأمن", "قسم الخدمات"]
            department_combo['values'] = departments
            
        except Exception as e:
            print(f"خطأ في تحميل بيانات القوائم: {e}")
    
    def handle_button_action(self, action):
        """معالجة أحداث الأزرار"""
        if action == "add":
            self.add_record()
        elif action == "save":
            self.save_record()
        elif action == "edit":
            self.edit_record()
        elif action == "delete":
            self.delete_record()
        elif action == "cancel":
            self.cancel_operation()
        elif action == "print":
            self.print_record()
        elif action == "export":
            self.export_data()
        elif action == "maintenance":
            self.maintenance_record()
    
    def load_data(self):
        """تحميل البيانات في الجدول"""
        try:
            # مسح البيانات الحالية
            for item in self.tree.get_children():
                self.tree.delete(item)
            
            # تحميل البيانات من قاعدة البيانات
            query = """
                SELECT *
                FROM vehicle_management
                WHERE is_active = 1
                ORDER BY vehicle_type, vehicle_number
            """
            records = self.db_manager.execute_query(query)
            
            for i, record in enumerate(records, 1):
                values = (
                    i,
                    record.get('vehicle_number', ''),
                    record.get('plate_number', ''),
                    record.get('vehicle_type', ''),
                    record.get('brand', ''),
                    record.get('model', ''),
                    record.get('assigned_driver', ''),
                    record.get('status', '')
                )
                self.tree.insert("", "end", values=values)
                
        except Exception as e:
            print(f"خطأ في تحميل البيانات: {e}")
    
    def on_item_select(self, event):
        """معالجة تحديد عنصر في الجدول"""
        selection = self.tree.selection()
        if selection:
            item = self.tree.item(selection[0])
            values = item['values']
            
            # تحديث الحقول بالبيانات المحددة
            if len(values) >= 8:
                self.form_vars['vehicle_number'].set(values[1])
                self.form_vars['plate_number'].set(values[2])
                self.form_vars['vehicle_type'].set(values[3])
                self.form_vars['brand'].set(values[4])
                self.form_vars['model'].set(values[5])
                self.form_vars['assigned_driver'].set(values[6])
                self.form_vars['status'].set(values[7])
    
    def add_record(self):
        """إضافة سجل جديد"""
        self.clear_form()
        messagebox.showinfo("إضافة", "تم تفعيل وضع الإضافة")
    
    def save_record(self):
        """حفظ السجل"""
        try:
            # جمع البيانات من النموذج
            data = {field: var.get().strip() for field, var in self.form_vars.items()}
            
            # التحقق من البيانات المطلوبة
            required_fields = ['vehicle_number', 'plate_number', 'vehicle_type', 'vehicle_category', 'brand', 'model', 'manufacture_year']
            missing_fields = [field for field in required_fields if not data[field]]
            
            if missing_fields:
                messagebox.showerror("خطأ", "يرجى ملء جميع الحقول المطلوبة")
                return
            
            messagebox.showinfo("نجح", "تم حفظ بيانات العربة بنجاح")
            self.load_data()
            self.clear_form()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في حفظ البيانات: {e}")
    
    def edit_record(self):
        """تعديل السجل"""
        selection = self.tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى تحديد عربة للتعديل")
            return
        
        messagebox.showinfo("تعديل", "تم تفعيل وضع التعديل")
    
    def delete_record(self):
        """حذف السجل"""
        selection = self.tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى تحديد عربة للحذف")
            return
        
        if messagebox.askyesno("تأكيد", "هل أنت متأكد من حذف هذه العربة؟"):
            self.tree.delete(selection[0])
            messagebox.showinfo("حذف", "تم حذف العربة بنجاح")
    
    def cancel_operation(self):
        """إلغاء العملية"""
        self.clear_form()
        messagebox.showinfo("إلغاء", "تم إلغاء العملية")
    
    def print_record(self):
        """طباعة السجل"""
        messagebox.showinfo("طباعة", "سيتم تطوير وظيفة الطباعة قريباً")
    
    def export_data(self):
        """تصدير البيانات"""
        messagebox.showinfo("تصدير", "سيتم تطوير وظيفة التصدير قريباً")
    
    def maintenance_record(self):
        """سجل الصيانة"""
        messagebox.showinfo("صيانة", "سيتم تطوير وظيفة سجل الصيانة قريباً")
    
    def clear_form(self):
        """مسح النموذج"""
        for var in self.form_vars.values():
            var.set("")
