#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
وحدة إدارة صور وشعار التقارير الكاملة مطابقة للبرنامج الأصلي
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import ttkbootstrap as ttk_bs
from ttkbootstrap.constants import *
from datetime import datetime
from PIL import Image, ImageTk
import os

class ReportImagesLogosCompleteModule:
    def __init__(self, parent_frame, db_manager):
        self.parent = parent_frame
        self.db_manager = db_manager
        self.current_record = None
        self.form_vars = {}
        self.current_image = None
        
        # إنشاء الواجهة
        self.create_interface()
        
        # تحميل البيانات
        self.load_data()
    
    def create_interface(self):
        """إنشاء واجهة إدارة صور وشعار التقارير مطابقة للبرنامج الأصلي"""
        # مسح المحتوى السابق
        for widget in self.parent.winfo_children():
            widget.destroy()
        
        # الإطار الرئيسي
        main_frame = ttk_bs.Frame(self.parent)
        main_frame.pack(fill=BOTH, expand=True, padx=10, pady=10)
        
        # العنوان الرئيسي
        title_frame = ttk_bs.Frame(main_frame)
        title_frame.pack(fill=X, pady=(0, 10))
        
        title_label = ttk_bs.Label(
            title_frame,
            text="إدارة صور وشعار التقارير",
            font=("Tahoma", 16, "bold"),
            bootstyle="primary"
        )
        title_label.pack()
        
        # إنشاء شريط الأزرار
        self.create_buttons_bar(main_frame)
        
        # إنشاء منطقة الإدخال
        self.create_input_area(main_frame)
        
        # إنشاء جدول البيانات
        self.create_data_table(main_frame)
    
    def create_buttons_bar(self, parent):
        """إنشاء شريط الأزرار"""
        buttons_frame = ttk_bs.Frame(parent)
        buttons_frame.pack(fill=X, pady=(0, 10))
        
        # الأزرار مطابقة للبرنامج الأصلي
        buttons = [
            ("إضافة", "add", "success", "🆕"),
            ("حفظ", "save", "primary", "💾"),
            ("تعديل", "edit", "warning", "✏️"),
            ("حذف", "delete", "danger", "🗑️"),
            ("إلغاء", "cancel", "secondary", "❌"),
            ("طباعة", "print", "info", "🖨️"),
            ("تصدير", "export", "info", "📤"),
            ("معاينة", "preview", "info", "👁️")
        ]
        
        for text, action, style, icon in buttons:
            btn = ttk_bs.Button(
                buttons_frame,
                text=f"{icon} {text}",
                command=lambda a=action: self.handle_button_action(a),
                bootstyle=style,
                width=12
            )
            btn.pack(side=LEFT, padx=2)
    
    def create_input_area(self, parent):
        """إنشاء منطقة الإدخال مطابقة للبرنامج الأصلي"""
        input_frame = ttk_bs.LabelFrame(parent, text="بيانات الصورة/الشعار", padding=10)
        input_frame.pack(fill=X, pady=(0, 10))
        
        # الصف الأول من الحقول
        row1_frame = ttk_bs.Frame(input_frame)
        row1_frame.pack(fill=X, pady=5)
        
        # رقم الصورة
        ttk_bs.Label(row1_frame, text="* رقم الصورة:").pack(side=RIGHT, padx=5)
        self.form_vars['image_number'] = ttk_bs.StringVar()
        image_number_entry = ttk_bs.Entry(row1_frame, textvariable=self.form_vars['image_number'], width=15)
        image_number_entry.pack(side=RIGHT, padx=5)
        
        # اسم الصورة
        ttk_bs.Label(row1_frame, text="* اسم الصورة:").pack(side=RIGHT, padx=5)
        self.form_vars['image_name'] = ttk_bs.StringVar()
        image_name_entry = ttk_bs.Entry(row1_frame, textvariable=self.form_vars['image_name'], width=30)
        image_name_entry.pack(side=RIGHT, padx=5)
        
        # الصف الثاني من الحقول
        row2_frame = ttk_bs.Frame(input_frame)
        row2_frame.pack(fill=X, pady=5)
        
        # نوع الصورة
        ttk_bs.Label(row2_frame, text="* نوع الصورة:").pack(side=RIGHT, padx=5)
        self.form_vars['image_type'] = ttk_bs.StringVar()
        type_combo = ttk_bs.Combobox(row2_frame, textvariable=self.form_vars['image_type'], width=20)
        type_combo['values'] = ("شعار", "رأسية", "تذييل", "خلفية", "توقيع", "ختم", "أيقونة")
        type_combo.pack(side=RIGHT, padx=5)
        
        # فئة الاستخدام
        ttk_bs.Label(row2_frame, text="* فئة الاستخدام:").pack(side=RIGHT, padx=5)
        self.form_vars['usage_category'] = ttk_bs.StringVar()
        category_combo = ttk_bs.Combobox(row2_frame, textvariable=self.form_vars['usage_category'], width=20)
        category_combo['values'] = ("تقارير رسمية", "تقارير إدارية", "تقارير مالية", "تقارير فنية", "مراسلات", "شهادات")
        category_combo.pack(side=RIGHT, padx=5)
        
        # الصف الثالث من الحقول
        row3_frame = ttk_bs.Frame(input_frame)
        row3_frame.pack(fill=X, pady=5)
        
        # مسار الملف
        ttk_bs.Label(row3_frame, text="* مسار الملف:").pack(side=RIGHT, padx=5)
        self.form_vars['file_path'] = ttk_bs.StringVar()
        file_path_entry = ttk_bs.Entry(row3_frame, textvariable=self.form_vars['file_path'], width=40)
        file_path_entry.pack(side=RIGHT, padx=5)
        
        # زر تصفح
        browse_btn = ttk_bs.Button(
            row3_frame,
            text="تصفح",
            command=self.browse_file,
            bootstyle="info",
            width=10
        )
        browse_btn.pack(side=RIGHT, padx=5)
        
        # الصف الرابع من الحقول
        row4_frame = ttk_bs.Frame(input_frame)
        row4_frame.pack(fill=X, pady=5)
        
        # تنسيق الملف
        ttk_bs.Label(row4_frame, text="تنسيق الملف:").pack(side=RIGHT, padx=5)
        self.form_vars['file_format'] = ttk_bs.StringVar()
        format_combo = ttk_bs.Combobox(row4_frame, textvariable=self.form_vars['file_format'], width=15)
        format_combo['values'] = ("PNG", "JPG", "JPEG", "GIF", "BMP", "SVG", "PDF")
        format_combo.pack(side=RIGHT, padx=5)
        
        # حجم الملف
        ttk_bs.Label(row4_frame, text="حجم الملف (KB):").pack(side=RIGHT, padx=5)
        self.form_vars['file_size'] = ttk_bs.StringVar()
        file_size_entry = ttk_bs.Entry(row4_frame, textvariable=self.form_vars['file_size'], width=12)
        file_size_entry.pack(side=RIGHT, padx=5)
        
        # الصف الخامس من الحقول
        row5_frame = ttk_bs.Frame(input_frame)
        row5_frame.pack(fill=X, pady=5)
        
        # العرض (بكسل)
        ttk_bs.Label(row5_frame, text="العرض (بكسل):").pack(side=RIGHT, padx=5)
        self.form_vars['width'] = ttk_bs.StringVar()
        width_entry = ttk_bs.Entry(row5_frame, textvariable=self.form_vars['width'], width=10)
        width_entry.pack(side=RIGHT, padx=5)
        
        # الارتفاع (بكسل)
        ttk_bs.Label(row5_frame, text="الارتفاع (بكسل):").pack(side=RIGHT, padx=5)
        self.form_vars['height'] = ttk_bs.StringVar()
        height_entry = ttk_bs.Entry(row5_frame, textvariable=self.form_vars['height'], width=10)
        height_entry.pack(side=RIGHT, padx=5)
        
        # دقة الصورة
        ttk_bs.Label(row5_frame, text="دقة الصورة (DPI):").pack(side=RIGHT, padx=5)
        self.form_vars['resolution'] = ttk_bs.StringVar()
        resolution_entry = ttk_bs.Entry(row5_frame, textvariable=self.form_vars['resolution'], width=10)
        resolution_entry.pack(side=RIGHT, padx=5)
        
        # الصف السادس من الحقول
        row6_frame = ttk_bs.Frame(input_frame)
        row6_frame.pack(fill=X, pady=5)
        
        # موضع الصورة
        ttk_bs.Label(row6_frame, text="موضع الصورة:").pack(side=RIGHT, padx=5)
        self.form_vars['position'] = ttk_bs.StringVar()
        position_combo = ttk_bs.Combobox(row6_frame, textvariable=self.form_vars['position'], width=15)
        position_combo['values'] = ("أعلى يمين", "أعلى يسار", "أعلى وسط", "أسفل يمين", "أسفل يسار", "أسفل وسط", "وسط")
        position_combo.pack(side=RIGHT, padx=5)
        
        # الشفافية
        ttk_bs.Label(row6_frame, text="الشفافية (%):").pack(side=RIGHT, padx=5)
        self.form_vars['transparency'] = ttk_bs.StringVar()
        transparency_entry = ttk_bs.Entry(row6_frame, textvariable=self.form_vars['transparency'], width=10)
        transparency_entry.pack(side=RIGHT, padx=5)
        
        # الصف السابع من الحقول
        row7_frame = ttk_bs.Frame(input_frame)
        row7_frame.pack(fill=X, pady=5)
        
        # الحالة
        ttk_bs.Label(row7_frame, text="الحالة:").pack(side=RIGHT, padx=5)
        self.form_vars['status'] = ttk_bs.StringVar()
        status_combo = ttk_bs.Combobox(row7_frame, textvariable=self.form_vars['status'], width=12)
        status_combo['values'] = ("نشط", "غير نشط", "مؤقت")
        status_combo.pack(side=RIGHT, padx=5)
        
        # تاريخ الإضافة
        ttk_bs.Label(row7_frame, text="تاريخ الإضافة:").pack(side=RIGHT, padx=5)
        self.form_vars['creation_date'] = ttk_bs.StringVar()
        creation_date_entry = ttk_bs.Entry(row7_frame, textvariable=self.form_vars['creation_date'], width=12)
        creation_date_entry.pack(side=RIGHT, padx=5)
        
        # الصف الثامن - الوصف
        row8_frame = ttk_bs.Frame(input_frame)
        row8_frame.pack(fill=X, pady=5)
        
        ttk_bs.Label(row8_frame, text="الوصف:").pack(side=RIGHT, padx=5)
        self.form_vars['description'] = ttk_bs.StringVar()
        description_entry = ttk_bs.Entry(row8_frame, textvariable=self.form_vars['description'], width=50)
        description_entry.pack(side=RIGHT, padx=5, fill=X, expand=True)
        
        # الصف التاسع - الملاحظات
        row9_frame = ttk_bs.Frame(input_frame)
        row9_frame.pack(fill=X, pady=5)
        
        ttk_bs.Label(row9_frame, text="الملاحظات:").pack(side=RIGHT, padx=5)
        self.form_vars['notes'] = ttk_bs.StringVar()
        notes_entry = ttk_bs.Entry(row9_frame, textvariable=self.form_vars['notes'], width=50)
        notes_entry.pack(side=RIGHT, padx=5, fill=X, expand=True)
        
        # منطقة عرض الصورة
        self.create_image_preview(input_frame)
    
    def create_image_preview(self, parent):
        """إنشاء منطقة معاينة الصورة"""
        preview_frame = ttk_bs.LabelFrame(parent, text="معاينة الصورة", padding=10)
        preview_frame.pack(fill=X, pady=10)
        
        # إطار عرض الصورة
        self.image_frame = ttk_bs.Frame(preview_frame, width=200, height=150, relief="solid", borderwidth=1)
        self.image_frame.pack(pady=10)
        self.image_frame.pack_propagate(False)
        
        # تسمية توضيحية
        self.image_label = ttk_bs.Label(self.image_frame, text="لا توجد صورة محددة", anchor=CENTER)
        self.image_label.pack(expand=True)
    
    def create_data_table(self, parent):
        """إنشاء جدول البيانات"""
        table_frame = ttk_bs.LabelFrame(parent, text="قائمة الصور والشعارات", padding=10)
        table_frame.pack(fill=BOTH, expand=True)
        
        # إنشاء Treeview
        columns = ("الرقم", "رقم الصورة", "اسم الصورة", "نوع الصورة", "فئة الاستخدام", "تنسيق الملف", "الحجم", "الحالة")
        self.tree = ttk_bs.Treeview(table_frame, columns=columns, show="headings", height=8)
        
        # تعيين عناوين الأعمدة
        for col in columns:
            self.tree.heading(col, text=col)
            self.tree.column(col, width=100, anchor=CENTER)
        
        # شريط التمرير
        scrollbar = ttk_bs.Scrollbar(table_frame, orient=VERTICAL, command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)
        
        # تخطيط الجدول
        self.tree.pack(side=LEFT, fill=BOTH, expand=True)
        scrollbar.pack(side=RIGHT, fill=Y)
        
        # ربط حدث النقر
        self.tree.bind("<<TreeviewSelect>>", self.on_item_select)
    
    def browse_file(self):
        """تصفح واختيار ملف الصورة"""
        file_types = [
            ("ملفات الصور", "*.png *.jpg *.jpeg *.gif *.bmp"),
            ("PNG files", "*.png"),
            ("JPEG files", "*.jpg *.jpeg"),
            ("GIF files", "*.gif"),
            ("BMP files", "*.bmp"),
            ("جميع الملفات", "*.*")
        ]
        
        file_path = filedialog.askopenfilename(
            title="اختيار صورة",
            filetypes=file_types
        )
        
        if file_path:
            self.form_vars['file_path'].set(file_path)
            self.load_image_info(file_path)
            self.display_image_preview(file_path)
    
    def load_image_info(self, file_path):
        """تحميل معلومات الصورة"""
        try:
            # الحصول على معلومات الملف
            file_size = os.path.getsize(file_path) / 1024  # بالكيلوبايت
            file_format = os.path.splitext(file_path)[1][1:].upper()
            
            # الحصول على أبعاد الصورة
            with Image.open(file_path) as img:
                width, height = img.size
            
            # تحديث الحقول
            self.form_vars['file_size'].set(f"{file_size:.2f}")
            self.form_vars['file_format'].set(file_format)
            self.form_vars['width'].set(str(width))
            self.form_vars['height'].set(str(height))
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في قراءة معلومات الصورة: {e}")
    
    def display_image_preview(self, file_path):
        """عرض معاينة الصورة"""
        try:
            # تحميل وتغيير حجم الصورة للمعاينة
            with Image.open(file_path) as img:
                # تغيير الحجم مع الحفاظ على النسبة
                img.thumbnail((180, 130), Image.Resampling.LANCZOS)
                
                # تحويل إلى PhotoImage
                self.current_image = ImageTk.PhotoImage(img)
                
                # عرض الصورة
                self.image_label.configure(image=self.current_image, text="")
                
        except Exception as e:
            self.image_label.configure(image="", text=f"خطأ في عرض الصورة: {e}")
    
    def handle_button_action(self, action):
        """معالجة أحداث الأزرار"""
        if action == "add":
            self.add_record()
        elif action == "save":
            self.save_record()
        elif action == "edit":
            self.edit_record()
        elif action == "delete":
            self.delete_record()
        elif action == "cancel":
            self.cancel_operation()
        elif action == "print":
            self.print_record()
        elif action == "export":
            self.export_data()
        elif action == "preview":
            self.preview_image()
    
    def load_data(self):
        """تحميل البيانات في الجدول"""
        try:
            # مسح البيانات الحالية
            for item in self.tree.get_children():
                self.tree.delete(item)
            
            # تحميل البيانات من قاعدة البيانات
            query = """
                SELECT *
                FROM report_images_logos
                WHERE is_active = 1
                ORDER BY image_type, image_name
            """
            records = self.db_manager.execute_query(query)
            
            for i, record in enumerate(records, 1):
                values = (
                    i,
                    record.get('image_number', ''),
                    record.get('image_name', ''),
                    record.get('image_type', ''),
                    record.get('usage_category', ''),
                    record.get('file_format', ''),
                    record.get('file_size', ''),
                    record.get('status', '')
                )
                self.tree.insert("", "end", values=values)
                
        except Exception as e:
            print(f"خطأ في تحميل البيانات: {e}")
    
    def on_item_select(self, event):
        """معالجة تحديد عنصر في الجدول"""
        selection = self.tree.selection()
        if selection:
            item = self.tree.item(selection[0])
            values = item['values']
            
            # تحديث الحقول بالبيانات المحددة
            if len(values) >= 8:
                self.form_vars['image_number'].set(values[1])
                self.form_vars['image_name'].set(values[2])
                self.form_vars['image_type'].set(values[3])
                self.form_vars['usage_category'].set(values[4])
                self.form_vars['file_format'].set(values[5])
                self.form_vars['file_size'].set(values[6])
                self.form_vars['status'].set(values[7])
    
    def add_record(self):
        """إضافة سجل جديد"""
        self.clear_form()
        messagebox.showinfo("إضافة", "تم تفعيل وضع الإضافة")
    
    def save_record(self):
        """حفظ السجل"""
        try:
            # جمع البيانات من النموذج
            data = {field: var.get().strip() for field, var in self.form_vars.items()}
            
            # التحقق من البيانات المطلوبة
            required_fields = ['image_number', 'image_name', 'image_type', 'usage_category', 'file_path']
            missing_fields = [field for field in required_fields if not data[field]]
            
            if missing_fields:
                messagebox.showerror("خطأ", "يرجى ملء جميع الحقول المطلوبة")
                return
            
            messagebox.showinfo("نجح", "تم حفظ بيانات الصورة بنجاح")
            self.load_data()
            self.clear_form()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في حفظ البيانات: {e}")
    
    def edit_record(self):
        """تعديل السجل"""
        selection = self.tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى تحديد صورة للتعديل")
            return
        
        messagebox.showinfo("تعديل", "تم تفعيل وضع التعديل")
    
    def delete_record(self):
        """حذف السجل"""
        selection = self.tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى تحديد صورة للحذف")
            return
        
        if messagebox.askyesno("تأكيد", "هل أنت متأكد من حذف هذه الصورة؟"):
            self.tree.delete(selection[0])
            messagebox.showinfo("حذف", "تم حذف الصورة بنجاح")
    
    def cancel_operation(self):
        """إلغاء العملية"""
        self.clear_form()
        messagebox.showinfo("إلغاء", "تم إلغاء العملية")
    
    def print_record(self):
        """طباعة السجل"""
        messagebox.showinfo("طباعة", "سيتم تطوير وظيفة الطباعة قريباً")
    
    def export_data(self):
        """تصدير البيانات"""
        messagebox.showinfo("تصدير", "سيتم تطوير وظيفة التصدير قريباً")
    
    def preview_image(self):
        """معاينة الصورة"""
        file_path = self.form_vars['file_path'].get()
        if file_path and os.path.exists(file_path):
            self.display_image_preview(file_path)
            messagebox.showinfo("معاينة", "تم عرض معاينة الصورة")
        else:
            messagebox.showwarning("تحذير", "يرجى تحديد مسار صورة صحيح أولاً")
    
    def clear_form(self):
        """مسح النموذج"""
        for var in self.form_vars.values():
            var.set("")
        self.image_label.configure(image="", text="لا توجد صورة محددة")
        self.current_image = None
