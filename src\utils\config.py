#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
وحدة إعدادات النظام
"""

import os
import json
from typing import Dict, Any, Optional


class Config:
    """فئة إدارة إعدادات النظام"""
    
    def __init__(self, config_file: str = "config.json"):
        """
        تهيئة الإعدادات
        
        Args:
            config_file: مسار ملف الإعدادات
        """
        self.config_file = config_file
        self.settings = self._load_default_settings()
        self.load_settings()
    
    def _load_default_settings(self) -> Dict[str, Any]:
        """تحميل الإعدادات الافتراضية"""
        return {
            # إعدادات التطبيق العامة
            "app": {
                "name": "نظام إدارة التموين العام والقوة العمومية",
                "version": "2.0.0",
                "author": "فريق التطوير",
                "language": "ar",
                "theme": "cosmo",
                "window_size": {
                    "width": 1400,
                    "height": 800
                },
                "window_position": {
                    "x": 100,
                    "y": 50
                }
            },
            
            # إعدادات قاعدة البيانات
            "database": {
                "path": "database/supply_management.db",
                "backup_path": "database/backups/",
                "auto_backup": True,
                "backup_interval_hours": 24,
                "max_backups": 30,
                "connection_timeout": 30,
                "enable_wal_mode": True,
                "cache_size": 10000
            },
            
            # إعدادات الواجهة
            "ui": {
                "font_family": "Tahoma",
                "font_size": 10,
                "rtl_support": True,
                "show_tooltips": True,
                "animation_enabled": True,
                "auto_refresh_interval": 30,
                "items_per_page": 50
            },
            
            # إعدادات التقارير
            "reports": {
                "output_path": "reports/output/",
                "template_path": "reports/templates/",
                "default_format": "pdf",
                "include_logo": True,
                "logo_path": "assets/logo.png",
                "page_size": "A4",
                "margins": {
                    "top": 2.5,
                    "bottom": 2.5,
                    "left": 2.0,
                    "right": 2.0
                }
            },
            
            # إعدادات الأمان
            "security": {
                "session_timeout_minutes": 60,
                "max_login_attempts": 3,
                "password_min_length": 6,
                "enable_audit_log": True,
                "audit_log_path": "logs/audit.log"
            },
            
            # إعدادات الأداء
            "performance": {
                "enable_caching": True,
                "cache_size_mb": 100,
                "lazy_loading": True,
                "batch_size": 1000,
                "enable_indexing": True,
                "optimize_queries": True
            },
            
            # إعدادات السجلات
            "logging": {
                "level": "INFO",
                "file_path": "logs/app.log",
                "max_file_size_mb": 10,
                "backup_count": 5,
                "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
            },
            
            # إعدادات الاستيراد والتصدير
            "import_export": {
                "excel_max_rows": 100000,
                "csv_encoding": "utf-8-sig",
                "date_format": "%Y-%m-%d",
                "datetime_format": "%Y-%m-%d %H:%M:%S",
                "temp_path": "temp/"
            }
        }
    
    def load_settings(self) -> None:
        """تحميل الإعدادات من الملف"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    file_settings = json.load(f)
                    # دمج الإعدادات المحملة مع الافتراضية
                    self._merge_settings(self.settings, file_settings)
        except Exception as e:
            print(f"خطأ في تحميل الإعدادات: {e}")
    
    def save_settings(self) -> bool:
        """حفظ الإعدادات إلى الملف"""
        try:
            # إنشاء المجلد إذا لم يكن موجوداً
            os.makedirs(os.path.dirname(self.config_file) if os.path.dirname(self.config_file) else '.', exist_ok=True)
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.settings, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            print(f"خطأ في حفظ الإعدادات: {e}")
            return False
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        الحصول على قيمة إعداد
        
        Args:
            key: مفتاح الإعداد (يمكن استخدام النقاط للوصول للمستويات العميقة)
            default: القيمة الافتراضية
            
        Returns:
            قيمة الإعداد
        """
        try:
            keys = key.split('.')
            value = self.settings
            
            for k in keys:
                if isinstance(value, dict) and k in value:
                    value = value[k]
                else:
                    return default
            
            return value
        except Exception:
            return default
    
    def set(self, key: str, value: Any) -> None:
        """
        تعيين قيمة إعداد
        
        Args:
            key: مفتاح الإعداد
            value: القيمة الجديدة
        """
        try:
            keys = key.split('.')
            current = self.settings
            
            # الوصول إلى المستوى الأخير
            for k in keys[:-1]:
                if k not in current:
                    current[k] = {}
                current = current[k]
            
            # تعيين القيمة
            current[keys[-1]] = value
        except Exception as e:
            print(f"خطأ في تعيين الإعداد {key}: {e}")
    
    def _merge_settings(self, default: Dict, loaded: Dict) -> None:
        """دمج الإعدادات المحملة مع الافتراضية"""
        for key, value in loaded.items():
            if key in default and isinstance(default[key], dict) and isinstance(value, dict):
                self._merge_settings(default[key], value)
            else:
                default[key] = value
    
    def get_database_path(self) -> str:
        """الحصول على مسار قاعدة البيانات"""
        return self.get('database.path', 'database/supply_management.db')
    
    def get_theme(self) -> str:
        """الحصول على سمة الواجهة"""
        return self.get('app.theme', 'cosmo')

    def get_window_size(self) -> tuple:
        """الحصول على حجم النافذة"""
        width = self.get("ui.window.width", 1200)
        height = self.get("ui.window.height", 700)
        return (width, height)

    def set_window_size(self, width: int, height: int) -> None:
        """تعيين حجم النافذة"""
        self.set("ui.window.width", width)
        self.set("ui.window.height", height)
        self.save()

    def get_language(self) -> str:
        """الحصول على لغة الواجهة"""
        return self.get('app.language', 'ar')

    def set_language(self, language: str) -> None:
        """تعيين اللغة"""
        self.set("app.language", language)
        self.save()
    
    def get_window_size(self) -> tuple:
        """الحصول على حجم النافذة"""
        width = self.get('app.window_size.width', 1400)
        height = self.get('app.window_size.height', 800)
        return (width, height)
    
    def get_window_position(self) -> tuple:
        """الحصول على موضع النافذة"""
        x = self.get('app.window_position.x', 100)
        y = self.get('app.window_position.y', 50)
        return (x, y)
    
    def is_rtl_enabled(self) -> bool:
        """التحقق من تفعيل دعم RTL"""
        return self.get('ui.rtl_support', True)
    
    def get_items_per_page(self) -> int:
        """الحصول على عدد العناصر في الصفحة"""
        return self.get('ui.items_per_page', 50)
    
    def get_session_timeout(self) -> int:
        """الحصول على مهلة انتهاء الجلسة بالدقائق"""
        return self.get('security.session_timeout_minutes', 60)
    
    def reset_to_defaults(self) -> None:
        """إعادة تعيين الإعدادات إلى القيم الافتراضية"""
        self.settings = self._load_default_settings()
        self.save_settings()
