#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
وحدة نظام التقارير الكاملة مطابقة للبرنامج الأصلي
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import ttkbootstrap as ttk_bs
from ttkbootstrap.constants import *
from datetime import datetime, date
import os

class ReportsSystemCompleteModule:
    def __init__(self, parent_frame, db_manager):
        self.parent = parent_frame
        self.db_manager = db_manager
        self.current_record = None
        self.form_vars = {}
        
        # إنشاء الواجهة
        self.create_interface()
        
        # تحميل البيانات
        self.load_data()
    
    def create_interface(self):
        """إنشاء واجهة نظام التقارير مطابقة للبرنامج الأصلي"""
        # مسح المحتوى السابق
        for widget in self.parent.winfo_children():
            widget.destroy()
        
        # الإطار الرئيسي
        main_frame = ttk_bs.Frame(self.parent)
        main_frame.pack(fill=BOTH, expand=True, padx=10, pady=10)
        
        # العنوان الرئيسي
        title_frame = ttk_bs.Frame(main_frame)
        title_frame.pack(fill=X, pady=(0, 10))
        
        title_label = ttk_bs.Label(
            title_frame,
            text="نظام التقارير",
            font=("Tahoma", 16, "bold"),
            bootstyle="primary"
        )
        title_label.pack()
        
        # إنشاء شريط الأزرار
        self.create_buttons_bar(main_frame)
        
        # إنشاء منطقة الإدخال
        self.create_input_area(main_frame)
        
        # إنشاء جدول البيانات
        self.create_data_table(main_frame)
    
    def create_buttons_bar(self, parent):
        """إنشاء شريط الأزرار"""
        buttons_frame = ttk_bs.Frame(parent)
        buttons_frame.pack(fill=X, pady=(0, 10))
        
        # الأزرار مطابقة للبرنامج الأصلي
        buttons = [
            ("إنشاء تقرير", "create", "success", "📊"),
            ("حفظ التقرير", "save", "primary", "💾"),
            ("تعديل", "edit", "warning", "✏️"),
            ("حذف", "delete", "danger", "🗑️"),
            ("إلغاء", "cancel", "secondary", "❌"),
            ("طباعة", "print", "info", "🖨️"),
            ("معاينة", "preview", "info", "👁️"),
            ("تصدير PDF", "export_pdf", "success", "📄"),
            ("تصدير Excel", "export_excel", "success", "📊"),
            ("إرسال بريد", "email", "warning", "📧")
        ]
        
        for text, action, style, icon in buttons:
            btn = ttk_bs.Button(
                buttons_frame,
                text=f"{icon} {text}",
                command=lambda a=action: self.handle_button_action(a),
                bootstyle=style,
                width=12
            )
            btn.pack(side=LEFT, padx=2)
    
    def create_input_area(self, parent):
        """إنشاء منطقة الإدخال مطابقة للبرنامج الأصلي"""
        input_frame = ttk_bs.LabelFrame(parent, text="إعدادات التقرير", padding=10)
        input_frame.pack(fill=X, pady=(0, 10))
        
        # الصف الأول من الحقول
        row1_frame = ttk_bs.Frame(input_frame)
        row1_frame.pack(fill=X, pady=5)
        
        # رقم التقرير
        ttk_bs.Label(row1_frame, text="* رقم التقرير:").pack(side=RIGHT, padx=5)
        self.form_vars['report_number'] = ttk_bs.StringVar()
        report_number_entry = ttk_bs.Entry(row1_frame, textvariable=self.form_vars['report_number'], width=15)
        report_number_entry.pack(side=RIGHT, padx=5)
        
        # اسم التقرير
        ttk_bs.Label(row1_frame, text="* اسم التقرير:").pack(side=RIGHT, padx=5)
        self.form_vars['report_name'] = ttk_bs.StringVar()
        report_name_entry = ttk_bs.Entry(row1_frame, textvariable=self.form_vars['report_name'], width=30)
        report_name_entry.pack(side=RIGHT, padx=5)
        
        # الصف الثاني من الحقول
        row2_frame = ttk_bs.Frame(input_frame)
        row2_frame.pack(fill=X, pady=5)
        
        # نوع التقرير
        ttk_bs.Label(row2_frame, text="* نوع التقرير:").pack(side=RIGHT, padx=5)
        self.form_vars['report_type'] = ttk_bs.StringVar()
        type_combo = ttk_bs.Combobox(row2_frame, textvariable=self.form_vars['report_type'], width=25)
        type_combo['values'] = (
            "تقرير المخزون", "تقرير المعدات", "تقرير الأفراد", "تقرير العربات", 
            "تقرير الأسلحة", "تقرير مالي", "تقرير إحصائي", "تقرير شهري", 
            "تقرير سنوي", "تقرير مخصص"
        )
        type_combo.pack(side=RIGHT, padx=5)
        
        # فئة التقرير
        ttk_bs.Label(row2_frame, text="* فئة التقرير:").pack(side=RIGHT, padx=5)
        self.form_vars['report_category'] = ttk_bs.StringVar()
        category_combo = ttk_bs.Combobox(row2_frame, textvariable=self.form_vars['report_category'], width=20)
        category_combo['values'] = ("إداري", "مالي", "فني", "أمني", "إحصائي", "تشغيلي")
        category_combo.pack(side=RIGHT, padx=5)
        
        # الصف الثالث من الحقول
        row3_frame = ttk_bs.Frame(input_frame)
        row3_frame.pack(fill=X, pady=5)
        
        # من تاريخ
        ttk_bs.Label(row3_frame, text="* من تاريخ:").pack(side=RIGHT, padx=5)
        self.form_vars['date_from'] = ttk_bs.StringVar()
        date_from_entry = ttk_bs.Entry(row3_frame, textvariable=self.form_vars['date_from'], width=12)
        date_from_entry.pack(side=RIGHT, padx=5)
        
        # إلى تاريخ
        ttk_bs.Label(row3_frame, text="* إلى تاريخ:").pack(side=RIGHT, padx=5)
        self.form_vars['date_to'] = ttk_bs.StringVar()
        date_to_entry = ttk_bs.Entry(row3_frame, textvariable=self.form_vars['date_to'], width=12)
        date_to_entry.pack(side=RIGHT, padx=5)
        
        # الفترة
        ttk_bs.Label(row3_frame, text="الفترة:").pack(side=RIGHT, padx=5)
        self.form_vars['period'] = ttk_bs.StringVar()
        period_combo = ttk_bs.Combobox(row3_frame, textvariable=self.form_vars['period'], width=15)
        period_combo['values'] = ("يومي", "أسبوعي", "شهري", "ربع سنوي", "نصف سنوي", "سنوي", "مخصص")
        period_combo.pack(side=RIGHT, padx=5)
        
        # الصف الرابع من الحقول
        row4_frame = ttk_bs.Frame(input_frame)
        row4_frame.pack(fill=X, pady=5)
        
        # القسم
        ttk_bs.Label(row4_frame, text="القسم:").pack(side=RIGHT, padx=5)
        self.form_vars['department'] = ttk_bs.StringVar()
        department_combo = ttk_bs.Combobox(row4_frame, textvariable=self.form_vars['department'], width=25)
        department_combo['values'] = ("جميع الأقسام", "الإدارة العامة", "قسم المخازن", "قسم المعدات", "قسم الأفراد")
        department_combo.pack(side=RIGHT, padx=5)
        
        # الوحدة
        ttk_bs.Label(row4_frame, text="الوحدة:").pack(side=RIGHT, padx=5)
        self.form_vars['unit'] = ttk_bs.StringVar()
        unit_combo = ttk_bs.Combobox(row4_frame, textvariable=self.form_vars['unit'], width=25)
        unit_combo['values'] = ("جميع الوحدات", "الوحدة الأولى", "الوحدة الثانية", "وحدة الأمن", "وحدة الطوارئ")
        unit_combo.pack(side=RIGHT, padx=5)
        
        # الصف الخامس من الحقول
        row5_frame = ttk_bs.Frame(input_frame)
        row5_frame.pack(fill=X, pady=5)
        
        # تنسيق التقرير
        ttk_bs.Label(row5_frame, text="تنسيق التقرير:").pack(side=RIGHT, padx=5)
        self.form_vars['format'] = ttk_bs.StringVar()
        format_combo = ttk_bs.Combobox(row5_frame, textvariable=self.form_vars['format'], width=15)
        format_combo['values'] = ("PDF", "Excel", "Word", "HTML", "CSV")
        format_combo.pack(side=RIGHT, padx=5)
        
        # اتجاه الصفحة
        ttk_bs.Label(row5_frame, text="اتجاه الصفحة:").pack(side=RIGHT, padx=5)
        self.form_vars['orientation'] = ttk_bs.StringVar()
        orientation_combo = ttk_bs.Combobox(row5_frame, textvariable=self.form_vars['orientation'], width=15)
        orientation_combo['values'] = ("عمودي", "أفقي")
        orientation_combo.pack(side=RIGHT, padx=5)
        
        # حجم الصفحة
        ttk_bs.Label(row5_frame, text="حجم الصفحة:").pack(side=RIGHT, padx=5)
        self.form_vars['page_size'] = ttk_bs.StringVar()
        page_size_combo = ttk_bs.Combobox(row5_frame, textvariable=self.form_vars['page_size'], width=10)
        page_size_combo['values'] = ("A4", "A3", "Letter", "Legal")
        page_size_combo.pack(side=RIGHT, padx=5)
        
        # الصف السادس من الحقول
        row6_frame = ttk_bs.Frame(input_frame)
        row6_frame.pack(fill=X, pady=5)
        
        # تضمين الرسوم البيانية
        self.form_vars['include_charts'] = ttk_bs.BooleanVar()
        charts_check = ttk_bs.Checkbutton(
            row6_frame, 
            text="تضمين الرسوم البيانية", 
            variable=self.form_vars['include_charts']
        )
        charts_check.pack(side=RIGHT, padx=5)
        
        # تضمين الصور
        self.form_vars['include_images'] = ttk_bs.BooleanVar()
        images_check = ttk_bs.Checkbutton(
            row6_frame, 
            text="تضمين الصور", 
            variable=self.form_vars['include_images']
        )
        images_check.pack(side=RIGHT, padx=5)
        
        # تضمين التوقيعات
        self.form_vars['include_signatures'] = ttk_bs.BooleanVar()
        signatures_check = ttk_bs.Checkbutton(
            row6_frame, 
            text="تضمين التوقيعات", 
            variable=self.form_vars['include_signatures']
        )
        signatures_check.pack(side=RIGHT, padx=5)
        
        # الصف السابع من الحقول
        row7_frame = ttk_bs.Frame(input_frame)
        row7_frame.pack(fill=X, pady=5)
        
        # مستوى التفصيل
        ttk_bs.Label(row7_frame, text="مستوى التفصيل:").pack(side=RIGHT, padx=5)
        self.form_vars['detail_level'] = ttk_bs.StringVar()
        detail_combo = ttk_bs.Combobox(row7_frame, textvariable=self.form_vars['detail_level'], width=15)
        detail_combo['values'] = ("ملخص", "تفصيلي", "شامل")
        detail_combo.pack(side=RIGHT, padx=5)
        
        # ترتيب البيانات
        ttk_bs.Label(row7_frame, text="ترتيب البيانات:").pack(side=RIGHT, padx=5)
        self.form_vars['sort_order'] = ttk_bs.StringVar()
        sort_combo = ttk_bs.Combobox(row7_frame, textvariable=self.form_vars['sort_order'], width=15)
        sort_combo['values'] = ("تصاعدي", "تنازلي", "حسب التاريخ", "حسب الاسم", "حسب الرقم")
        sort_combo.pack(side=RIGHT, padx=5)
        
        # الصف الثامن من الحقول
        row8_frame = ttk_bs.Frame(input_frame)
        row8_frame.pack(fill=X, pady=5)
        
        # المعد بواسطة
        ttk_bs.Label(row8_frame, text="المعد بواسطة:").pack(side=RIGHT, padx=5)
        self.form_vars['prepared_by'] = ttk_bs.StringVar()
        prepared_combo = ttk_bs.Combobox(row8_frame, textvariable=self.form_vars['prepared_by'], width=25)
        prepared_combo.pack(side=RIGHT, padx=5)
        
        # المراجع بواسطة
        ttk_bs.Label(row8_frame, text="المراجع بواسطة:").pack(side=RIGHT, padx=5)
        self.form_vars['reviewed_by'] = ttk_bs.StringVar()
        reviewed_combo = ttk_bs.Combobox(row8_frame, textvariable=self.form_vars['reviewed_by'], width=25)
        reviewed_combo.pack(side=RIGHT, padx=5)
        
        # الصف التاسع من الحقول
        row9_frame = ttk_bs.Frame(input_frame)
        row9_frame.pack(fill=X, pady=5)
        
        # المعتمد بواسطة
        ttk_bs.Label(row9_frame, text="المعتمد بواسطة:").pack(side=RIGHT, padx=5)
        self.form_vars['approved_by'] = ttk_bs.StringVar()
        approved_combo = ttk_bs.Combobox(row9_frame, textvariable=self.form_vars['approved_by'], width=25)
        approved_combo.pack(side=RIGHT, padx=5)
        
        # حالة التقرير
        ttk_bs.Label(row9_frame, text="حالة التقرير:").pack(side=RIGHT, padx=5)
        self.form_vars['status'] = ttk_bs.StringVar()
        status_combo = ttk_bs.Combobox(row9_frame, textvariable=self.form_vars['status'], width=15)
        status_combo['values'] = ("مسودة", "قيد المراجعة", "معتمد", "منشور", "مؤرشف")
        status_combo.pack(side=RIGHT, padx=5)
        
        # الصف العاشر - الوصف
        row10_frame = ttk_bs.Frame(input_frame)
        row10_frame.pack(fill=X, pady=5)
        
        ttk_bs.Label(row10_frame, text="وصف التقرير:").pack(side=RIGHT, padx=5)
        self.form_vars['description'] = ttk_bs.StringVar()
        description_entry = ttk_bs.Entry(row10_frame, textvariable=self.form_vars['description'], width=60)
        description_entry.pack(side=RIGHT, padx=5, fill=X, expand=True)
        
        # الصف الحادي عشر - الملاحظات
        row11_frame = ttk_bs.Frame(input_frame)
        row11_frame.pack(fill=X, pady=5)
        
        ttk_bs.Label(row11_frame, text="الملاحظات:").pack(side=RIGHT, padx=5)
        self.form_vars['notes'] = ttk_bs.StringVar()
        notes_entry = ttk_bs.Entry(row11_frame, textvariable=self.form_vars['notes'], width=60)
        notes_entry.pack(side=RIGHT, padx=5, fill=X, expand=True)
        
        # تحميل البيانات في القوائم المنسدلة
        self.load_combo_data(prepared_combo, reviewed_combo, approved_combo)
    
    def create_data_table(self, parent):
        """إنشاء جدول البيانات"""
        table_frame = ttk_bs.LabelFrame(parent, text="قائمة التقارير", padding=10)
        table_frame.pack(fill=BOTH, expand=True)
        
        # إنشاء Treeview
        columns = ("الرقم", "رقم التقرير", "اسم التقرير", "نوع التقرير", "الفترة", "المعد بواسطة", "تاريخ الإنشاء", "الحالة")
        self.tree = ttk_bs.Treeview(table_frame, columns=columns, show="headings", height=8)
        
        # تعيين عناوين الأعمدة
        for col in columns:
            self.tree.heading(col, text=col)
            self.tree.column(col, width=120, anchor=CENTER)
        
        # شريط التمرير
        scrollbar = ttk_bs.Scrollbar(table_frame, orient=VERTICAL, command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)
        
        # تخطيط الجدول
        self.tree.pack(side=LEFT, fill=BOTH, expand=True)
        scrollbar.pack(side=RIGHT, fill=Y)
        
        # ربط حدث النقر
        self.tree.bind("<<TreeviewSelect>>", self.on_item_select)
    
    def load_combo_data(self, prepared_combo, reviewed_combo, approved_combo):
        """تحميل بيانات القوائم المنسدلة"""
        try:
            # تحميل الأفراد
            personnel = ["أحمد محمد السالم", "سالم علي الأحمد", "خالد حسن المحمد", "محمد عبدالله الزهراني"]
            prepared_combo['values'] = personnel
            reviewed_combo['values'] = personnel
            approved_combo['values'] = personnel
            
        except Exception as e:
            print(f"خطأ في تحميل بيانات القوائم: {e}")
    
    def handle_button_action(self, action):
        """معالجة أحداث الأزرار"""
        if action == "create":
            self.create_report()
        elif action == "save":
            self.save_record()
        elif action == "edit":
            self.edit_record()
        elif action == "delete":
            self.delete_record()
        elif action == "cancel":
            self.cancel_operation()
        elif action == "print":
            self.print_record()
        elif action == "preview":
            self.preview_report()
        elif action == "export_pdf":
            self.export_pdf()
        elif action == "export_excel":
            self.export_excel()
        elif action == "email":
            self.send_email()
    
    def load_data(self):
        """تحميل البيانات في الجدول"""
        try:
            # مسح البيانات الحالية
            for item in self.tree.get_children():
                self.tree.delete(item)
            
            # بيانات تجريبية للتقارير
            sample_reports = [
                ("1", "RPT001", "تقرير المخزون الشهري", "تقرير المخزون", "شهري", "أحمد محمد", "2024-01-15", "معتمد"),
                ("2", "RPT002", "تقرير المعدات السنوي", "تقرير المعدات", "سنوي", "سالم علي", "2024-01-20", "قيد المراجعة"),
                ("3", "RPT003", "تقرير الأفراد الربع سنوي", "تقرير الأفراد", "ربع سنوي", "خالد حسن", "2024-01-25", "مسودة")
            ]
            
            for report in sample_reports:
                self.tree.insert("", "end", values=report)
                
        except Exception as e:
            print(f"خطأ في تحميل البيانات: {e}")
    
    def on_item_select(self, event):
        """معالجة تحديد عنصر في الجدول"""
        selection = self.tree.selection()
        if selection:
            item = self.tree.item(selection[0])
            values = item['values']
            
            # تحديث الحقول بالبيانات المحددة
            if len(values) >= 8:
                self.form_vars['report_number'].set(values[1])
                self.form_vars['report_name'].set(values[2])
                self.form_vars['report_type'].set(values[3])
                self.form_vars['period'].set(values[4])
                self.form_vars['prepared_by'].set(values[5])
                self.form_vars['status'].set(values[7])
    
    def create_report(self):
        """إنشاء تقرير جديد"""
        self.clear_form()
        # تعيين قيم افتراضية
        self.form_vars['report_number'].set(f"RPT{datetime.now().strftime('%Y%m%d%H%M')}")
        self.form_vars['date_from'].set(date.today().strftime('%Y-%m-%d'))
        self.form_vars['date_to'].set(date.today().strftime('%Y-%m-%d'))
        self.form_vars['format'].set("PDF")
        self.form_vars['orientation'].set("عمودي")
        self.form_vars['page_size'].set("A4")
        self.form_vars['detail_level'].set("تفصيلي")
        self.form_vars['sort_order'].set("تصاعدي")
        self.form_vars['status'].set("مسودة")
        messagebox.showinfo("إنشاء تقرير", "تم تفعيل وضع إنشاء تقرير جديد")
    
    def save_record(self):
        """حفظ التقرير"""
        try:
            # جمع البيانات من النموذج
            data = {field: var.get().strip() if hasattr(var, 'get') else var.get() for field, var in self.form_vars.items()}
            
            # التحقق من البيانات المطلوبة
            required_fields = ['report_number', 'report_name', 'report_type', 'report_category', 'date_from', 'date_to']
            missing_fields = [field for field in required_fields if not data[field]]
            
            if missing_fields:
                messagebox.showerror("خطأ", "يرجى ملء جميع الحقول المطلوبة")
                return
            
            messagebox.showinfo("نجح", "تم حفظ إعدادات التقرير بنجاح")
            self.load_data()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في حفظ البيانات: {e}")
    
    def edit_record(self):
        """تعديل التقرير"""
        selection = self.tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى تحديد تقرير للتعديل")
            return
        
        messagebox.showinfo("تعديل", "تم تفعيل وضع التعديل")
    
    def delete_record(self):
        """حذف التقرير"""
        selection = self.tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى تحديد تقرير للحذف")
            return
        
        if messagebox.askyesno("تأكيد", "هل أنت متأكد من حذف هذا التقرير؟"):
            self.tree.delete(selection[0])
            messagebox.showinfo("حذف", "تم حذف التقرير بنجاح")
    
    def cancel_operation(self):
        """إلغاء العملية"""
        self.clear_form()
        messagebox.showinfo("إلغاء", "تم إلغاء العملية")
    
    def print_record(self):
        """طباعة التقرير"""
        messagebox.showinfo("طباعة", "سيتم تطوير وظيفة الطباعة قريباً")
    
    def preview_report(self):
        """معاينة التقرير"""
        messagebox.showinfo("معاينة", "سيتم تطوير وظيفة المعاينة قريباً")
    
    def export_pdf(self):
        """تصدير PDF"""
        messagebox.showinfo("تصدير PDF", "سيتم تطوير وظيفة تصدير PDF قريباً")
    
    def export_excel(self):
        """تصدير Excel"""
        messagebox.showinfo("تصدير Excel", "سيتم تطوير وظيفة تصدير Excel قريباً")
    
    def send_email(self):
        """إرسال بريد إلكتروني"""
        messagebox.showinfo("إرسال بريد", "سيتم تطوير وظيفة إرسال البريد الإلكتروني قريباً")
    
    def clear_form(self):
        """مسح النموذج"""
        for var in self.form_vars.values():
            if hasattr(var, 'set'):
                var.set("")
