#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
وحدة إدارة قاعدة البيانات
"""

import sqlite3
import os
import threading
import hashlib
from datetime import datetime
from typing import List, Dict, Any, Optional, Tuple
from contextlib import contextmanager


class DatabaseManager:
    """فئة إدارة قاعدة البيانات"""
    
    def __init__(self, db_path: str):
        """
        تهيئة مدير قاعدة البيانات
        
        Args:
            db_path: مسار قاعدة البيانات
        """
        self.db_path = db_path
        self.connection = None
        self.lock = threading.Lock()
        
        # إنشاء مجلد قاعدة البيانات إذا لم يكن موجوداً
        os.makedirs(os.path.dirname(db_path), exist_ok=True)
        
        # تهيئة قاعدة البيانات
        self.init_database()
    
    def init_database(self) -> None:
        """تهيئة قاعدة البيانات وإنشاء الجداول"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                # تفعيل دعم المفاتيح الخارجية
                cursor.execute("PRAGMA foreign_keys = ON")
                
                # تحسين الأداء
                cursor.execute("PRAGMA journal_mode = WAL")
                cursor.execute("PRAGMA synchronous = NORMAL")
                cursor.execute("PRAGMA cache_size = 10000")
                cursor.execute("PRAGMA temp_store = MEMORY")
                
                # إنشاء الجداول
                self._create_tables(cursor)
                
                # إنشاء الفهارس
                self._create_indexes(cursor)
                
                # إدراج البيانات الأساسية
                self._insert_default_data(cursor)
                
                conn.commit()
                
        except Exception as e:
            raise Exception(f"خطأ في تهيئة قاعدة البيانات: {e}")
    
    @contextmanager
    def get_connection(self):
        """الحصول على اتصال قاعدة البيانات مع إدارة الموارد"""
        conn = None
        try:
            with self.lock:
                conn = sqlite3.connect(
                    self.db_path,
                    timeout=30.0,
                    check_same_thread=False
                )
                conn.row_factory = sqlite3.Row
                yield conn
        except Exception as e:
            if conn:
                conn.rollback()
            raise e
        finally:
            if conn:
                conn.close()
    
    def _create_tables(self, cursor: sqlite3.Cursor) -> None:
        """إنشاء جداول قاعدة البيانات"""
        
        # جدول المستخدمين
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT UNIQUE NOT NULL,
                password_hash TEXT NOT NULL,
                full_name TEXT NOT NULL,
                email TEXT,
                role TEXT NOT NULL DEFAULT 'user',
                is_active BOOLEAN DEFAULT 1,
                last_login DATETIME,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # جدول الرتب
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS ranks (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT UNIQUE NOT NULL,
                abbreviation TEXT,
                order_number INTEGER,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # جدول المسميات الوظيفية
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS job_titles (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT UNIQUE NOT NULL,
                description TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # جدول الوحدات
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS units (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT UNIQUE NOT NULL,
                code TEXT UNIQUE,
                parent_unit_id INTEGER,
                commander_id INTEGER,
                location TEXT,
                phone TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (parent_unit_id) REFERENCES units(id),
                FOREIGN KEY (commander_id) REFERENCES personnel(id)
            )
        """)
        
        # جدول الألوان
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS colors (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT UNIQUE NOT NULL,
                hex_code TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        """)

        # جدول المعلومات الأساسية للقوة العمومية
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS force_basic_info (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                force_name TEXT NOT NULL,
                force_number TEXT,
                unit_number TEXT NOT NULL,
                unit_name TEXT,
                commander_name TEXT,
                commander_rank TEXT,
                commander_position TEXT,
                supply_name TEXT,
                supply_rank TEXT,
                supply_position TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # جدول الأفراد
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS personnel (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                military_number TEXT UNIQUE NOT NULL,
                national_id TEXT UNIQUE NOT NULL,
                first_name TEXT NOT NULL,
                middle_name TEXT,
                last_name TEXT NOT NULL,
                full_name TEXT NOT NULL,
                rank_id INTEGER,
                job_title_id INTEGER,
                unit_id INTEGER,
                birth_date DATE,
                recruitment_date DATE,
                phone TEXT,
                address TEXT,
                emergency_contact TEXT,
                emergency_phone TEXT,
                blood_type TEXT,
                marital_status TEXT,
                education_level TEXT,
                specialization TEXT,
                notes TEXT,
                photo_path TEXT,
                is_active BOOLEAN DEFAULT 1,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (rank_id) REFERENCES ranks(id),
                FOREIGN KEY (job_title_id) REFERENCES job_titles(id),
                FOREIGN KEY (unit_id) REFERENCES units(id)
            )
        """)
        
        # جدول أنواع المعدات
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS equipment_types (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT UNIQUE NOT NULL,
                category TEXT,
                description TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # جدول المعدات
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS equipment (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                serial_number TEXT UNIQUE NOT NULL,
                name TEXT NOT NULL,
                type_id INTEGER,
                model TEXT,
                manufacturer TEXT,
                manufacture_year INTEGER,
                purchase_date DATE,
                purchase_price DECIMAL(10,2),
                current_value DECIMAL(10,2),
                condition_status TEXT DEFAULT 'جيد',
                location TEXT,
                assigned_to_personnel_id INTEGER,
                assigned_to_unit_id INTEGER,
                warranty_expiry DATE,
                maintenance_schedule TEXT,
                last_maintenance DATE,
                next_maintenance DATE,
                specifications TEXT,
                notes TEXT,
                photo_path TEXT,
                is_active BOOLEAN DEFAULT 1,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (type_id) REFERENCES equipment_types(id),
                FOREIGN KEY (assigned_to_personnel_id) REFERENCES personnel(id),
                FOREIGN KEY (assigned_to_unit_id) REFERENCES units(id)
            )
        """)
        
        # جدول أنواع العربات
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS vehicle_types (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT UNIQUE NOT NULL,
                category TEXT,
                description TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # جدول العربات
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS vehicles (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                plate_number TEXT UNIQUE NOT NULL,
                chassis_number TEXT UNIQUE,
                engine_number TEXT,
                name TEXT NOT NULL,
                type_id INTEGER,
                model TEXT,
                manufacturer TEXT,
                manufacture_year INTEGER,
                color_id INTEGER,
                fuel_type TEXT,
                engine_capacity TEXT,
                transmission_type TEXT,
                purchase_date DATE,
                purchase_price DECIMAL(10,2),
                current_value DECIMAL(10,2),
                condition_status TEXT DEFAULT 'جيد',
                mileage INTEGER DEFAULT 0,
                assigned_to_personnel_id INTEGER,
                assigned_to_unit_id INTEGER,
                insurance_company TEXT,
                insurance_policy TEXT,
                insurance_expiry DATE,
                license_expiry DATE,
                last_service DATE,
                next_service DATE,
                service_interval_km INTEGER DEFAULT 10000,
                notes TEXT,
                photo_path TEXT,
                is_active BOOLEAN DEFAULT 1,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (type_id) REFERENCES vehicle_types(id),
                FOREIGN KEY (color_id) REFERENCES colors(id),
                FOREIGN KEY (assigned_to_personnel_id) REFERENCES personnel(id),
                FOREIGN KEY (assigned_to_unit_id) REFERENCES units(id)
            )
        """)
        
        # جدول أنواع الأسلحة
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS weapon_types (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT UNIQUE NOT NULL,
                category TEXT,
                caliber TEXT,
                description TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # جدول الأسلحة
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS weapons (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                serial_number TEXT UNIQUE NOT NULL,
                name TEXT NOT NULL,
                type_id INTEGER,
                model TEXT,
                manufacturer TEXT,
                manufacture_year INTEGER,
                caliber TEXT,
                barrel_length TEXT,
                weight TEXT,
                purchase_date DATE,
                purchase_price DECIMAL(10,2),
                current_value DECIMAL(10,2),
                condition_status TEXT DEFAULT 'جيد',
                assigned_to_personnel_id INTEGER,
                assigned_to_unit_id INTEGER,
                license_number TEXT,
                license_expiry DATE,
                last_inspection DATE,
                next_inspection DATE,
                ammunition_type TEXT,
                magazine_capacity INTEGER,
                effective_range TEXT,
                notes TEXT,
                photo_path TEXT,
                is_active BOOLEAN DEFAULT 1,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (type_id) REFERENCES weapon_types(id),
                FOREIGN KEY (assigned_to_personnel_id) REFERENCES personnel(id),
                FOREIGN KEY (assigned_to_unit_id) REFERENCES units(id)
            )
        """)
        
        # جدول البيانات الأخرى
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS other_data (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                category TEXT NOT NULL,
                name TEXT NOT NULL,
                description TEXT,
                value TEXT,
                unit_id INTEGER,
                assigned_to_personnel_id INTEGER,
                location TEXT,
                condition_status TEXT DEFAULT 'جيد',
                purchase_date DATE,
                purchase_price DECIMAL(10,2),
                current_value DECIMAL(10,2),
                notes TEXT,
                photo_path TEXT,
                is_active BOOLEAN DEFAULT 1,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (unit_id) REFERENCES units(id),
                FOREIGN KEY (assigned_to_personnel_id) REFERENCES personnel(id)
            )
        """)
        
        # جدول سجل العمليات
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS audit_log (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER,
                action TEXT NOT NULL,
                table_name TEXT,
                record_id INTEGER,
                old_values TEXT,
                new_values TEXT,
                ip_address TEXT,
                user_agent TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id)
            )
        """)

        # جدول نظام التقارير
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS reports_system (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                report_number TEXT UNIQUE NOT NULL,
                report_name TEXT NOT NULL,
                report_type TEXT NOT NULL,
                report_category TEXT NOT NULL,
                date_from DATE,
                date_to DATE,
                period TEXT,
                data_source TEXT,
                report_format TEXT DEFAULT 'PDF',
                language TEXT DEFAULT 'العربية',
                include_charts BOOLEAN DEFAULT 0,
                include_statistics BOOLEAN DEFAULT 0,
                include_summary BOOLEAN DEFAULT 1,
                confidentiality_level TEXT DEFAULT 'عام',
                distribution_method TEXT DEFAULT 'طباعة',
                prepared_by TEXT,
                reviewed_by TEXT,
                approved_by TEXT,
                creation_date DATE DEFAULT CURRENT_DATE,
                status TEXT DEFAULT 'مسودة',
                priority TEXT DEFAULT 'عادي',
                file_path TEXT,
                file_size INTEGER,
                notes TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        """)

        # جدول نظام التقارير العامة
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS general_reports (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                general_report_type TEXT NOT NULL,
                report_level TEXT NOT NULL,
                report_scope TEXT NOT NULL,
                date_from DATE,
                date_to DATE,
                include_inventory BOOLEAN DEFAULT 0,
                include_equipment BOOLEAN DEFAULT 0,
                include_personnel BOOLEAN DEFAULT 0,
                include_vehicles BOOLEAN DEFAULT 0,
                include_weapons BOOLEAN DEFAULT 0,
                include_financial BOOLEAN DEFAULT 0,
                include_statistics BOOLEAN DEFAULT 0,
                include_charts BOOLEAN DEFAULT 0,
                include_recommendations BOOLEAN DEFAULT 0,
                format TEXT DEFAULT 'PDF',
                language TEXT DEFAULT 'العربية',
                confidentiality TEXT DEFAULT 'عام',
                distribution TEXT DEFAULT 'طباعة',
                prepared_by TEXT,
                reviewed_by TEXT,
                approved_by TEXT,
                priority TEXT DEFAULT 'عادي',
                notes TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        """)

        # جدول البحث الشامل
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS comprehensive_search (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                search_term TEXT NOT NULL,
                search_type TEXT NOT NULL,
                search_inventory BOOLEAN DEFAULT 0,
                search_equipment BOOLEAN DEFAULT 0,
                search_personnel BOOLEAN DEFAULT 0,
                search_vehicles BOOLEAN DEFAULT 0,
                search_weapons BOOLEAN DEFAULT 0,
                date_from DATE,
                date_to DATE,
                department TEXT,
                status TEXT,
                type TEXT,
                category TEXT,
                exact_match BOOLEAN DEFAULT 0,
                ignore_case BOOLEAN DEFAULT 1,
                search_notes BOOLEAN DEFAULT 0,
                sort_by TEXT DEFAULT 'الصلة',
                sort_order TEXT DEFAULT 'تنازلي',
                result_limit TEXT DEFAULT '100',
                responsible TEXT,
                location TEXT,
                results_count INTEGER DEFAULT 0,
                search_time REAL DEFAULT 0.0,
                search_date DATE DEFAULT CURRENT_DATE,
                user_id TEXT,
                saved_search_name TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        """)
    
    def _create_indexes(self, cursor: sqlite3.Cursor) -> None:
        """إنشاء الفهارس لتحسين الأداء"""
        indexes = [
            "CREATE INDEX IF NOT EXISTS idx_personnel_military_number ON personnel(military_number)",
            "CREATE INDEX IF NOT EXISTS idx_personnel_national_id ON personnel(national_id)",
            "CREATE INDEX IF NOT EXISTS idx_personnel_full_name ON personnel(full_name)",
            "CREATE INDEX IF NOT EXISTS idx_personnel_rank ON personnel(rank_id)",
            "CREATE INDEX IF NOT EXISTS idx_personnel_unit ON personnel(unit_id)",
            "CREATE INDEX IF NOT EXISTS idx_equipment_serial ON equipment(serial_number)",
            "CREATE INDEX IF NOT EXISTS idx_equipment_assigned_personnel ON equipment(assigned_to_personnel_id)",
            "CREATE INDEX IF NOT EXISTS idx_equipment_assigned_unit ON equipment(assigned_to_unit_id)",
            "CREATE INDEX IF NOT EXISTS idx_vehicles_plate ON vehicles(plate_number)",
            "CREATE INDEX IF NOT EXISTS idx_vehicles_assigned_personnel ON vehicles(assigned_to_personnel_id)",
            "CREATE INDEX IF NOT EXISTS idx_vehicles_assigned_unit ON vehicles(assigned_to_unit_id)",
            "CREATE INDEX IF NOT EXISTS idx_weapons_serial ON weapons(serial_number)",
            "CREATE INDEX IF NOT EXISTS idx_weapons_assigned_personnel ON weapons(assigned_to_personnel_id)",
            "CREATE INDEX IF NOT EXISTS idx_weapons_assigned_unit ON weapons(assigned_to_unit_id)",
            "CREATE INDEX IF NOT EXISTS idx_audit_log_user ON audit_log(user_id)",
            "CREATE INDEX IF NOT EXISTS idx_audit_log_created ON audit_log(created_at)",
            "CREATE INDEX IF NOT EXISTS idx_reports_system_number ON reports_system(report_number)",
            "CREATE INDEX IF NOT EXISTS idx_reports_system_type ON reports_system(report_type)",
            "CREATE INDEX IF NOT EXISTS idx_reports_system_status ON reports_system(status)",
            "CREATE INDEX IF NOT EXISTS idx_general_reports_type ON general_reports(general_report_type)",
            "CREATE INDEX IF NOT EXISTS idx_general_reports_level ON general_reports(report_level)",
            "CREATE INDEX IF NOT EXISTS idx_comprehensive_search_term ON comprehensive_search(search_term)",
            "CREATE INDEX IF NOT EXISTS idx_comprehensive_search_date ON comprehensive_search(search_date)",
        ]
        
        for index_sql in indexes:
            cursor.execute(index_sql)
    
    def _insert_default_data(self, cursor: sqlite3.Cursor) -> None:
        """إدراج البيانات الافتراضية"""
        
        # التحقق من وجود مستخدم افتراضي
        cursor.execute("SELECT COUNT(*) FROM users")
        if cursor.fetchone()[0] == 0:
            # إنشاء مستخدم افتراضي
            admin_password = self.hash_password("admin123")
            cursor.execute("""
                INSERT INTO users (username, password_hash, full_name, role)
                VALUES (?, ?, ?, ?)
            """, ("admin", admin_password, "مدير النظام", "admin"))
        
        # إدراج الرتب الافتراضية
        default_ranks = [
            ("فريق", "فريق", 1),
            ("لواء", "لواء", 2),
            ("عميد", "عميد", 3),
            ("عقيد", "عقيد", 4),
            ("مقدم", "مقدم", 5),
            ("رائد", "رائد", 6),
            ("نقيب", "نقيب", 7),
            ("ملازم أول", "ملازم أول", 8),
            ("ملازم", "ملازم", 9),
            ("رقيب أول", "رقيب أول", 10),
            ("رقيب", "رقيب", 11),
            ("عريف", "عريف", 12),
            ("جندي أول", "جندي أول", 13),
            ("جندي", "جندي", 14)
        ]
        
        for rank_name, abbreviation, order_num in default_ranks:
            cursor.execute("""
                INSERT OR IGNORE INTO ranks (name, abbreviation, order_number)
                VALUES (?, ?, ?)
            """, (rank_name, abbreviation, order_num))
        
        # إدراج الألوان الافتراضية
        default_colors = [
            ("أبيض", "#FFFFFF"),
            ("أسود", "#000000"),
            ("أحمر", "#FF0000"),
            ("أزرق", "#0000FF"),
            ("أخضر", "#008000"),
            ("أصفر", "#FFFF00"),
            ("رمادي", "#808080"),
            ("بني", "#A52A2A"),
            ("برتقالي", "#FFA500"),
            ("بنفسجي", "#800080")
        ]
        
        for color_name, hex_code in default_colors:
            cursor.execute("""
                INSERT OR IGNORE INTO colors (name, hex_code)
                VALUES (?, ?)
            """, (color_name, hex_code))
    
    @staticmethod
    def hash_password(password: str) -> str:
        """تشفير كلمة المرور"""
        return hashlib.sha256(password.encode()).hexdigest()
    
    def verify_password(self, password: str, hashed: str) -> bool:
        """التحقق من كلمة المرور"""
        return self.hash_password(password) == hashed
    
    def execute_query(self, query: str, params: tuple = ()) -> List[Dict]:
        """تنفيذ استعلام وإرجاع النتائج"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(query, params)

                # إذا كان الاستعلام SELECT
                if query.strip().upper().startswith('SELECT'):
                    rows = cursor.fetchall()
                    return [dict(row) for row in rows]
                else:
                    conn.commit()
                    return [{"affected_rows": cursor.rowcount, "lastrowid": cursor.lastrowid}]

        except Exception as e:
            raise Exception(f"خطأ في تنفيذ الاستعلام: {e}")

    def execute_many(self, query: str, params_list: List[tuple]) -> int:
        """تنفيذ استعلام متعدد"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.executemany(query, params_list)
                conn.commit()
                return cursor.rowcount
        except Exception as e:
            raise Exception(f"خطأ في تنفيذ الاستعلام المتعدد: {e}")

    def get_table_data(self, table_name: str, conditions: str = "", params: tuple = (),
                      limit: int = None, offset: int = None) -> List[Dict]:
        """الحصول على بيانات جدول"""
        try:
            query = f"SELECT * FROM {table_name}"

            if conditions:
                query += f" WHERE {conditions}"

            query += " ORDER BY id DESC"

            if limit:
                query += f" LIMIT {limit}"
                if offset:
                    query += f" OFFSET {offset}"

            return self.execute_query(query, params)
        except Exception as e:
            raise Exception(f"خطأ في الحصول على بيانات الجدول {table_name}: {e}")

    def insert_record(self, table_name: str, data: Dict[str, Any]) -> int:
        """إدراج سجل جديد"""
        try:
            columns = list(data.keys())
            placeholders = ['?' for _ in columns]
            values = list(data.values())

            query = f"""
                INSERT INTO {table_name} ({', '.join(columns)})
                VALUES ({', '.join(placeholders)})
            """

            result = self.execute_query(query, tuple(values))
            return result[0]['lastrowid']
        except Exception as e:
            raise Exception(f"خطأ في إدراج السجل في {table_name}: {e}")

    def update_record(self, table_name: str, record_id: int, data: Dict[str, Any]) -> bool:
        """تحديث سجل"""
        try:
            # التحقق من وجود عمود updated_at وإضافة تاريخ التحديث إذا كان موجوداً
            columns = self.get_table_columns(table_name)
            if 'updated_at' in columns:
                data['updated_at'] = datetime.now().isoformat()

            set_clause = ', '.join([f"{col} = ?" for col in data.keys()])
            values = list(data.values()) + [record_id]

            query = f"UPDATE {table_name} SET {set_clause} WHERE id = ?"

            result = self.execute_query(query, tuple(values))
            return result[0]['affected_rows'] > 0
        except Exception as e:
            raise Exception(f"خطأ في تحديث السجل في {table_name}: {e}")

    def delete_record(self, table_name: str, record_id: int) -> bool:
        """حذف سجل"""
        try:
            query = f"DELETE FROM {table_name} WHERE id = ?"
            result = self.execute_query(query, (record_id,))
            return result[0]['affected_rows'] > 0
        except Exception as e:
            raise Exception(f"خطأ في حذف السجل من {table_name}: {e}")

    def get_record_by_id(self, table_name: str, record_id: int) -> Optional[Dict]:
        """الحصول على سجل بالمعرف"""
        try:
            query = f"SELECT * FROM {table_name} WHERE id = ?"
            results = self.execute_query(query, (record_id,))
            return results[0] if results else None
        except Exception as e:
            raise Exception(f"خطأ في الحصول على السجل من {table_name}: {e}")

    def search_records(self, table_name: str, search_term: str,
                      search_columns: List[str]) -> List[Dict]:
        """البحث في السجلات"""
        try:
            conditions = []
            params = []

            for column in search_columns:
                conditions.append(f"{column} LIKE ?")
                params.append(f"%{search_term}%")

            where_clause = " OR ".join(conditions)
            query = f"SELECT * FROM {table_name} WHERE {where_clause} ORDER BY id DESC"

            return self.execute_query(query, tuple(params))
        except Exception as e:
            raise Exception(f"خطأ في البحث في {table_name}: {e}")

    def get_count(self, table_name: str, conditions: str = "", params: tuple = ()) -> int:
        """الحصول على عدد السجلات"""
        try:
            query = f"SELECT COUNT(*) as count FROM {table_name}"
            if conditions:
                query += f" WHERE {conditions}"

            result = self.execute_query(query, params)
            return result[0]['count']
        except Exception as e:
            raise Exception(f"خطأ في الحصول على العدد من {table_name}: {e}")

    def get_table_columns(self, table_name: str) -> List[str]:
        """الحصول على أعمدة الجدول"""
        try:
            query = f"PRAGMA table_info({table_name})"
            result = self.execute_query(query)
            return [row['name'] for row in result] if result else []
        except Exception as e:
            print(f"خطأ في الحصول على أعمدة الجدول {table_name}: {e}")
            return []

    def backup_database(self, backup_path: str) -> bool:
        """نسخ احتياطي لقاعدة البيانات"""
        try:
            import shutil

            # إنشاء مجلد النسخ الاحتياطية
            os.makedirs(os.path.dirname(backup_path), exist_ok=True)

            # نسخ الملف
            shutil.copy2(self.db_path, backup_path)
            return True
        except Exception as e:
            raise Exception(f"خطأ في إنشاء النسخة الاحتياطية: {e}")

    def restore_database(self, backup_path: str) -> bool:
        """استعادة قاعدة البيانات من نسخة احتياطية"""
        try:
            import shutil

            if not os.path.exists(backup_path):
                raise Exception("ملف النسخة الاحتياطية غير موجود")

            # إغلاق الاتصال الحالي
            self.close()

            # استعادة الملف
            shutil.copy2(backup_path, self.db_path)

            # إعادة تهيئة الاتصال
            self.init_database()
            return True
        except Exception as e:
            raise Exception(f"خطأ في استعادة النسخة الاحتياطية: {e}")

    def get_database_info(self) -> Dict[str, Any]:
        """الحصول على معلومات قاعدة البيانات"""
        try:
            info = {
                "path": self.db_path,
                "size_bytes": os.path.getsize(self.db_path) if os.path.exists(self.db_path) else 0,
                "tables": {},
                "total_records": 0
            }

            # الحصول على قائمة الجداول
            tables_query = "SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'"
            tables = self.execute_query(tables_query)

            for table in tables:
                table_name = table['name']
                count = self.get_count(table_name)
                info['tables'][table_name] = count
                info['total_records'] += count

            return info
        except Exception as e:
            raise Exception(f"خطأ في الحصول على معلومات قاعدة البيانات: {e}")

    def close(self) -> None:
        """إغلاق الاتصال بقاعدة البيانات"""
        if self.connection:
            self.connection.close()
            self.connection = None
