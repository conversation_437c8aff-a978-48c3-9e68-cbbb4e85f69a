#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
وحدة البحث الشامل الكاملة مطابقة للبرنامج الأصلي
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import ttkbootstrap as ttk_bs
from ttkbootstrap.constants import *
from datetime import datetime, date
import os

class ComprehensiveSearchCompleteModule:
    def __init__(self, parent_frame, db_manager):
        self.parent = parent_frame
        self.db_manager = db_manager
        self.current_record = None
        self.form_vars = {}
        self.search_results = []
        
        # إنشاء الواجهة
        self.create_interface()
        
        # تحميل البيانات
        self.load_data()
    
    def create_interface(self):
        """إنشاء واجهة البحث الشامل مطابقة للبرنامج الأصلي"""
        # مسح المحتوى السابق
        for widget in self.parent.winfo_children():
            widget.destroy()
        
        # الإطار الرئيسي
        main_frame = ttk_bs.Frame(self.parent)
        main_frame.pack(fill=BOTH, expand=True, padx=10, pady=10)
        
        # العنوان الرئيسي
        title_frame = ttk_bs.Frame(main_frame)
        title_frame.pack(fill=X, pady=(0, 10))
        
        title_label = ttk_bs.Label(
            title_frame,
            text="البحث الشامل",
            font=("Tahoma", 16, "bold"),
            bootstyle="primary"
        )
        title_label.pack()
        
        # إنشاء شريط الأزرار
        self.create_buttons_bar(main_frame)
        
        # إنشاء منطقة الإدخال
        self.create_input_area(main_frame)
        
        # إنشاء جدول البيانات
        self.create_data_table(main_frame)
    
    def create_buttons_bar(self, parent):
        """إنشاء شريط الأزرار"""
        buttons_frame = ttk_bs.Frame(parent)
        buttons_frame.pack(fill=X, pady=(0, 10))
        
        # الأزرار مطابقة للبرنامج الأصلي
        buttons = [
            ("بحث", "search", "success", "🔍"),
            ("بحث متقدم", "advanced_search", "primary", "🔎"),
            ("مسح", "clear", "secondary", "🧹"),
            ("حفظ البحث", "save_search", "info", "💾"),
            ("تحميل بحث", "load_search", "info", "📂"),
            ("تصدير النتائج", "export_results", "warning", "📤"),
            ("طباعة النتائج", "print_results", "info", "🖨️"),
            ("إحصائيات", "statistics", "success", "📊"),
            ("تفاصيل", "details", "primary", "📋"),
            ("تحديث", "refresh", "secondary", "🔄")
        ]
        
        for text, action, style, icon in buttons:
            btn = ttk_bs.Button(
                buttons_frame,
                text=f"{icon} {text}",
                command=lambda a=action: self.handle_button_action(a),
                bootstyle=style,
                width=12
            )
            btn.pack(side=LEFT, padx=2)
    
    def create_input_area(self, parent):
        """إنشاء منطقة الإدخال مطابقة للبرنامج الأصلي"""
        input_frame = ttk_bs.LabelFrame(parent, text="معايير البحث الشامل", padding=10)
        input_frame.pack(fill=X, pady=(0, 10))
        
        # الصف الأول من الحقول - البحث الأساسي
        row1_frame = ttk_bs.Frame(input_frame)
        row1_frame.pack(fill=X, pady=5)
        
        # كلمة البحث
        ttk_bs.Label(row1_frame, text="كلمة البحث:").pack(side=RIGHT, padx=5)
        self.form_vars['search_term'] = ttk_bs.StringVar()
        search_entry = ttk_bs.Entry(row1_frame, textvariable=self.form_vars['search_term'], width=30)
        search_entry.pack(side=RIGHT, padx=5)
        
        # نوع البحث
        ttk_bs.Label(row1_frame, text="نوع البحث:").pack(side=RIGHT, padx=5)
        self.form_vars['search_type'] = ttk_bs.StringVar()
        search_type_combo = ttk_bs.Combobox(row1_frame, textvariable=self.form_vars['search_type'], width=20)
        search_type_combo['values'] = ("بحث عام", "بحث دقيق", "بحث جزئي", "بحث متقدم", "بحث بالكلمات المفتاحية")
        search_type_combo.pack(side=RIGHT, padx=5)
        
        # الصف الثاني من الحقول - نطاق البحث
        row2_frame = ttk_bs.Frame(input_frame)
        row2_frame.pack(fill=X, pady=5)
        
        # البحث في المخزون
        self.form_vars['search_inventory'] = ttk_bs.BooleanVar()
        inventory_check = ttk_bs.Checkbutton(
            row2_frame, 
            text="المخزون", 
            variable=self.form_vars['search_inventory']
        )
        inventory_check.pack(side=RIGHT, padx=5)
        
        # البحث في المعدات
        self.form_vars['search_equipment'] = ttk_bs.BooleanVar()
        equipment_check = ttk_bs.Checkbutton(
            row2_frame, 
            text="المعدات", 
            variable=self.form_vars['search_equipment']
        )
        equipment_check.pack(side=RIGHT, padx=5)
        
        # البحث في الأفراد
        self.form_vars['search_personnel'] = ttk_bs.BooleanVar()
        personnel_check = ttk_bs.Checkbutton(
            row2_frame, 
            text="الأفراد", 
            variable=self.form_vars['search_personnel']
        )
        personnel_check.pack(side=RIGHT, padx=5)
        
        # البحث في العربات
        self.form_vars['search_vehicles'] = ttk_bs.BooleanVar()
        vehicles_check = ttk_bs.Checkbutton(
            row2_frame, 
            text="العربات", 
            variable=self.form_vars['search_vehicles']
        )
        vehicles_check.pack(side=RIGHT, padx=5)
        
        # البحث في الأسلحة
        self.form_vars['search_weapons'] = ttk_bs.BooleanVar()
        weapons_check = ttk_bs.Checkbutton(
            row2_frame, 
            text="الأسلحة", 
            variable=self.form_vars['search_weapons']
        )
        weapons_check.pack(side=RIGHT, padx=5)
        
        # الصف الثالث من الحقول - معايير إضافية
        row3_frame = ttk_bs.Frame(input_frame)
        row3_frame.pack(fill=X, pady=5)
        
        # من تاريخ
        ttk_bs.Label(row3_frame, text="من تاريخ:").pack(side=RIGHT, padx=5)
        self.form_vars['date_from'] = ttk_bs.StringVar()
        date_from_entry = ttk_bs.Entry(row3_frame, textvariable=self.form_vars['date_from'], width=12)
        date_from_entry.pack(side=RIGHT, padx=5)
        
        # إلى تاريخ
        ttk_bs.Label(row3_frame, text="إلى تاريخ:").pack(side=RIGHT, padx=5)
        self.form_vars['date_to'] = ttk_bs.StringVar()
        date_to_entry = ttk_bs.Entry(row3_frame, textvariable=self.form_vars['date_to'], width=12)
        date_to_entry.pack(side=RIGHT, padx=5)
        
        # القسم
        ttk_bs.Label(row3_frame, text="القسم:").pack(side=RIGHT, padx=5)
        self.form_vars['department'] = ttk_bs.StringVar()
        department_combo = ttk_bs.Combobox(row3_frame, textvariable=self.form_vars['department'], width=20)
        department_combo['values'] = ("جميع الأقسام", "الإدارة العامة", "قسم المخازن", "قسم المعدات", "قسم الأفراد")
        department_combo.pack(side=RIGHT, padx=5)
        
        # الصف الرابع من الحقول - معايير متقدمة
        row4_frame = ttk_bs.Frame(input_frame)
        row4_frame.pack(fill=X, pady=5)
        
        # الحالة
        ttk_bs.Label(row4_frame, text="الحالة:").pack(side=RIGHT, padx=5)
        self.form_vars['status'] = ttk_bs.StringVar()
        status_combo = ttk_bs.Combobox(row4_frame, textvariable=self.form_vars['status'], width=15)
        status_combo['values'] = ("جميع الحالات", "نشط", "غير نشط", "صيانة", "خارج الخدمة")
        status_combo.pack(side=RIGHT, padx=5)
        
        # النوع
        ttk_bs.Label(row4_frame, text="النوع:").pack(side=RIGHT, padx=5)
        self.form_vars['type'] = ttk_bs.StringVar()
        type_combo = ttk_bs.Combobox(row4_frame, textvariable=self.form_vars['type'], width=20)
        type_combo.pack(side=RIGHT, padx=5)
        
        # الفئة
        ttk_bs.Label(row4_frame, text="الفئة:").pack(side=RIGHT, padx=5)
        self.form_vars['category'] = ttk_bs.StringVar()
        category_combo = ttk_bs.Combobox(row4_frame, textvariable=self.form_vars['category'], width=20)
        category_combo.pack(side=RIGHT, padx=5)
        
        # الصف الخامس من الحقول - خيارات البحث
        row5_frame = ttk_bs.Frame(input_frame)
        row5_frame.pack(fill=X, pady=5)
        
        # البحث الدقيق
        self.form_vars['exact_match'] = ttk_bs.BooleanVar()
        exact_check = ttk_bs.Checkbutton(
            row5_frame, 
            text="بحث دقيق", 
            variable=self.form_vars['exact_match']
        )
        exact_check.pack(side=RIGHT, padx=5)
        
        # تجاهل حالة الأحرف
        self.form_vars['ignore_case'] = ttk_bs.BooleanVar()
        case_check = ttk_bs.Checkbutton(
            row5_frame, 
            text="تجاهل حالة الأحرف", 
            variable=self.form_vars['ignore_case']
        )
        case_check.pack(side=RIGHT, padx=5)
        
        # البحث في الملاحظات
        self.form_vars['search_notes'] = ttk_bs.BooleanVar()
        notes_check = ttk_bs.Checkbutton(
            row5_frame, 
            text="البحث في الملاحظات", 
            variable=self.form_vars['search_notes']
        )
        notes_check.pack(side=RIGHT, padx=5)
        
        # الصف السادس من الحقول - ترتيب النتائج
        row6_frame = ttk_bs.Frame(input_frame)
        row6_frame.pack(fill=X, pady=5)
        
        # ترتيب النتائج
        ttk_bs.Label(row6_frame, text="ترتيب النتائج:").pack(side=RIGHT, padx=5)
        self.form_vars['sort_by'] = ttk_bs.StringVar()
        sort_combo = ttk_bs.Combobox(row6_frame, textvariable=self.form_vars['sort_by'], width=20)
        sort_combo['values'] = ("الصلة", "التاريخ", "الاسم", "الرقم", "النوع", "القسم")
        sort_combo.pack(side=RIGHT, padx=5)
        
        # اتجاه الترتيب
        ttk_bs.Label(row6_frame, text="اتجاه الترتيب:").pack(side=RIGHT, padx=5)
        self.form_vars['sort_order'] = ttk_bs.StringVar()
        order_combo = ttk_bs.Combobox(row6_frame, textvariable=self.form_vars['sort_order'], width=15)
        order_combo['values'] = ("تصاعدي", "تنازلي")
        order_combo.pack(side=RIGHT, padx=5)
        
        # عدد النتائج
        ttk_bs.Label(row6_frame, text="عدد النتائج:").pack(side=RIGHT, padx=5)
        self.form_vars['result_limit'] = ttk_bs.StringVar()
        limit_combo = ttk_bs.Combobox(row6_frame, textvariable=self.form_vars['result_limit'], width=10)
        limit_combo['values'] = ("50", "100", "200", "500", "1000", "الكل")
        limit_combo.pack(side=RIGHT, padx=5)
        
        # الصف السابع من الحقول - فلاتر إضافية
        row7_frame = ttk_bs.Frame(input_frame)
        row7_frame.pack(fill=X, pady=5)
        
        # المسؤول
        ttk_bs.Label(row7_frame, text="المسؤول:").pack(side=RIGHT, padx=5)
        self.form_vars['responsible'] = ttk_bs.StringVar()
        responsible_combo = ttk_bs.Combobox(row7_frame, textvariable=self.form_vars['responsible'], width=25)
        responsible_combo.pack(side=RIGHT, padx=5)
        
        # الموقع
        ttk_bs.Label(row7_frame, text="الموقع:").pack(side=RIGHT, padx=5)
        self.form_vars['location'] = ttk_bs.StringVar()
        location_combo = ttk_bs.Combobox(row7_frame, textvariable=self.form_vars['location'], width=25)
        location_combo.pack(side=RIGHT, padx=5)
        
        # الصف الثامن - معلومات البحث
        row8_frame = ttk_bs.Frame(input_frame)
        row8_frame.pack(fill=X, pady=5)
        
        # عدد النتائج الموجودة
        ttk_bs.Label(row8_frame, text="عدد النتائج الموجودة:").pack(side=RIGHT, padx=5)
        self.results_count_label = ttk_bs.Label(row8_frame, text="0", bootstyle="info")
        self.results_count_label.pack(side=RIGHT, padx=5)
        
        # وقت البحث
        ttk_bs.Label(row8_frame, text="وقت البحث:").pack(side=RIGHT, padx=5)
        self.search_time_label = ttk_bs.Label(row8_frame, text="0.00 ثانية", bootstyle="info")
        self.search_time_label.pack(side=RIGHT, padx=5)
        
        # تحميل البيانات في القوائم المنسدلة
        self.load_combo_data(type_combo, category_combo, responsible_combo, location_combo)
    
    def create_data_table(self, parent):
        """إنشاء جدول البيانات"""
        table_frame = ttk_bs.LabelFrame(parent, text="نتائج البحث", padding=10)
        table_frame.pack(fill=BOTH, expand=True)
        
        # إنشاء Treeview
        columns = ("الرقم", "النوع", "الاسم/الوصف", "الرقم التعريفي", "القسم", "الحالة", "التاريخ", "المسؤول")
        self.tree = ttk_bs.Treeview(table_frame, columns=columns, show="headings", height=10)
        
        # تعيين عناوين الأعمدة
        for col in columns:
            self.tree.heading(col, text=col)
            self.tree.column(col, width=120, anchor=CENTER)
        
        # شريط التمرير
        scrollbar = ttk_bs.Scrollbar(table_frame, orient=VERTICAL, command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)
        
        # تخطيط الجدول
        self.tree.pack(side=LEFT, fill=BOTH, expand=True)
        scrollbar.pack(side=RIGHT, fill=Y)
        
        # ربط حدث النقر
        self.tree.bind("<<TreeviewSelect>>", self.on_item_select)
        self.tree.bind("<Double-1>", self.on_double_click)
    
    def load_combo_data(self, type_combo, category_combo, responsible_combo, location_combo):
        """تحميل بيانات القوائم المنسدلة"""
        try:
            # تحميل الأنواع
            types = ["جميع الأنواع", "مخزون", "معدات", "أفراد", "عربات", "أسلحة"]
            type_combo['values'] = types
            
            # تحميل الفئات
            categories = ["جميع الفئات", "إداري", "فني", "أمني", "طبي", "تقني"]
            category_combo['values'] = categories
            
            # تحميل المسؤولين
            responsible = ["جميع المسؤولين", "أحمد محمد السالم", "سالم علي الأحمد", "خالد حسن المحمد"]
            responsible_combo['values'] = responsible
            
            # تحميل المواقع
            locations = ["جميع المواقع", "المبنى الرئيسي", "المستودع الأول", "المستودع الثاني", "الورشة"]
            location_combo['values'] = locations
            
        except Exception as e:
            print(f"خطأ في تحميل بيانات القوائم: {e}")
    
    def handle_button_action(self, action):
        """معالجة أحداث الأزرار"""
        if action == "search":
            self.perform_search()
        elif action == "advanced_search":
            self.advanced_search()
        elif action == "clear":
            self.clear_search()
        elif action == "save_search":
            self.save_search()
        elif action == "load_search":
            self.load_search()
        elif action == "export_results":
            self.export_results()
        elif action == "print_results":
            self.print_results()
        elif action == "statistics":
            self.show_statistics()
        elif action == "details":
            self.show_details()
        elif action == "refresh":
            self.refresh_search()
    
    def load_data(self):
        """تحميل البيانات الأولية"""
        try:
            # تعيين قيم افتراضية
            self.form_vars['search_type'].set("بحث عام")
            self.form_vars['sort_by'].set("الصلة")
            self.form_vars['sort_order'].set("تنازلي")
            self.form_vars['result_limit'].set("100")
            self.form_vars['ignore_case'].set(True)
            
        except Exception as e:
            print(f"خطأ في تحميل البيانات: {e}")
    
    def perform_search(self):
        """تنفيذ البحث"""
        try:
            start_time = datetime.now()
            
            # مسح النتائج السابقة
            for item in self.tree.get_children():
                self.tree.delete(item)
            
            search_term = self.form_vars['search_term'].get().strip()
            
            if not search_term:
                messagebox.showwarning("تحذير", "يرجى إدخال كلمة البحث")
                return
            
            # محاكاة نتائج البحث
            sample_results = [
                ("1", "مخزون", "أجهزة كمبيوتر", "INV001", "قسم تقنية المعلومات", "نشط", "2024-01-15", "أحمد محمد"),
                ("2", "معدات", "طابعة ليزر", "EQP002", "الإدارة العامة", "نشط", "2024-01-20", "سالم علي"),
                ("3", "أفراد", "محمد أحمد السالم", "PER003", "قسم الأمن", "نشط", "2024-01-10", "خالد حسن"),
                ("4", "عربات", "سيارة تويوتا", "VEH004", "قسم النقل", "نشط", "2024-01-25", "محمد عبدالله"),
                ("5", "أسلحة", "مسدس جلوك", "WPN005", "قسم الأمن", "نشط", "2024-01-30", "أحمد محمد")
            ]
            
            # فلترة النتائج حسب المعايير المحددة
            filtered_results = []
            for result in sample_results:
                if search_term.lower() in result[2].lower():  # البحث في الاسم/الوصف
                    # فحص نطاق البحث
                    if self.should_include_result(result[1]):
                        filtered_results.append(result)
            
            # إضافة النتائج إلى الجدول
            for result in filtered_results:
                self.tree.insert("", "end", values=result)
            
            # حساب وقت البحث
            end_time = datetime.now()
            search_duration = (end_time - start_time).total_seconds()
            
            # تحديث معلومات البحث
            self.results_count_label.config(text=str(len(filtered_results)))
            self.search_time_label.config(text=f"{search_duration:.2f} ثانية")
            
            messagebox.showinfo("البحث", f"تم العثور على {len(filtered_results)} نتيجة")
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تنفيذ البحث: {e}")
    
    def should_include_result(self, result_type):
        """فحص ما إذا كان يجب تضمين النتيجة حسب نطاق البحث"""
        if result_type == "مخزون" and self.form_vars['search_inventory'].get():
            return True
        elif result_type == "معدات" and self.form_vars['search_equipment'].get():
            return True
        elif result_type == "أفراد" and self.form_vars['search_personnel'].get():
            return True
        elif result_type == "عربات" and self.form_vars['search_vehicles'].get():
            return True
        elif result_type == "أسلحة" and self.form_vars['search_weapons'].get():
            return True
        elif not any([
            self.form_vars['search_inventory'].get(),
            self.form_vars['search_equipment'].get(),
            self.form_vars['search_personnel'].get(),
            self.form_vars['search_vehicles'].get(),
            self.form_vars['search_weapons'].get()
        ]):
            return True  # إذا لم يتم تحديد أي نطاق، ابحث في الكل
        return False
    
    def on_item_select(self, event):
        """معالجة تحديد عنصر في الجدول"""
        selection = self.tree.selection()
        if selection:
            item = self.tree.item(selection[0])
            values = item['values']
            # يمكن إضافة منطق إضافي هنا
    
    def on_double_click(self, event):
        """معالجة النقر المزدوج لعرض التفاصيل"""
        self.show_details()
    
    def advanced_search(self):
        """البحث المتقدم"""
        messagebox.showinfo("البحث المتقدم", "سيتم تطوير واجهة البحث المتقدم قريباً")
    
    def clear_search(self):
        """مسح معايير البحث"""
        for var in self.form_vars.values():
            if hasattr(var, 'set'):
                var.set("")
        
        # مسح النتائج
        for item in self.tree.get_children():
            self.tree.delete(item)
        
        # إعادة تعيين القيم الافتراضية
        self.load_data()
        self.results_count_label.config(text="0")
        self.search_time_label.config(text="0.00 ثانية")
        
        messagebox.showinfo("مسح", "تم مسح معايير البحث والنتائج")
    
    def save_search(self):
        """حفظ معايير البحث"""
        messagebox.showinfo("حفظ البحث", "سيتم تطوير وظيفة حفظ البحث قريباً")
    
    def load_search(self):
        """تحميل معايير بحث محفوظة"""
        messagebox.showinfo("تحميل البحث", "سيتم تطوير وظيفة تحميل البحث قريباً")
    
    def export_results(self):
        """تصدير النتائج"""
        messagebox.showinfo("تصدير النتائج", "سيتم تطوير وظيفة تصدير النتائج قريباً")
    
    def print_results(self):
        """طباعة النتائج"""
        messagebox.showinfo("طباعة النتائج", "سيتم تطوير وظيفة طباعة النتائج قريباً")
    
    def show_statistics(self):
        """عرض إحصائيات البحث"""
        messagebox.showinfo("إحصائيات", "سيتم تطوير وظيفة الإحصائيات قريباً")
    
    def show_details(self):
        """عرض تفاصيل العنصر المحدد"""
        selection = self.tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى تحديد عنصر لعرض تفاصيله")
            return
        
        messagebox.showinfo("التفاصيل", "سيتم تطوير وظيفة عرض التفاصيل قريباً")
    
    def refresh_search(self):
        """تحديث البحث"""
        if self.form_vars['search_term'].get().strip():
            self.perform_search()
        else:
            messagebox.showinfo("تحديث", "يرجى إدخال كلمة البحث أولاً")
