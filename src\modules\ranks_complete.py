#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
وحدة إدارة الرتب الكاملة مطابقة للبرنامج الأصلي
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import ttkbootstrap as ttk_bs
from ttkbootstrap.constants import *
from datetime import datetime

class RanksCompleteModule:
    def __init__(self, parent_frame, db_manager):
        self.parent = parent_frame
        self.db_manager = db_manager
        self.current_record = None
        self.form_vars = {}
        
        # إنشاء الواجهة
        self.create_interface()
        
        # تحميل البيانات
        self.load_data()
    
    def create_interface(self):
        """إنشاء واجهة إدارة الرتب مطابقة للبرنامج الأصلي"""
        # مسح المحتوى السابق
        for widget in self.parent.winfo_children():
            widget.destroy()
        
        # الإطار الرئيسي
        main_frame = ttk_bs.Frame(self.parent)
        main_frame.pack(fill=BOTH, expand=True, padx=10, pady=10)
        
        # العنوان الرئيسي
        title_frame = ttk_bs.Frame(main_frame)
        title_frame.pack(fill=X, pady=(0, 10))
        
        title_label = ttk_bs.Label(
            title_frame,
            text="إدارة الرتب",
            font=("Tahoma", 16, "bold"),
            bootstyle="primary"
        )
        title_label.pack()
        
        # إنشاء شريط الأزرار
        self.create_buttons_bar(main_frame)
        
        # إنشاء منطقة الإدخال
        self.create_input_area(main_frame)
        
        # إنشاء جدول البيانات
        self.create_data_table(main_frame)
    
    def create_buttons_bar(self, parent):
        """إنشاء شريط الأزرار"""
        buttons_frame = ttk_bs.Frame(parent)
        buttons_frame.pack(fill=X, pady=(0, 10))
        
        # الأزرار مطابقة للبرنامج الأصلي
        buttons = [
            ("إضافة", "add", "success", "🆕"),
            ("حفظ", "save", "primary", "💾"),
            ("تعديل", "edit", "warning", "✏️"),
            ("حذف", "delete", "danger", "🗑️"),
            ("إلغاء", "cancel", "secondary", "❌"),
            ("طباعة", "print", "info", "🖨️"),
            ("تصدير", "export", "info", "📤")
        ]
        
        for text, action, style, icon in buttons:
            btn = ttk_bs.Button(
                buttons_frame,
                text=f"{icon} {text}",
                command=lambda a=action: self.handle_button_action(a),
                bootstyle=style,
                width=12
            )
            btn.pack(side=LEFT, padx=2)
    
    def create_input_area(self, parent):
        """إنشاء منطقة الإدخال مطابقة للبرنامج الأصلي"""
        input_frame = ttk_bs.LabelFrame(parent, text="بيانات الرتبة", padding=10)
        input_frame.pack(fill=X, pady=(0, 10))
        
        # الصف الأول من الحقول
        row1_frame = ttk_bs.Frame(input_frame)
        row1_frame.pack(fill=X, pady=5)
        
        # رقم الرتبة
        ttk_bs.Label(row1_frame, text="* رقم الرتبة:").pack(side=RIGHT, padx=5)
        self.form_vars['rank_number'] = ttk_bs.StringVar()
        rank_number_entry = ttk_bs.Entry(row1_frame, textvariable=self.form_vars['rank_number'], width=10)
        rank_number_entry.pack(side=RIGHT, padx=5)
        
        # اسم الرتبة
        ttk_bs.Label(row1_frame, text="* اسم الرتبة:").pack(side=RIGHT, padx=5)
        self.form_vars['rank_name'] = ttk_bs.StringVar()
        rank_name_entry = ttk_bs.Entry(row1_frame, textvariable=self.form_vars['rank_name'], width=25)
        rank_name_entry.pack(side=RIGHT, padx=5)
        
        # الصف الثاني من الحقول
        row2_frame = ttk_bs.Frame(input_frame)
        row2_frame.pack(fill=X, pady=5)
        
        # نوع الرتبة
        ttk_bs.Label(row2_frame, text="* نوع الرتبة:").pack(side=RIGHT, padx=5)
        self.form_vars['rank_type'] = ttk_bs.StringVar()
        rank_type_combo = ttk_bs.Combobox(row2_frame, textvariable=self.form_vars['rank_type'], width=20)
        rank_type_combo['values'] = ("ضباط", "ضباط صف", "أفراد", "مدنيين")
        rank_type_combo.pack(side=RIGHT, padx=5)
        
        # مستوى الرتبة
        ttk_bs.Label(row2_frame, text="* مستوى الرتبة:").pack(side=RIGHT, padx=5)
        self.form_vars['rank_level'] = ttk_bs.StringVar()
        rank_level_entry = ttk_bs.Entry(row2_frame, textvariable=self.form_vars['rank_level'], width=10)
        rank_level_entry.pack(side=RIGHT, padx=5)
        
        # الصف الثالث من الحقول
        row3_frame = ttk_bs.Frame(input_frame)
        row3_frame.pack(fill=X, pady=5)
        
        # الاختصار
        ttk_bs.Label(row3_frame, text="الاختصار:").pack(side=RIGHT, padx=5)
        self.form_vars['abbreviation'] = ttk_bs.StringVar()
        abbreviation_entry = ttk_bs.Entry(row3_frame, textvariable=self.form_vars['abbreviation'], width=10)
        abbreviation_entry.pack(side=RIGHT, padx=5)
        
        # الاختصار الإنجليزي
        ttk_bs.Label(row3_frame, text="الاختصار الإنجليزي:").pack(side=RIGHT, padx=5)
        self.form_vars['english_abbreviation'] = ttk_bs.StringVar()
        eng_abbr_entry = ttk_bs.Entry(row3_frame, textvariable=self.form_vars['english_abbreviation'], width=10)
        eng_abbr_entry.pack(side=RIGHT, padx=5)
        
        # الصف الرابع من الحقول
        row4_frame = ttk_bs.Frame(input_frame)
        row4_frame.pack(fill=X, pady=5)
        
        # الراتب الأساسي
        ttk_bs.Label(row4_frame, text="الراتب الأساسي:").pack(side=RIGHT, padx=5)
        self.form_vars['basic_salary'] = ttk_bs.StringVar()
        salary_entry = ttk_bs.Entry(row4_frame, textvariable=self.form_vars['basic_salary'], width=15)
        salary_entry.pack(side=RIGHT, padx=5)
        
        # بدل الرتبة
        ttk_bs.Label(row4_frame, text="بدل الرتبة:").pack(side=RIGHT, padx=5)
        self.form_vars['rank_allowance'] = ttk_bs.StringVar()
        allowance_entry = ttk_bs.Entry(row4_frame, textvariable=self.form_vars['rank_allowance'], width=15)
        allowance_entry.pack(side=RIGHT, padx=5)
        
        # الصف الخامس من الحقول
        row5_frame = ttk_bs.Frame(input_frame)
        row5_frame.pack(fill=X, pady=5)
        
        # سنوات الخدمة المطلوبة
        ttk_bs.Label(row5_frame, text="سنوات الخدمة المطلوبة:").pack(side=RIGHT, padx=5)
        self.form_vars['required_service_years'] = ttk_bs.StringVar()
        service_years_entry = ttk_bs.Entry(row5_frame, textvariable=self.form_vars['required_service_years'], width=10)
        service_years_entry.pack(side=RIGHT, padx=5)
        
        # الحد الأقصى للعمر
        ttk_bs.Label(row5_frame, text="الحد الأقصى للعمر:").pack(side=RIGHT, padx=5)
        self.form_vars['max_age'] = ttk_bs.StringVar()
        max_age_entry = ttk_bs.Entry(row5_frame, textvariable=self.form_vars['max_age'], width=10)
        max_age_entry.pack(side=RIGHT, padx=5)
        
        # الصف السادس من الحقول
        row6_frame = ttk_bs.Frame(input_frame)
        row6_frame.pack(fill=X, pady=5)
        
        # المؤهل المطلوب
        ttk_bs.Label(row6_frame, text="المؤهل المطلوب:").pack(side=RIGHT, padx=5)
        self.form_vars['required_qualification'] = ttk_bs.StringVar()
        qualification_combo = ttk_bs.Combobox(row6_frame, textvariable=self.form_vars['required_qualification'], width=20)
        qualification_combo['values'] = ("ابتدائي", "متوسط", "ثانوي", "دبلوم", "بكالوريوس", "ماجستير", "دكتوراه")
        qualification_combo.pack(side=RIGHT, padx=5)
        
        # الحالة
        ttk_bs.Label(row6_frame, text="الحالة:").pack(side=RIGHT, padx=5)
        self.form_vars['status'] = ttk_bs.StringVar()
        status_combo = ttk_bs.Combobox(row6_frame, textvariable=self.form_vars['status'], width=12)
        status_combo['values'] = ("نشطة", "غير نشطة", "ملغاة")
        status_combo.pack(side=RIGHT, padx=5)
        
        # الصف السابع - الوصف
        row7_frame = ttk_bs.Frame(input_frame)
        row7_frame.pack(fill=X, pady=5)
        
        ttk_bs.Label(row7_frame, text="الوصف:").pack(side=RIGHT, padx=5)
        self.form_vars['description'] = ttk_bs.StringVar()
        description_entry = ttk_bs.Entry(row7_frame, textvariable=self.form_vars['description'], width=50)
        description_entry.pack(side=RIGHT, padx=5, fill=X, expand=True)
        
        # الصف الثامن - الملاحظات
        row8_frame = ttk_bs.Frame(input_frame)
        row8_frame.pack(fill=X, pady=5)
        
        ttk_bs.Label(row8_frame, text="الملاحظات:").pack(side=RIGHT, padx=5)
        self.form_vars['notes'] = ttk_bs.StringVar()
        notes_entry = ttk_bs.Entry(row8_frame, textvariable=self.form_vars['notes'], width=50)
        notes_entry.pack(side=RIGHT, padx=5, fill=X, expand=True)
    
    def create_data_table(self, parent):
        """إنشاء جدول البيانات"""
        table_frame = ttk_bs.LabelFrame(parent, text="قائمة الرتب", padding=10)
        table_frame.pack(fill=BOTH, expand=True)

        # إنشاء Treeview
        columns = ("الرقم", "رقم الرتبة", "اسم الرتبة", "نوع الرتبة", "مستوى الرتبة", "الاختصار", "الراتب الأساسي", "الحالة")
        self.tree = ttk_bs.Treeview(table_frame, columns=columns, show="headings", height=10)

        # تعيين عناوين الأعمدة
        for col in columns:
            self.tree.heading(col, text=col)
            self.tree.column(col, width=120, anchor=CENTER)

        # شريط التمرير
        scrollbar = ttk_bs.Scrollbar(table_frame, orient=VERTICAL, command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)

        # تخطيط الجدول
        self.tree.pack(side=LEFT, fill=BOTH, expand=True)
        scrollbar.pack(side=RIGHT, fill=Y)

        # ربط حدث النقر
        self.tree.bind("<<TreeviewSelect>>", self.on_item_select)
    
    def handle_button_action(self, action):
        """معالجة أحداث الأزرار"""
        if action == "add":
            self.add_record()
        elif action == "save":
            self.save_record()
        elif action == "edit":
            self.edit_record()
        elif action == "delete":
            self.delete_record()
        elif action == "cancel":
            self.cancel_operation()
        elif action == "print":
            self.print_record()
        elif action == "export":
            self.export_data()
    
    def load_data(self):
        """تحميل البيانات في الجدول"""
        try:
            # مسح البيانات الحالية
            for item in self.tree.get_children():
                self.tree.delete(item)
            
            # تحميل البيانات من قاعدة البيانات
            query = """
                SELECT *
                FROM ranks
                WHERE is_active = 1
                ORDER BY rank_level, id
            """
            records = self.db_manager.execute_query(query)
            
            for i, record in enumerate(records, 1):
                values = (
                    i,
                    record.get('rank_number', ''),
                    record.get('rank_name', ''),
                    record.get('rank_type', ''),
                    record.get('rank_level', ''),
                    record.get('abbreviation', ''),
                    record.get('basic_salary', ''),
                    record.get('status', '')
                )
                self.tree.insert("", "end", values=values)
                
        except Exception as e:
            print(f"خطأ في تحميل البيانات: {e}")
    
    def on_item_select(self, event):
        """معالجة تحديد عنصر في الجدول"""
        selection = self.tree.selection()
        if selection:
            item = self.tree.item(selection[0])
            values = item['values']
            
            # تحديث الحقول بالبيانات المحددة
            if len(values) >= 8:
                self.form_vars['rank_number'].set(values[1])
                self.form_vars['rank_name'].set(values[2])
                self.form_vars['rank_type'].set(values[3])
                self.form_vars['rank_level'].set(values[4])
                self.form_vars['abbreviation'].set(values[5])
                self.form_vars['basic_salary'].set(values[6])
                self.form_vars['status'].set(values[7])
    
    def add_record(self):
        """إضافة سجل جديد"""
        self.clear_form()
        messagebox.showinfo("إضافة", "تم تفعيل وضع الإضافة")
    
    def save_record(self):
        """حفظ السجل"""
        try:
            # جمع البيانات من النموذج
            data = {field: var.get().strip() for field, var in self.form_vars.items()}
            
            # التحقق من البيانات المطلوبة
            required_fields = ['rank_number', 'rank_name', 'rank_type', 'rank_level']
            missing_fields = [field for field in required_fields if not data[field]]
            
            if missing_fields:
                messagebox.showerror("خطأ", "يرجى ملء جميع الحقول المطلوبة")
                return
            
            # إنشاء استعلام الإدراج
            query = """
                INSERT INTO ranks 
                (rank_number, rank_name, rank_type, rank_level, abbreviation, english_abbreviation,
                 basic_salary, rank_allowance, required_service_years, max_age, required_qualification,
                 status, description, notes, is_active)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 1)
            """
            
            params = tuple(data[field] for field in [
                'rank_number', 'rank_name', 'rank_type', 'rank_level', 'abbreviation',
                'english_abbreviation', 'basic_salary', 'rank_allowance', 'required_service_years',
                'max_age', 'required_qualification', 'status', 'description', 'notes'
            ])
            
            # تنفيذ الاستعلام
            self.db_manager.execute_query(query, params)
            messagebox.showinfo("نجح", "تم حفظ بيانات الرتبة بنجاح")
            
            # إعادة تحميل البيانات
            self.load_data()
            self.clear_form()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في حفظ البيانات: {e}")
    
    def edit_record(self):
        """تعديل السجل"""
        selection = self.tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى تحديد رتبة للتعديل")
            return
        
        messagebox.showinfo("تعديل", "تم تفعيل وضع التعديل")
    
    def delete_record(self):
        """حذف السجل"""
        selection = self.tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى تحديد رتبة للحذف")
            return
        
        if messagebox.askyesno("تأكيد", "هل أنت متأكد من حذف هذه الرتبة؟"):
            self.tree.delete(selection[0])
            messagebox.showinfo("حذف", "تم حذف الرتبة بنجاح")
    
    def cancel_operation(self):
        """إلغاء العملية"""
        self.clear_form()
        messagebox.showinfo("إلغاء", "تم إلغاء العملية")
    
    def print_record(self):
        """طباعة السجل"""
        messagebox.showinfo("طباعة", "سيتم تطوير وظيفة الطباعة قريباً")
    
    def export_data(self):
        """تصدير البيانات"""
        messagebox.showinfo("تصدير", "سيتم تطوير وظيفة التصدير قريباً")
    
    def clear_form(self):
        """مسح النموذج"""
        for var in self.form_vars.values():
            var.set("")
