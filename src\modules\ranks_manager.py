#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
مدير الرتب
"""

from .base_data_manager import BaseDataManager


class RanksManager(BaseDataManager):
    """فئة إدارة الرتب"""
    
    def __init__(self, parent_frame, db_manager, logger):
        """
        تهيئة مدير الرتب
        
        Args:
            parent_frame: الإطار الأب
            db_manager: مدير قاعدة البيانات
            logger: نظام السجلات
        """
        
        # تعريف أعمدة جدول الرتب
        columns = [
            {
                'id': 'id',
                'text': 'المعرف',
                'width': 80,
                'anchor': 'center',
                'editable': False,
                'searchable': False
            },
            {
                'id': 'name',
                'text': 'اسم الرتبة',
                'width': 200,
                'anchor': 'w',
                'editable': True,
                'searchable': True,
                'required': True,
                'type': 'text'
            },
            {
                'id': 'abbreviation',
                'text': 'الاختصار',
                'width': 100,
                'anchor': 'center',
                'editable': True,
                'searchable': True,
                'required': False,
                'type': 'text'
            },
            {
                'id': 'order_number',
                'text': 'رقم الترتيب',
                'width': 120,
                'anchor': 'center',
                'editable': True,
                'searchable': False,
                'required': False,
                'type': 'text'
            },
            {
                'id': 'created_at',
                'text': 'تاريخ الإنشاء',
                'width': 150,
                'anchor': 'center',
                'editable': False,
                'searchable': False,
                'type': 'date'
            }
        ]
        
        # استدعاء الفئة الأساسية
        super().__init__(
            parent_frame=parent_frame,
            db_manager=db_manager,
            logger=logger,
            table_name="ranks",
            title="إدارة الرتب",
            columns=columns
        )


def create_ranks_manager(parent_frame, db_manager, logger):
    """إنشاء مدير الرتب"""
    return RanksManager(parent_frame, db_manager, logger)
