#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار الواجهة الجديدة بدون القوائم الجانبية
"""

import sys
import os

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_new_interface():
    """اختبار الواجهة الجديدة"""
    print("=" * 60)
    print("اختبار الواجهة الجديدة بدون القوائم الجانبية")
    print("=" * 60)
    
    try:
        # استيراد المكونات
        from src.ui.main_window import MainWindow
        from src.database.database_manager import DatabaseManager
        
        print("✓ تم استيراد جميع المكونات بنجاح")
        
        # تهيئة قاعدة البيانات
        db_manager = DatabaseManager()
        print("✓ تم تهيئة قاعدة البيانات")
        
        # بيانات مستخدم تجريبية
        user_data = {
            'id': 1,
            'username': 'admin',
            'full_name': 'مدير النظام',
            'role': 'مدير'
        }
        
        print("✓ تم إعداد بيانات المستخدم التجريبية")
        
        # إنشاء النافذة الرئيسية
        print("\n🚀 تشغيل النافذة الرئيسية مع التصميم الجديد...")
        print("📋 المميزات الجديدة:")
        print("   • إزالة القوائم الجانبية")
        print("   • شريط أدوات محدث:")
        print("     - لوحة التحكم")
        print("     - المعلومات الأساسية")
        print("     - المعدات")
        print("     - الأصناف")
        print("     - المستخدمين")
        print("     - النسخ الاحتياطي")
        print("   • قوائم علوية جديدة:")
        print("     - ملف")
        print("     - جهات الإضافة")
        print("     - الإعدادات")
        print("     - التقارير")
        print("     - مساعدة")
        print("   • تصميم مبسط ومحسن")
        
        # تشغيل النافذة
        main_window = MainWindow(user_data, db_manager)
        main_window.run()
        
        print("✓ تم تشغيل النظام بنجاح")
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الواجهة: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

if __name__ == "__main__":
    print("بدء اختبار الواجهة الجديدة...")
    success = test_new_interface()
    
    if success:
        print("\n" + "=" * 60)
        print("✅ تم اختبار الواجهة الجديدة بنجاح!")
        print("🎨 التصميم الآن مبسط بدون قوائم جانبية")
        print("📱 شريط أدوات محدث مع الأزرار المطلوبة")
        print("📋 قوائم علوية شاملة")
        print("=" * 60)
    else:
        print("\n" + "=" * 60)
        print("❌ فشل في اختبار الواجهة الجديدة")
        print("=" * 60)
