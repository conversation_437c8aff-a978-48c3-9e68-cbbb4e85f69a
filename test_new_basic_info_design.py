#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار التصميم الجديد لشاشة المعلومات الأساسية
"""

import sys
import os

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_new_design():
    """اختبار التصميم الجديد"""
    try:
        # استيراد المكونات
        from src.modules.basic_info_complete import BasicInfoCompleteModule
        from src.database.database_manager import DatabaseManager
        import tkinter as tk
        import ttkbootstrap as ttk_bs
        
        print("✓ تم استيراد جميع المكونات بنجاح")
        
        # تهيئة قاعدة البيانات
        db_path = "database/supply_management.db"
        db_manager = DatabaseManager(db_path)
        print("✓ تم تهيئة قاعدة البيانات")
        
        # إنشاء نافذة اختبار مع خلفية بيضاء
        root = ttk_bs.Window(themename="cosmo")
        root.title("اختبار التصميم الجديد - شاشة المعلومات الأساسية")
        root.geometry("1400x900")
        root.configure(bg='white')
        root.state('zoomed')  # ملء الشاشة
        
        print("✓ تم إنشاء النافذة الرئيسية")
        
        # إنشاء إطار المحتوى مع خلفية بيضاء
        content_frame = ttk_bs.Frame(root, bootstyle="light")
        content_frame.pack(fill='both', expand=True)
        content_frame.configure(style="Light.TFrame")
        
        print("✓ تم إنشاء إطار المحتوى")
        
        # إنشاء شاشة المعلومات الأساسية
        basic_info_screen = BasicInfoCompleteModule(content_frame, db_manager)
        print("✓ تم إنشاء شاشة المعلومات الأساسية")
        
        # إضافة بعض البيانات التجريبية
        try:
            # إنشاء جدول البيانات إذا لم يكن موجوداً
            db_manager.execute_query("""
                CREATE TABLE IF NOT EXISTS force_basic_info (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    force_name TEXT NOT NULL,
                    unit_number TEXT NOT NULL,
                    force_number TEXT,
                    commander_name TEXT,
                    commander_rank TEXT,
                    commander_position TEXT,
                    supply_name TEXT,
                    supply_rank TEXT,
                    supply_position TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # إدراج بيانات تجريبية
            sample_data = [
                ("الفرقة الأولى", "001", "F001", "أحمد محمد", "عقيد", "قائد الفرقة", "محمد أحمد", "رائد", "ضابط تموين"),
                ("الفرقة الثانية", "002", "F002", "محمد علي", "مقدم", "قائد الفرقة", "علي محمد", "نقيب", "ضابط تموين"),
                ("الفرقة الثالثة", "003", "F003", "علي أحمد", "عقيد", "قائد الفرقة", "أحمد علي", "رائد", "ضابط تموين")
            ]
            
            for data in sample_data:
                db_manager.execute_query("""
                    INSERT OR IGNORE INTO force_basic_info 
                    (force_name, unit_number, force_number, commander_name, commander_rank, 
                     commander_position, supply_name, supply_rank, supply_position)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, data)
            
            # إعادة تحميل البيانات
            basic_info_screen.load_data()
            print("✓ تم إضافة البيانات التجريبية")
            
        except Exception as e:
            print(f"⚠️ تحذير: لم يتم إضافة البيانات التجريبية: {e}")
        
        print("\n🚀 تشغيل الشاشة الجديدة...")
        print("📌 التحسينات الجديدة:")
        print("   • خلفية بيضاء واضحة")
        print("   • تبويبات بألوان مطابقة للصورة")
        print("   • أزرار بدون أيقونات")
        print("   • حقول إدخال منظمة في ثلاثة صفوف")
        print("   • جدول بيانات محسن")
        print("   • تنسيق مطابق للصورة المرفقة")
        
        # تشغيل النافذة
        root.mainloop()
        
        print("✓ تم تشغيل الشاشة بنجاح")
        
    except Exception as e:
        print(f"❌ خطأ في اختبار التصميم الجديد: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

if __name__ == "__main__":
    print("=" * 60)
    print("اختبار التصميم الجديد لشاشة المعلومات الأساسية")
    print("=" * 60)
    
    success = test_new_design()
    
    if success:
        print("\n✅ تم اختبار التصميم الجديد بنجاح!")
    else:
        print("\n❌ فشل في اختبار التصميم الجديد!")
