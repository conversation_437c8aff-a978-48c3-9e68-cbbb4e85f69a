#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
مدير الألوان
"""

from .base_data_manager import BaseDataManager


class ColorsManager(BaseDataManager):
    """فئة إدارة الألوان"""
    
    def __init__(self, parent_frame, db_manager, logger):
        """
        تهيئة مدير الألوان
        
        Args:
            parent_frame: الإطار الأب
            db_manager: مدير قاعدة البيانات
            logger: نظام السجلات
        """
        
        # تعريف أعمدة جدول الألوان
        columns = [
            {
                'id': 'id',
                'text': 'المعرف',
                'width': 80,
                'anchor': 'center',
                'editable': False,
                'searchable': False
            },
            {
                'id': 'name',
                'text': 'اسم اللون',
                'width': 250,
                'anchor': 'w',
                'editable': True,
                'searchable': True,
                'required': True,
                'type': 'text'
            },
            {
                'id': 'hex_code',
                'text': 'الرمز السادس عشري',
                'width': 200,
                'anchor': 'center',
                'editable': True,
                'searchable': True,
                'required': False,
                'type': 'text'
            },
            {
                'id': 'created_at',
                'text': 'تاريخ الإنشاء',
                'width': 150,
                'anchor': 'center',
                'editable': False,
                'searchable': False,
                'type': 'date'
            }
        ]
        
        # استدعاء الفئة الأساسية
        super().__init__(
            parent_frame=parent_frame,
            db_manager=db_manager,
            logger=logger,
            table_name="colors",
            title="إدارة الألوان",
            columns=columns
        )


def create_colors_manager(parent_frame, db_manager, logger):
    """إنشاء مدير الألوان"""
    return ColorsManager(parent_frame, db_manager, logger)
