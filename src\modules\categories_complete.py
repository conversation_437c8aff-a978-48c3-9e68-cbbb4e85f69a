#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
وحدة إدارة الأصناف الكاملة مطابقة للبرنامج الأصلي
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import ttkbootstrap as ttk_bs
from ttkbootstrap.constants import *
from datetime import datetime

class CategoriesCompleteModule:
    def __init__(self, parent_frame, db_manager):
        self.parent = parent_frame
        self.db_manager = db_manager
        self.current_record = None
        self.form_vars = {}
        
        # إنشاء الواجهة
        self.create_interface()
        
        # تحميل البيانات
        self.load_data()
    
    def create_interface(self):
        """إنشاء واجهة إدارة الأصناف مطابقة للبرنامج الأصلي"""
        # مسح المحتوى السابق
        for widget in self.parent.winfo_children():
            widget.destroy()
        
        # الإطار الرئيسي
        main_frame = ttk_bs.Frame(self.parent)
        main_frame.pack(fill=BOTH, expand=True, padx=10, pady=10)
        
        # العنوان الرئيسي
        title_frame = ttk_bs.Frame(main_frame)
        title_frame.pack(fill=X, pady=(0, 10))
        
        title_label = ttk_bs.Label(
            title_frame,
            text="إدارة الأصناف",
            font=("Tahoma", 16, "bold"),
            bootstyle="primary"
        )
        title_label.pack()
        
        # إنشاء التبويبات
        self.create_tabs(main_frame)
        
        # إنشاء شريط الأزرار
        self.create_buttons_bar(main_frame)
        
        # إنشاء منطقة الإدخال
        self.create_input_area(main_frame)
        
        # إنشاء جدول البيانات
        self.create_data_table(main_frame)
    
    def create_tabs(self, parent):
        """إنشاء التبويبات العلوية"""
        tabs_frame = ttk_bs.Frame(parent)
        tabs_frame.pack(fill=X, pady=(0, 10))
        
        # التبويبات مطابقة للبرنامج الأصلي
        tabs = [
            ("القوة العمومية", "force", "info"),
            ("المعدات", "equipment", "info"),
            ("الأصناف", "categories", "primary"),  # النشط
            ("الأسلحة", "weapons", "info"),
            ("المستخدمين", "users", "info"),
            ("أدوات الاستعلام", "queries", "info")
        ]
        
        for text, tab_id, style in tabs:
            btn = ttk_bs.Button(
                tabs_frame,
                text=text,
                command=lambda t=tab_id: self.switch_tab(t),
                bootstyle=style,
                width=15
            )
            btn.pack(side=LEFT, padx=1)
    
    def create_buttons_bar(self, parent):
        """إنشاء شريط الأزرار"""
        buttons_frame = ttk_bs.Frame(parent)
        buttons_frame.pack(fill=X, pady=(0, 10))
        
        # الأزرار مطابقة للبرنامج الأصلي
        buttons = [
            ("إضافة", "add", "success", "🆕"),
            ("حفظ", "save", "primary", "💾"),
            ("تعديل", "edit", "warning", "✏️"),
            ("حذف", "delete", "danger", "🗑️"),
            ("إلغاء", "cancel", "secondary", "❌"),
            ("طباعة", "print", "info", "🖨️"),
            ("تصدير", "export", "info", "📤")
        ]
        
        for text, action, style, icon in buttons:
            btn = ttk_bs.Button(
                buttons_frame,
                text=f"{icon} {text}",
                command=lambda a=action: self.handle_button_action(a),
                bootstyle=style,
                width=12
            )
            btn.pack(side=LEFT, padx=2)
    
    def create_input_area(self, parent):
        """إنشاء منطقة الإدخال مطابقة للبرنامج الأصلي"""
        input_frame = ttk_bs.LabelFrame(parent, text="بيانات الصنف", padding=10)
        input_frame.pack(fill=X, pady=(0, 10))
        
        # الصف الأول من الحقول
        row1_frame = ttk_bs.Frame(input_frame)
        row1_frame.pack(fill=X, pady=5)
        
        # رقم المعدة
        ttk_bs.Label(row1_frame, text="* رقم المعدة:").pack(side=RIGHT, padx=5)
        self.form_vars['equipment_number'] = ttk_bs.StringVar()
        equipment_entry = ttk_bs.Entry(row1_frame, textvariable=self.form_vars['equipment_number'], width=15)
        equipment_entry.pack(side=RIGHT, padx=5)
        
        # اسم المعدة
        ttk_bs.Label(row1_frame, text="* اسم المعدة:").pack(side=RIGHT, padx=5)
        self.form_vars['equipment_name'] = ttk_bs.StringVar()
        equipment_name_combo = ttk_bs.Combobox(row1_frame, textvariable=self.form_vars['equipment_name'], width=25)
        equipment_name_combo.pack(side=RIGHT, padx=5)
        
        # اسم القوة العمومية
        ttk_bs.Label(row1_frame, text="* اسم القوة العمومية:").pack(side=RIGHT, padx=5)
        self.form_vars['force_name'] = ttk_bs.StringVar()
        force_combo = ttk_bs.Combobox(row1_frame, textvariable=self.form_vars['force_name'], width=20)
        force_combo.pack(side=RIGHT, padx=5)
        
        # الصف الثاني من الحقول
        row2_frame = ttk_bs.Frame(input_frame)
        row2_frame.pack(fill=X, pady=5)
        
        # رقم الصنف
        ttk_bs.Label(row2_frame, text="* رقم الصنف:").pack(side=RIGHT, padx=5)
        self.form_vars['category_number'] = ttk_bs.StringVar()
        category_number_entry = ttk_bs.Entry(row2_frame, textvariable=self.form_vars['category_number'], width=15)
        category_number_entry.pack(side=RIGHT, padx=5)
        
        # اسم الصنف
        ttk_bs.Label(row2_frame, text="* اسم الصنف:").pack(side=RIGHT, padx=5)
        self.form_vars['category_name'] = ttk_bs.StringVar()
        category_name_entry = ttk_bs.Entry(row2_frame, textvariable=self.form_vars['category_name'], width=25)
        category_name_entry.pack(side=RIGHT, padx=5)
        
        # التشكيل
        ttk_bs.Label(row2_frame, text="التشكيل:").pack(side=RIGHT, padx=5)
        self.form_vars['formation'] = ttk_bs.StringVar()
        formation_combo = ttk_bs.Combobox(row2_frame, textvariable=self.form_vars['formation'], width=20)
        formation_combo.pack(side=RIGHT, padx=5)
        
        # الصف الثالث من الحقول
        row3_frame = ttk_bs.Frame(input_frame)
        row3_frame.pack(fill=X, pady=5)
        
        # الكمية
        ttk_bs.Label(row3_frame, text="الكمية:").pack(side=RIGHT, padx=5)
        self.form_vars['quantity'] = ttk_bs.StringVar()
        quantity_entry = ttk_bs.Entry(row3_frame, textvariable=self.form_vars['quantity'], width=10)
        quantity_entry.pack(side=RIGHT, padx=5)
        
        # الوحدة
        ttk_bs.Label(row3_frame, text="الوحدة:").pack(side=RIGHT, padx=5)
        self.form_vars['unit'] = ttk_bs.StringVar()
        unit_combo = ttk_bs.Combobox(row3_frame, textvariable=self.form_vars['unit'], width=12)
        unit_combo['values'] = ("قطعة", "كيلو", "متر", "لتر", "صندوق", "حقيبة")
        unit_combo.pack(side=RIGHT, padx=5)
        
        # تاريخ الإضافة
        ttk_bs.Label(row3_frame, text="تاريخ الإضافة:").pack(side=RIGHT, padx=5)
        self.form_vars['addition_date'] = ttk_bs.StringVar()
        addition_date_entry = ttk_bs.Entry(row3_frame, textvariable=self.form_vars['addition_date'], width=12)
        addition_date_entry.pack(side=RIGHT, padx=5)
        
        # الصف الرابع من الحقول
        row4_frame = ttk_bs.Frame(input_frame)
        row4_frame.pack(fill=X, pady=5)
        
        # حالة الصنف
        ttk_bs.Label(row4_frame, text="حالة الصنف:").pack(side=RIGHT, padx=5)
        self.form_vars['status'] = ttk_bs.StringVar()
        status_combo = ttk_bs.Combobox(row4_frame, textvariable=self.form_vars['status'], width=12)
        status_combo['values'] = ("متوفر", "غير متوفر", "قيد الطلب", "منتهي الصلاحية")
        status_combo.pack(side=RIGHT, padx=5)
        
        # الموقع
        ttk_bs.Label(row4_frame, text="الموقع:").pack(side=RIGHT, padx=5)
        self.form_vars['location'] = ttk_bs.StringVar()
        location_entry = ttk_bs.Entry(row4_frame, textvariable=self.form_vars['location'], width=20)
        location_entry.pack(side=RIGHT, padx=5)
        
        # المسؤول
        ttk_bs.Label(row4_frame, text="المسؤول:").pack(side=RIGHT, padx=5)
        self.form_vars['responsible'] = ttk_bs.StringVar()
        responsible_combo = ttk_bs.Combobox(row4_frame, textvariable=self.form_vars['responsible'], width=20)
        responsible_combo.pack(side=RIGHT, padx=5)
        
        # الصف الخامس - الملاحظات
        row5_frame = ttk_bs.Frame(input_frame)
        row5_frame.pack(fill=X, pady=5)
        
        ttk_bs.Label(row5_frame, text="الملاحظات:").pack(side=RIGHT, padx=5)
        self.form_vars['notes'] = ttk_bs.StringVar()
        notes_entry = ttk_bs.Entry(row5_frame, textvariable=self.form_vars['notes'], width=60)
        notes_entry.pack(side=RIGHT, padx=5, fill=X, expand=True)
        
        # تحميل البيانات في القوائم المنسدلة
        self.load_combo_data(force_combo, equipment_name_combo, formation_combo, responsible_combo)
    
    def create_data_table(self, parent):
        """إنشاء جدول البيانات"""
        table_frame = ttk_bs.LabelFrame(parent, text="قائمة الأصناف", padding=10)
        table_frame.pack(fill=BOTH, expand=True)
        
        # إنشاء Treeview
        columns = ("الرقم", "رقم المعدة", "اسم المعدة", "اسم القوة العمومية", "رقم الصنف", "اسم الصنف", "التشكيل")
        self.tree = ttk_bs.Treeview(table_frame, columns=columns, show="headings", height=12)
        
        # تعيين عناوين الأعمدة
        for col in columns:
            self.tree.heading(col, text=col)
            self.tree.column(col, width=120, anchor=CENTER)
        
        # شريط التمرير
        scrollbar = ttk_bs.Scrollbar(table_frame, orient=VERTICAL, command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)
        
        # تخطيط الجدول
        self.tree.pack(side=LEFT, fill=BOTH, expand=True)
        scrollbar.pack(side=RIGHT, fill=Y)
        
        # ربط حدث النقر
        self.tree.bind("<<TreeviewSelect>>", self.on_item_select)
    
    def switch_tab(self, tab_id):
        """تبديل التبويبات"""
        if tab_id == "force":
            # الانتقال لشاشة القوة العمومية
            from src.modules.basic_info_complete import BasicInfoCompleteModule
            BasicInfoCompleteModule(self.parent, self.db_manager)
        elif tab_id == "equipment":
            # الانتقال لشاشة المعدات
            from src.modules.equipment_complete import EquipmentCompleteModule
            EquipmentCompleteModule(self.parent, self.db_manager)
        elif tab_id == "weapons":
            # الانتقال لشاشة الأسلحة
            print("الانتقال لشاشة الأسلحة")
        elif tab_id == "users":
            # الانتقال لشاشة المستخدمين
            from src.modules.users_complete import UsersCompleteModule
            UsersCompleteModule(self.parent, self.db_manager)
        elif tab_id == "queries":
            # الانتقال لأدوات الاستعلام
            print("الانتقال لأدوات الاستعلام")
    
    def handle_button_action(self, action):
        """معالجة أحداث الأزرار"""
        if action == "add":
            self.add_record()
        elif action == "save":
            self.save_record()
        elif action == "edit":
            self.edit_record()
        elif action == "delete":
            self.delete_record()
        elif action == "cancel":
            self.cancel_operation()
        elif action == "print":
            self.print_record()
        elif action == "export":
            self.export_data()
    
    def load_combo_data(self, force_combo, equipment_name_combo, formation_combo, responsible_combo):
        """تحميل بيانات القوائم المنسدلة"""
        try:
            # تحميل أسماء القوة العمومية
            forces = self.db_manager.execute_query("SELECT DISTINCT force_name FROM force_basic_info")
            force_names = [force['force_name'] for force in forces] if forces else []
            force_combo['values'] = force_names
            
            # تحميل أسماء المعدات
            equipment = self.db_manager.execute_query("SELECT DISTINCT name FROM equipment")
            equipment_names = [eq['name'] for eq in equipment] if equipment else []
            equipment_name_combo['values'] = equipment_names
            
            # تحميل التشكيلات
            formations = ["فرقة", "لواء", "كتيبة", "سرية", "فصيل", "مجموعة"]
            formation_combo['values'] = formations
            
            # تحميل الأفراد المسؤولين
            personnel = self.db_manager.execute_query("SELECT full_name FROM personnel WHERE is_active = 1")
            personnel_names = [person['full_name'] for person in personnel] if personnel else []
            responsible_combo['values'] = personnel_names
            
        except Exception as e:
            print(f"خطأ في تحميل بيانات القوائم: {e}")
    
    def load_data(self):
        """تحميل البيانات في الجدول"""
        try:
            # مسح البيانات الحالية
            for item in self.tree.get_children():
                self.tree.delete(item)
            
            # تحميل البيانات من قاعدة البيانات
            query = """
                SELECT c.*, e.name as equipment_name, f.force_name 
                FROM categories c
                LEFT JOIN equipment e ON c.equipment_id = e.id
                LEFT JOIN force_basic_info f ON c.force_id = f.id
                ORDER BY c.id
            """
            records = self.db_manager.execute_query(query)
            
            for i, record in enumerate(records, 1):
                values = (
                    i,
                    record.get('equipment_number', ''),
                    record.get('equipment_name', ''),
                    record.get('force_name', ''),
                    record.get('category_number', ''),
                    record.get('category_name', ''),
                    record.get('formation', '')
                )
                self.tree.insert("", "end", values=values)
                
        except Exception as e:
            print(f"خطأ في تحميل البيانات: {e}")
    
    def on_item_select(self, event):
        """معالجة تحديد عنصر في الجدول"""
        selection = self.tree.selection()
        if selection:
            item = self.tree.item(selection[0])
            values = item['values']
            
            # تحديث الحقول بالبيانات المحددة
            if len(values) >= 7:
                self.form_vars['equipment_number'].set(values[1])
                self.form_vars['equipment_name'].set(values[2])
                self.form_vars['force_name'].set(values[3])
                self.form_vars['category_number'].set(values[4])
                self.form_vars['category_name'].set(values[5])
                self.form_vars['formation'].set(values[6])
    
    def add_record(self):
        """إضافة سجل جديد"""
        self.clear_form()
        messagebox.showinfo("إضافة", "تم تفعيل وضع الإضافة")
    
    def save_record(self):
        """حفظ السجل"""
        try:
            # جمع البيانات من النموذج
            data = {
                'equipment_number': self.form_vars['equipment_number'].get().strip(),
                'equipment_name': self.form_vars['equipment_name'].get().strip(),
                'force_name': self.form_vars['force_name'].get().strip(),
                'category_number': self.form_vars['category_number'].get().strip(),
                'category_name': self.form_vars['category_name'].get().strip(),
                'formation': self.form_vars['formation'].get().strip(),
                'quantity': self.form_vars['quantity'].get().strip(),
                'unit': self.form_vars['unit'].get().strip(),
                'addition_date': self.form_vars['addition_date'].get().strip(),
                'status': self.form_vars['status'].get().strip(),
                'location': self.form_vars['location'].get().strip(),
                'responsible': self.form_vars['responsible'].get().strip(),
                'notes': self.form_vars['notes'].get().strip()
            }
            
            # التحقق من البيانات المطلوبة
            required_fields = ['equipment_number', 'equipment_name', 'force_name', 'category_number', 'category_name']
            missing_fields = [field for field in required_fields if not data[field]]
            
            if missing_fields:
                messagebox.showerror("خطأ", "يرجى ملء جميع الحقول المطلوبة")
                return
            
            # إنشاء استعلام الإدراج
            query = """
                INSERT INTO categories 
                (equipment_number, equipment_name, force_name, category_number, category_name,
                 formation, quantity, unit, addition_date, status, location, responsible, notes)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """
            
            params = (
                data['equipment_number'], data['equipment_name'], data['force_name'],
                data['category_number'], data['category_name'], data['formation'],
                data['quantity'], data['unit'], data['addition_date'], data['status'],
                data['location'], data['responsible'], data['notes']
            )
            
            # تنفيذ الاستعلام
            self.db_manager.execute_query(query, params)
            messagebox.showinfo("نجح", "تم حفظ بيانات الصنف بنجاح")
            
            # إعادة تحميل البيانات
            self.load_data()
            self.clear_form()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في حفظ البيانات: {e}")
    
    def edit_record(self):
        """تعديل السجل"""
        selection = self.tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى تحديد صنف للتعديل")
            return
        
        messagebox.showinfo("تعديل", "تم تفعيل وضع التعديل")
    
    def delete_record(self):
        """حذف السجل"""
        selection = self.tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى تحديد صنف للحذف")
            return
        
        if messagebox.askyesno("تأكيد", "هل أنت متأكد من حذف هذا الصنف؟"):
            self.tree.delete(selection[0])
            messagebox.showinfo("حذف", "تم حذف الصنف بنجاح")
    
    def cancel_operation(self):
        """إلغاء العملية"""
        self.clear_form()
        messagebox.showinfo("إلغاء", "تم إلغاء العملية")
    
    def print_record(self):
        """طباعة السجل"""
        messagebox.showinfo("طباعة", "سيتم تطوير وظيفة الطباعة قريباً")
    
    def export_data(self):
        """تصدير البيانات"""
        messagebox.showinfo("تصدير", "سيتم تطوير وظيفة التصدير قريباً")
    
    def clear_form(self):
        """مسح النموذج"""
        for var in self.form_vars.values():
            var.set("")
