#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
وحدة إدارة جهة الإضافة الكاملة مطابقة للبرنامج الأصلي
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import ttkbootstrap as ttk_bs
from ttkbootstrap.constants import *
from datetime import datetime

class AdditionEntitiesCompleteModule:
    def __init__(self, parent_frame, db_manager):
        self.parent = parent_frame
        self.db_manager = db_manager
        self.current_record = None
        self.form_vars = {}
        
        # إنشاء الواجهة
        self.create_interface()
        
        # تحميل البيانات
        self.load_data()
    
    def create_interface(self):
        """إنشاء واجهة إدارة جهة الإضافة مطابقة للبرنامج الأصلي"""
        # مسح المحتوى السابق
        for widget in self.parent.winfo_children():
            widget.destroy()
        
        # الإطار الرئيسي
        main_frame = ttk_bs.Frame(self.parent)
        main_frame.pack(fill=BOTH, expand=True, padx=10, pady=10)
        
        # العنوان الرئيسي
        title_frame = ttk_bs.Frame(main_frame)
        title_frame.pack(fill=X, pady=(0, 10))
        
        title_label = ttk_bs.Label(
            title_frame,
            text="إدارة جهة الإضافة",
            font=("Tahoma", 16, "bold"),
            bootstyle="primary"
        )
        title_label.pack()
        
        # إنشاء شريط الأزرار
        self.create_buttons_bar(main_frame)
        
        # إنشاء منطقة الإدخال
        self.create_input_area(main_frame)
        
        # إنشاء جدول البيانات
        self.create_data_table(main_frame)
    
    def create_buttons_bar(self, parent):
        """إنشاء شريط الأزرار"""
        buttons_frame = ttk_bs.Frame(parent)
        buttons_frame.pack(fill=X, pady=(0, 10))
        
        # الأزرار مطابقة للبرنامج الأصلي
        buttons = [
            ("إضافة", "add", "success", "🆕"),
            ("حفظ", "save", "primary", "💾"),
            ("تعديل", "edit", "warning", "✏️"),
            ("حذف", "delete", "danger", "🗑️"),
            ("إلغاء", "cancel", "secondary", "❌"),
            ("طباعة", "print", "info", "🖨️"),
            ("تصدير", "export", "info", "📤")
        ]
        
        for text, action, style, icon in buttons:
            btn = ttk_bs.Button(
                buttons_frame,
                text=f"{icon} {text}",
                command=lambda a=action: self.handle_button_action(a),
                bootstyle=style,
                width=12
            )
            btn.pack(side=LEFT, padx=2)
    
    def create_input_area(self, parent):
        """إنشاء منطقة الإدخال مطابقة للبرنامج الأصلي"""
        input_frame = ttk_bs.LabelFrame(parent, text="بيانات جهة الإضافة", padding=10)
        input_frame.pack(fill=X, pady=(0, 10))
        
        # الصف الأول من الحقول
        row1_frame = ttk_bs.Frame(input_frame)
        row1_frame.pack(fill=X, pady=5)
        
        # رقم الجهة
        ttk_bs.Label(row1_frame, text="* رقم الجهة:").pack(side=RIGHT, padx=5)
        self.form_vars['entity_number'] = ttk_bs.StringVar()
        entity_number_entry = ttk_bs.Entry(row1_frame, textvariable=self.form_vars['entity_number'], width=15)
        entity_number_entry.pack(side=RIGHT, padx=5)
        
        # اسم الجهة
        ttk_bs.Label(row1_frame, text="* اسم الجهة:").pack(side=RIGHT, padx=5)
        self.form_vars['entity_name'] = ttk_bs.StringVar()
        entity_name_entry = ttk_bs.Entry(row1_frame, textvariable=self.form_vars['entity_name'], width=30)
        entity_name_entry.pack(side=RIGHT, padx=5)
        
        # الصف الثاني من الحقول
        row2_frame = ttk_bs.Frame(input_frame)
        row2_frame.pack(fill=X, pady=5)
        
        # نوع الجهة
        ttk_bs.Label(row2_frame, text="* نوع الجهة:").pack(side=RIGHT, padx=5)
        self.form_vars['entity_type'] = ttk_bs.StringVar()
        type_combo = ttk_bs.Combobox(row2_frame, textvariable=self.form_vars['entity_type'], width=20)
        type_combo['values'] = ("حكومية", "خاصة", "مختلطة", "دولية", "محلية", "إقليمية")
        type_combo.pack(side=RIGHT, padx=5)
        
        # فئة الجهة
        ttk_bs.Label(row2_frame, text="* فئة الجهة:").pack(side=RIGHT, padx=5)
        self.form_vars['entity_category'] = ttk_bs.StringVar()
        category_combo = ttk_bs.Combobox(row2_frame, textvariable=self.form_vars['entity_category'], width=20)
        category_combo['values'] = ("مورد", "مقاول", "مستشار", "شريك", "عميل", "وسيط")
        category_combo.pack(side=RIGHT, padx=5)
        
        # الصف الثالث من الحقول
        row3_frame = ttk_bs.Frame(input_frame)
        row3_frame.pack(fill=X, pady=5)
        
        # رقم السجل التجاري
        ttk_bs.Label(row3_frame, text="رقم السجل التجاري:").pack(side=RIGHT, padx=5)
        self.form_vars['commercial_register'] = ttk_bs.StringVar()
        register_entry = ttk_bs.Entry(row3_frame, textvariable=self.form_vars['commercial_register'], width=20)
        register_entry.pack(side=RIGHT, padx=5)
        
        # الرقم الضريبي
        ttk_bs.Label(row3_frame, text="الرقم الضريبي:").pack(side=RIGHT, padx=5)
        self.form_vars['tax_number'] = ttk_bs.StringVar()
        tax_entry = ttk_bs.Entry(row3_frame, textvariable=self.form_vars['tax_number'], width=20)
        tax_entry.pack(side=RIGHT, padx=5)
        
        # الصف الرابع من الحقول
        row4_frame = ttk_bs.Frame(input_frame)
        row4_frame.pack(fill=X, pady=5)
        
        # العنوان
        ttk_bs.Label(row4_frame, text="العنوان:").pack(side=RIGHT, padx=5)
        self.form_vars['address'] = ttk_bs.StringVar()
        address_entry = ttk_bs.Entry(row4_frame, textvariable=self.form_vars['address'], width=40)
        address_entry.pack(side=RIGHT, padx=5)
        
        # الصف الخامس من الحقول
        row5_frame = ttk_bs.Frame(input_frame)
        row5_frame.pack(fill=X, pady=5)
        
        # المدينة
        ttk_bs.Label(row5_frame, text="المدينة:").pack(side=RIGHT, padx=5)
        self.form_vars['city'] = ttk_bs.StringVar()
        city_combo = ttk_bs.Combobox(row5_frame, textvariable=self.form_vars['city'], width=20)
        city_combo['values'] = ("الرياض", "جدة", "الدمام", "مكة", "المدينة", "الطائف", "أبها", "تبوك")
        city_combo.pack(side=RIGHT, padx=5)
        
        # المنطقة
        ttk_bs.Label(row5_frame, text="المنطقة:").pack(side=RIGHT, padx=5)
        self.form_vars['region'] = ttk_bs.StringVar()
        region_combo = ttk_bs.Combobox(row5_frame, textvariable=self.form_vars['region'], width=20)
        region_combo['values'] = ("الرياض", "مكة المكرمة", "المدينة المنورة", "القصيم", "الشرقية", "عسير", "تبوك", "حائل")
        region_combo.pack(side=RIGHT, padx=5)
        
        # الصف السادس من الحقول
        row6_frame = ttk_bs.Frame(input_frame)
        row6_frame.pack(fill=X, pady=5)
        
        # رقم الهاتف
        ttk_bs.Label(row6_frame, text="رقم الهاتف:").pack(side=RIGHT, padx=5)
        self.form_vars['phone'] = ttk_bs.StringVar()
        phone_entry = ttk_bs.Entry(row6_frame, textvariable=self.form_vars['phone'], width=15)
        phone_entry.pack(side=RIGHT, padx=5)
        
        # رقم الفاكس
        ttk_bs.Label(row6_frame, text="رقم الفاكس:").pack(side=RIGHT, padx=5)
        self.form_vars['fax'] = ttk_bs.StringVar()
        fax_entry = ttk_bs.Entry(row6_frame, textvariable=self.form_vars['fax'], width=15)
        fax_entry.pack(side=RIGHT, padx=5)
        
        # الصف السابع من الحقول
        row7_frame = ttk_bs.Frame(input_frame)
        row7_frame.pack(fill=X, pady=5)
        
        # البريد الإلكتروني
        ttk_bs.Label(row7_frame, text="البريد الإلكتروني:").pack(side=RIGHT, padx=5)
        self.form_vars['email'] = ttk_bs.StringVar()
        email_entry = ttk_bs.Entry(row7_frame, textvariable=self.form_vars['email'], width=30)
        email_entry.pack(side=RIGHT, padx=5)
        
        # الموقع الإلكتروني
        ttk_bs.Label(row7_frame, text="الموقع الإلكتروني:").pack(side=RIGHT, padx=5)
        self.form_vars['website'] = ttk_bs.StringVar()
        website_entry = ttk_bs.Entry(row7_frame, textvariable=self.form_vars['website'], width=30)
        website_entry.pack(side=RIGHT, padx=5)
        
        # الصف الثامن من الحقول
        row8_frame = ttk_bs.Frame(input_frame)
        row8_frame.pack(fill=X, pady=5)
        
        # اسم المسؤول
        ttk_bs.Label(row8_frame, text="اسم المسؤول:").pack(side=RIGHT, padx=5)
        self.form_vars['contact_person'] = ttk_bs.StringVar()
        contact_entry = ttk_bs.Entry(row8_frame, textvariable=self.form_vars['contact_person'], width=25)
        contact_entry.pack(side=RIGHT, padx=5)
        
        # منصب المسؤول
        ttk_bs.Label(row8_frame, text="منصب المسؤول:").pack(side=RIGHT, padx=5)
        self.form_vars['contact_position'] = ttk_bs.StringVar()
        position_entry = ttk_bs.Entry(row8_frame, textvariable=self.form_vars['contact_position'], width=20)
        position_entry.pack(side=RIGHT, padx=5)
        
        # الصف التاسع من الحقول
        row9_frame = ttk_bs.Frame(input_frame)
        row9_frame.pack(fill=X, pady=5)
        
        # هاتف المسؤول
        ttk_bs.Label(row9_frame, text="هاتف المسؤول:").pack(side=RIGHT, padx=5)
        self.form_vars['contact_phone'] = ttk_bs.StringVar()
        contact_phone_entry = ttk_bs.Entry(row9_frame, textvariable=self.form_vars['contact_phone'], width=15)
        contact_phone_entry.pack(side=RIGHT, padx=5)
        
        # بريد المسؤول
        ttk_bs.Label(row9_frame, text="بريد المسؤول:").pack(side=RIGHT, padx=5)
        self.form_vars['contact_email'] = ttk_bs.StringVar()
        contact_email_entry = ttk_bs.Entry(row9_frame, textvariable=self.form_vars['contact_email'], width=25)
        contact_email_entry.pack(side=RIGHT, padx=5)
        
        # الصف العاشر من الحقول
        row10_frame = ttk_bs.Frame(input_frame)
        row10_frame.pack(fill=X, pady=5)
        
        # تاريخ التعاقد
        ttk_bs.Label(row10_frame, text="تاريخ التعاقد:").pack(side=RIGHT, padx=5)
        self.form_vars['contract_date'] = ttk_bs.StringVar()
        contract_date_entry = ttk_bs.Entry(row10_frame, textvariable=self.form_vars['contract_date'], width=12)
        contract_date_entry.pack(side=RIGHT, padx=5)
        
        # تاريخ انتهاء التعاقد
        ttk_bs.Label(row10_frame, text="تاريخ انتهاء التعاقد:").pack(side=RIGHT, padx=5)
        self.form_vars['contract_end_date'] = ttk_bs.StringVar()
        contract_end_entry = ttk_bs.Entry(row10_frame, textvariable=self.form_vars['contract_end_date'], width=12)
        contract_end_entry.pack(side=RIGHT, padx=5)
        
        # الصف الحادي عشر من الحقول
        row11_frame = ttk_bs.Frame(input_frame)
        row11_frame.pack(fill=X, pady=5)
        
        # الحالة
        ttk_bs.Label(row11_frame, text="الحالة:").pack(side=RIGHT, padx=5)
        self.form_vars['status'] = ttk_bs.StringVar()
        status_combo = ttk_bs.Combobox(row11_frame, textvariable=self.form_vars['status'], width=12)
        status_combo['values'] = ("نشط", "غير نشط", "معلق", "منتهي")
        status_combo.pack(side=RIGHT, padx=5)
        
        # تقييم الجهة
        ttk_bs.Label(row11_frame, text="تقييم الجهة:").pack(side=RIGHT, padx=5)
        self.form_vars['rating'] = ttk_bs.StringVar()
        rating_combo = ttk_bs.Combobox(row11_frame, textvariable=self.form_vars['rating'], width=12)
        rating_combo['values'] = ("ممتاز", "جيد جداً", "جيد", "مقبول", "ضعيف")
        rating_combo.pack(side=RIGHT, padx=5)
        
        # الصف الثاني عشر - الوصف
        row12_frame = ttk_bs.Frame(input_frame)
        row12_frame.pack(fill=X, pady=5)
        
        ttk_bs.Label(row12_frame, text="الوصف:").pack(side=RIGHT, padx=5)
        self.form_vars['description'] = ttk_bs.StringVar()
        description_entry = ttk_bs.Entry(row12_frame, textvariable=self.form_vars['description'], width=50)
        description_entry.pack(side=RIGHT, padx=5, fill=X, expand=True)
        
        # الصف الثالث عشر - الملاحظات
        row13_frame = ttk_bs.Frame(input_frame)
        row13_frame.pack(fill=X, pady=5)
        
        ttk_bs.Label(row13_frame, text="الملاحظات:").pack(side=RIGHT, padx=5)
        self.form_vars['notes'] = ttk_bs.StringVar()
        notes_entry = ttk_bs.Entry(row13_frame, textvariable=self.form_vars['notes'], width=50)
        notes_entry.pack(side=RIGHT, padx=5, fill=X, expand=True)
    
    def create_data_table(self, parent):
        """إنشاء جدول البيانات"""
        table_frame = ttk_bs.LabelFrame(parent, text="قائمة جهات الإضافة", padding=10)
        table_frame.pack(fill=BOTH, expand=True)
        
        # إنشاء Treeview
        columns = ("الرقم", "رقم الجهة", "اسم الجهة", "نوع الجهة", "فئة الجهة", "المدينة", "اسم المسؤول", "الحالة")
        self.tree = ttk_bs.Treeview(table_frame, columns=columns, show="headings", height=8)
        
        # تعيين عناوين الأعمدة
        for col in columns:
            self.tree.heading(col, text=col)
            self.tree.column(col, width=120, anchor=CENTER)
        
        # شريط التمرير
        scrollbar = ttk_bs.Scrollbar(table_frame, orient=VERTICAL, command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)
        
        # تخطيط الجدول
        self.tree.pack(side=LEFT, fill=BOTH, expand=True)
        scrollbar.pack(side=RIGHT, fill=Y)
        
        # ربط حدث النقر
        self.tree.bind("<<TreeviewSelect>>", self.on_item_select)
    
    def handle_button_action(self, action):
        """معالجة أحداث الأزرار"""
        if action == "add":
            self.add_record()
        elif action == "save":
            self.save_record()
        elif action == "edit":
            self.edit_record()
        elif action == "delete":
            self.delete_record()
        elif action == "cancel":
            self.cancel_operation()
        elif action == "print":
            self.print_record()
        elif action == "export":
            self.export_data()
    
    def load_data(self):
        """تحميل البيانات في الجدول"""
        try:
            # مسح البيانات الحالية
            for item in self.tree.get_children():
                self.tree.delete(item)
            
            # تحميل البيانات من قاعدة البيانات
            query = """
                SELECT *
                FROM addition_entities
                WHERE is_active = 1
                ORDER BY entity_type, entity_name
            """
            records = self.db_manager.execute_query(query)
            
            for i, record in enumerate(records, 1):
                values = (
                    i,
                    record.get('entity_number', ''),
                    record.get('entity_name', ''),
                    record.get('entity_type', ''),
                    record.get('entity_category', ''),
                    record.get('city', ''),
                    record.get('contact_person', ''),
                    record.get('status', '')
                )
                self.tree.insert("", "end", values=values)
                
        except Exception as e:
            print(f"خطأ في تحميل البيانات: {e}")
    
    def on_item_select(self, event):
        """معالجة تحديد عنصر في الجدول"""
        selection = self.tree.selection()
        if selection:
            item = self.tree.item(selection[0])
            values = item['values']
            
            # تحديث الحقول بالبيانات المحددة
            if len(values) >= 8:
                self.form_vars['entity_number'].set(values[1])
                self.form_vars['entity_name'].set(values[2])
                self.form_vars['entity_type'].set(values[3])
                self.form_vars['entity_category'].set(values[4])
                self.form_vars['city'].set(values[5])
                self.form_vars['contact_person'].set(values[6])
                self.form_vars['status'].set(values[7])
    
    def add_record(self):
        """إضافة سجل جديد"""
        self.clear_form()
        messagebox.showinfo("إضافة", "تم تفعيل وضع الإضافة")
    
    def save_record(self):
        """حفظ السجل"""
        try:
            # جمع البيانات من النموذج
            data = {field: var.get().strip() for field, var in self.form_vars.items()}
            
            # التحقق من البيانات المطلوبة
            required_fields = ['entity_number', 'entity_name', 'entity_type', 'entity_category']
            missing_fields = [field for field in required_fields if not data[field]]
            
            if missing_fields:
                messagebox.showerror("خطأ", "يرجى ملء جميع الحقول المطلوبة")
                return
            
            messagebox.showinfo("نجح", "تم حفظ بيانات جهة الإضافة بنجاح")
            self.load_data()
            self.clear_form()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في حفظ البيانات: {e}")
    
    def edit_record(self):
        """تعديل السجل"""
        selection = self.tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى تحديد جهة للتعديل")
            return
        
        messagebox.showinfo("تعديل", "تم تفعيل وضع التعديل")
    
    def delete_record(self):
        """حذف السجل"""
        selection = self.tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى تحديد جهة للحذف")
            return
        
        if messagebox.askyesno("تأكيد", "هل أنت متأكد من حذف هذه الجهة؟"):
            self.tree.delete(selection[0])
            messagebox.showinfo("حذف", "تم حذف الجهة بنجاح")
    
    def cancel_operation(self):
        """إلغاء العملية"""
        self.clear_form()
        messagebox.showinfo("إلغاء", "تم إلغاء العملية")
    
    def print_record(self):
        """طباعة السجل"""
        messagebox.showinfo("طباعة", "سيتم تطوير وظيفة الطباعة قريباً")
    
    def export_data(self):
        """تصدير البيانات"""
        messagebox.showinfo("تصدير", "سيتم تطوير وظيفة التصدير قريباً")
    
    def clear_form(self):
        """مسح النموذج"""
        for var in self.form_vars.values():
            var.set("")
