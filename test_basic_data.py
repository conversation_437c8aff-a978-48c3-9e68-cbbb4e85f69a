#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار وحدات البيانات الأساسية
"""

import sys
import os

# إضافة مسار src إلى Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_basic_data_modules():
    """اختبار وحدات البيانات الأساسية"""
    try:
        print("جاري اختبار وحدات البيانات الأساسية...")
        
        # استيراد الوحدات
        from database.database_manager import DatabaseManager
        from modules.ranks_manager import RanksManager
        from modules.job_titles_manager import JobTitlesManager
        from modules.units_manager import UnitsManager
        from modules.colors_manager import ColorsManager
        from utils.logger import Logger
        
        print("✓ تم استيراد جميع وحدات البيانات الأساسية بنجاح")
        
        # تهيئة قاعدة البيانات والسجلات
        db_manager = DatabaseManager("database/supply_management.db")
        logger = Logger()
        
        print("✓ تم تهيئة قاعدة البيانات ونظام السجلات")
        
        # اختبار البيانات الموجودة
        ranks_count = db_manager.get_count("ranks")
        job_titles_count = db_manager.get_count("job_titles")
        units_count = db_manager.get_count("units")
        colors_count = db_manager.get_count("colors")
        
        print(f"✓ عدد الرتب: {ranks_count}")
        print(f"✓ عدد المسميات الوظيفية: {job_titles_count}")
        print(f"✓ عدد الوحدات: {units_count}")
        print(f"✓ عدد الألوان: {colors_count}")
        
        # تنظيف البيانات السابقة
        print("\nتنظيف البيانات السابقة...")
        db_manager.execute_query("DELETE FROM ranks WHERE name LIKE '%اختبار%'")
        db_manager.execute_query("DELETE FROM job_titles WHERE name LIKE '%اختبار%'")
        db_manager.execute_query("DELETE FROM units WHERE name LIKE '%اختبار%'")
        db_manager.execute_query("DELETE FROM colors WHERE name LIKE '%اختبار%'")
        print("✓ تم تنظيف البيانات السابقة")

        # اختبار إضافة بيانات جديدة
        print("\nاختبار إضافة بيانات جديدة...")

        # إضافة رتبة جديدة
        new_rank = {
            "name": "رتبة اختبار",
            "abbreviation": "ر.ا",
            "order_number": 1
        }
        rank_id = db_manager.insert_record("ranks", new_rank)
        print(f"✓ تم إضافة رتبة جديدة برقم: {rank_id}")

        # إضافة مسمى وظيفي جديد
        new_job_title = {
            "name": "مسمى اختبار",
            "description": "مسمى للاختبار"
        }
        job_title_id = db_manager.insert_record("job_titles", new_job_title)
        print(f"✓ تم إضافة مسمى وظيفي جديد برقم: {job_title_id}")

        # إضافة وحدة جديدة
        new_unit = {
            "name": "وحدة اختبار",
            "code": "TEST001",
            "location": "موقع الاختبار",
            "phone": "123456789"
        }
        unit_id = db_manager.insert_record("units", new_unit)
        print(f"✓ تم إضافة وحدة جديدة برقم: {unit_id}")

        # إضافة لون جديد
        new_color = {
            "name": "لون اختبار",
            "hex_code": "#FF0000"
        }
        color_id = db_manager.insert_record("colors", new_color)
        print(f"✓ تم إضافة لون جديد برقم: {color_id}")
        
        # اختبار البحث
        print("\nاختبار البحث...")

        search_results = db_manager.search_records("ranks", "اختبار", ["name"])
        print(f"✓ نتائج البحث في الرتب: {len(search_results)} سجل")

        search_results = db_manager.search_records("job_titles", "اختبار", ["name", "description"])
        print(f"✓ نتائج البحث في المسميات الوظيفية: {len(search_results)} سجل")

        # اختبار التحديث
        print("\nاختبار التحديث...")

        updated_rank = {"abbreviation": "ر.م"}
        success = db_manager.update_record("ranks", rank_id, updated_rank)
        print(f"✓ تحديث الرتبة: {'نجح' if success else 'فشل'}")
        
        # اختبار الحذف
        print("\nاختبار الحذف...")
        
        success = db_manager.delete_record("ranks", rank_id)
        print(f"✓ حذف الرتبة: {'نجح' if success else 'فشل'}")
        
        success = db_manager.delete_record("job_titles", job_title_id)
        print(f"✓ حذف المسمى الوظيفي: {'نجح' if success else 'فشل'}")
        
        success = db_manager.delete_record("units", unit_id)
        print(f"✓ حذف الوحدة: {'نجح' if success else 'فشل'}")
        
        success = db_manager.delete_record("colors", color_id)
        print(f"✓ حذف اللون: {'نجح' if success else 'فشل'}")
        
        print("\n" + "="*60)
        print("تم اختبار وحدات البيانات الأساسية بنجاح!")
        print("جميع العمليات (إضافة، تعديل، حذف، بحث) تعمل بشكل صحيح")
        print("="*60)
        
        # إغلاق قاعدة البيانات
        db_manager.close()
        
        return True
        
    except Exception as e:
        print(f"خطأ في اختبار وحدات البيانات الأساسية: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_basic_data_modules()
