#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار التطبيق
"""

import sys
import os

# إضافة مسار src إلى Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_app():
    """اختبار التطبيق"""
    try:
        print("جاري اختبار التطبيق...")
        
        # استيراد الوحدات
        from database.database_manager import DatabaseManager
        from ui.login_window import LoginWindow
        from ui.main_window import MainWindow
        from utils.config import Config
        from utils.logger import Logger
        
        print("✓ تم استيراد جميع الوحدات بنجاح")
        
        # اختبار الإعدادات
        config = Config()
        print(f"✓ تم تحميل الإعدادات - السمة: {config.get_theme()}")
        
        # اختبار السجلات
        logger = Logger()
        logger.info("اختبار نظام السجلات")
        print("✓ تم تهيئة نظام السجلات")
        
        # اختبار قاعدة البيانات
        db_manager = DatabaseManager("database/supply_management.db")
        print("✓ تم تهيئة قاعدة البيانات")
        
        # اختبار معلومات قاعدة البيانات
        info = db_manager.get_database_info()
        print(f"✓ عدد الجداول: {len(info['tables'])}")
        
        print("\n" + "="*50)
        print("جميع الاختبارات نجحت!")
        print("يمكنك الآن تشغيل التطبيق باستخدام: python main.py")
        print("="*50)
        
        # إغلاق قاعدة البيانات
        db_manager.close()
        
        return True
        
    except Exception as e:
        print(f"خطأ في اختبار التطبيق: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_app()
