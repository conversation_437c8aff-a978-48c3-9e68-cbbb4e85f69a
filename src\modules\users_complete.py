#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
وحدة إدارة المستخدمين الكاملة مطابقة للبرنامج الأصلي
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import ttkbootstrap as ttk_bs
from ttkbootstrap.constants import *
from datetime import datetime
import hashlib

class UsersCompleteModule:
    def __init__(self, parent_frame, db_manager):
        self.parent = parent_frame
        self.db_manager = db_manager
        self.current_record = None
        self.form_vars = {}
        
        # إنشاء الواجهة
        self.create_interface()
        
        # تحميل البيانات
        self.load_data()
    
    def create_interface(self):
        """إنشاء واجهة إدارة المستخدمين مطابقة للبرنامج الأصلي"""
        # مسح المحتوى السابق
        for widget in self.parent.winfo_children():
            widget.destroy()
        
        # الإطار الرئيسي
        main_frame = ttk_bs.Frame(self.parent)
        main_frame.pack(fill=BOTH, expand=True, padx=10, pady=10)
        
        # العنوان الرئيسي
        title_frame = ttk_bs.Frame(main_frame)
        title_frame.pack(fill=X, pady=(0, 10))
        
        title_label = ttk_bs.Label(
            title_frame,
            text="إدارة المستخدمين",
            font=("Tahoma", 16, "bold"),
            bootstyle="primary"
        )
        title_label.pack()
        
        # إنشاء التبويبات
        self.create_tabs(main_frame)
        
        # إنشاء شريط الأزرار
        self.create_buttons_bar(main_frame)
        
        # إنشاء منطقة الإدخال
        self.create_input_area(main_frame)
        
        # إنشاء جدول البيانات
        self.create_data_table(main_frame)
    
    def create_tabs(self, parent):
        """إنشاء التبويبات العلوية"""
        tabs_frame = ttk_bs.Frame(parent)
        tabs_frame.pack(fill=X, pady=(0, 10))
        
        # التبويبات مطابقة للبرنامج الأصلي
        tabs = [
            ("القوة العمومية", "force", "info"),
            ("المعدات", "equipment", "info"),
            ("الأصناف", "categories", "info"),
            ("الأسلحة", "weapons", "info"),
            ("المستخدمين", "users", "primary"),  # النشط
            ("أدوات الاستعلام", "queries", "info")
        ]
        
        for text, tab_id, style in tabs:
            btn = ttk_bs.Button(
                tabs_frame,
                text=text,
                command=lambda t=tab_id: self.switch_tab(t),
                bootstyle=style,
                width=15
            )
            btn.pack(side=LEFT, padx=1)
    
    def create_buttons_bar(self, parent):
        """إنشاء شريط الأزرار"""
        buttons_frame = ttk_bs.Frame(parent)
        buttons_frame.pack(fill=X, pady=(0, 10))
        
        # الأزرار مطابقة للبرنامج الأصلي
        buttons = [
            ("إضافة", "add", "success", "🆕"),
            ("حفظ", "save", "primary", "💾"),
            ("تعديل", "edit", "warning", "✏️"),
            ("حذف", "delete", "danger", "🗑️"),
            ("إلغاء", "cancel", "secondary", "❌"),
            ("طباعة", "print", "info", "🖨️"),
            ("تصدير", "export", "info", "📤")
        ]
        
        for text, action, style, icon in buttons:
            btn = ttk_bs.Button(
                buttons_frame,
                text=f"{icon} {text}",
                command=lambda a=action: self.handle_button_action(a),
                bootstyle=style,
                width=12
            )
            btn.pack(side=LEFT, padx=2)
    
    def create_input_area(self, parent):
        """إنشاء منطقة الإدخال مطابقة للبرنامج الأصلي"""
        input_frame = ttk_bs.LabelFrame(parent, text="بيانات المستخدم", padding=10)
        input_frame.pack(fill=X, pady=(0, 10))
        
        # الصف الأول من الحقول
        row1_frame = ttk_bs.Frame(input_frame)
        row1_frame.pack(fill=X, pady=5)
        
        # الرقم العسكري
        ttk_bs.Label(row1_frame, text="* الرقم العسكري:").pack(side=RIGHT, padx=5)
        self.form_vars['military_number'] = ttk_bs.StringVar()
        military_entry = ttk_bs.Entry(row1_frame, textvariable=self.form_vars['military_number'], width=15)
        military_entry.pack(side=RIGHT, padx=5)
        
        # الاسم الكامل
        ttk_bs.Label(row1_frame, text="* الاسم الكامل:").pack(side=RIGHT, padx=5)
        self.form_vars['full_name'] = ttk_bs.StringVar()
        name_entry = ttk_bs.Entry(row1_frame, textvariable=self.form_vars['full_name'], width=25)
        name_entry.pack(side=RIGHT, padx=5)
        
        # الرتبة
        ttk_bs.Label(row1_frame, text="* الرتبة:").pack(side=RIGHT, padx=5)
        self.form_vars['rank'] = ttk_bs.StringVar()
        rank_combo = ttk_bs.Combobox(row1_frame, textvariable=self.form_vars['rank'], width=15)
        rank_combo.pack(side=RIGHT, padx=5)
        
        # الصف الثاني من الحقول
        row2_frame = ttk_bs.Frame(input_frame)
        row2_frame.pack(fill=X, pady=5)
        
        # الوظيفة
        ttk_bs.Label(row2_frame, text="* الوظيفة:").pack(side=RIGHT, padx=5)
        self.form_vars['job_title'] = ttk_bs.StringVar()
        job_combo = ttk_bs.Combobox(row2_frame, textvariable=self.form_vars['job_title'], width=20)
        job_combo.pack(side=RIGHT, padx=5)
        
        # الوحدة
        ttk_bs.Label(row2_frame, text="* الوحدة:").pack(side=RIGHT, padx=5)
        self.form_vars['unit'] = ttk_bs.StringVar()
        unit_combo = ttk_bs.Combobox(row2_frame, textvariable=self.form_vars['unit'], width=20)
        unit_combo.pack(side=RIGHT, padx=5)
        
        # رقم الهوية
        ttk_bs.Label(row2_frame, text="* رقم الهوية:").pack(side=RIGHT, padx=5)
        self.form_vars['national_id'] = ttk_bs.StringVar()
        id_entry = ttk_bs.Entry(row2_frame, textvariable=self.form_vars['national_id'], width=15)
        id_entry.pack(side=RIGHT, padx=5)
        
        # الصف الثالث من الحقول
        row3_frame = ttk_bs.Frame(input_frame)
        row3_frame.pack(fill=X, pady=5)
        
        # رقم الجوال
        ttk_bs.Label(row3_frame, text="رقم الجوال:").pack(side=RIGHT, padx=5)
        self.form_vars['phone'] = ttk_bs.StringVar()
        phone_entry = ttk_bs.Entry(row3_frame, textvariable=self.form_vars['phone'], width=15)
        phone_entry.pack(side=RIGHT, padx=5)
        
        # تاريخ الميلاد
        ttk_bs.Label(row3_frame, text="تاريخ الميلاد:").pack(side=RIGHT, padx=5)
        self.form_vars['birth_date'] = ttk_bs.StringVar()
        birth_entry = ttk_bs.Entry(row3_frame, textvariable=self.form_vars['birth_date'], width=12)
        birth_entry.pack(side=RIGHT, padx=5)
        
        # تاريخ التجنيد
        ttk_bs.Label(row3_frame, text="تاريخ التجنيد:").pack(side=RIGHT, padx=5)
        self.form_vars['recruitment_date'] = ttk_bs.StringVar()
        recruitment_entry = ttk_bs.Entry(row3_frame, textvariable=self.form_vars['recruitment_date'], width=12)
        recruitment_entry.pack(side=RIGHT, padx=5)
        
        # الصف الرابع من الحقول
        row4_frame = ttk_bs.Frame(input_frame)
        row4_frame.pack(fill=X, pady=5)
        
        # اسم المستخدم
        ttk_bs.Label(row4_frame, text="* اسم المستخدم:").pack(side=RIGHT, padx=5)
        self.form_vars['username'] = ttk_bs.StringVar()
        username_entry = ttk_bs.Entry(row4_frame, textvariable=self.form_vars['username'], width=15)
        username_entry.pack(side=RIGHT, padx=5)
        
        # كلمة المرور
        ttk_bs.Label(row4_frame, text="* كلمة المرور:").pack(side=RIGHT, padx=5)
        self.form_vars['password'] = ttk_bs.StringVar()
        password_entry = ttk_bs.Entry(row4_frame, textvariable=self.form_vars['password'], width=15, show="*")
        password_entry.pack(side=RIGHT, padx=5)
        
        # دور المستخدم
        ttk_bs.Label(row4_frame, text="* دور المستخدم:").pack(side=RIGHT, padx=5)
        self.form_vars['role'] = ttk_bs.StringVar()
        role_combo = ttk_bs.Combobox(row4_frame, textvariable=self.form_vars['role'], width=15)
        role_combo['values'] = ("مدير", "مستخدم", "مشاهد")
        role_combo.pack(side=RIGHT, padx=5)
        
        # الصف الخامس من الحقول
        row5_frame = ttk_bs.Frame(input_frame)
        row5_frame.pack(fill=X, pady=5)
        
        # الحالة
        ttk_bs.Label(row5_frame, text="الحالة:").pack(side=RIGHT, padx=5)
        self.form_vars['status'] = ttk_bs.StringVar()
        status_combo = ttk_bs.Combobox(row5_frame, textvariable=self.form_vars['status'], width=12)
        status_combo['values'] = ("نشط", "غير نشط", "معلق")
        status_combo.pack(side=RIGHT, padx=5)
        
        # العنوان
        ttk_bs.Label(row5_frame, text="العنوان:").pack(side=RIGHT, padx=5)
        self.form_vars['address'] = ttk_bs.StringVar()
        address_entry = ttk_bs.Entry(row5_frame, textvariable=self.form_vars['address'], width=30)
        address_entry.pack(side=RIGHT, padx=5)
        
        # الصف السادس - الملاحظات
        row6_frame = ttk_bs.Frame(input_frame)
        row6_frame.pack(fill=X, pady=5)
        
        ttk_bs.Label(row6_frame, text="الملاحظات:").pack(side=RIGHT, padx=5)
        self.form_vars['notes'] = ttk_bs.StringVar()
        notes_entry = ttk_bs.Entry(row6_frame, textvariable=self.form_vars['notes'], width=60)
        notes_entry.pack(side=RIGHT, padx=5, fill=X, expand=True)
        
        # تحميل البيانات في القوائم المنسدلة
        self.load_combo_data(rank_combo, job_combo, unit_combo)
    
    def create_data_table(self, parent):
        """إنشاء جدول البيانات"""
        table_frame = ttk_bs.LabelFrame(parent, text="قائمة المستخدمين", padding=10)
        table_frame.pack(fill=BOTH, expand=True)
        
        # إنشاء Treeview
        columns = ("الرقم", "الرقم العسكري", "الاسم", "الرتبة", "الوظيفة", "الوحدة", "رقم الهوية", "رقم الجوال")
        self.tree = ttk_bs.Treeview(table_frame, columns=columns, show="headings", height=12)
        
        # تعيين عناوين الأعمدة
        for col in columns:
            self.tree.heading(col, text=col)
            self.tree.column(col, width=120, anchor=CENTER)
        
        # شريط التمرير
        scrollbar = ttk_bs.Scrollbar(table_frame, orient=VERTICAL, command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)
        
        # تخطيط الجدول
        self.tree.pack(side=LEFT, fill=BOTH, expand=True)
        scrollbar.pack(side=RIGHT, fill=Y)
        
        # ربط حدث النقر
        self.tree.bind("<<TreeviewSelect>>", self.on_item_select)
    
    def switch_tab(self, tab_id):
        """تبديل التبويبات"""
        if tab_id == "force":
            # الانتقال لشاشة القوة العمومية
            from src.modules.basic_info_complete import BasicInfoCompleteModule
            BasicInfoCompleteModule(self.parent, self.db_manager)
        elif tab_id == "equipment":
            # الانتقال لشاشة المعدات
            from src.modules.equipment_complete import EquipmentCompleteModule
            EquipmentCompleteModule(self.parent, self.db_manager)
        elif tab_id == "categories":
            # الانتقال لشاشة الأصناف
            from src.modules.categories_complete import CategoriesCompleteModule
            CategoriesCompleteModule(self.parent, self.db_manager)
        elif tab_id == "weapons":
            # الانتقال لشاشة الأسلحة
            print("الانتقال لشاشة الأسلحة")
        elif tab_id == "queries":
            # الانتقال لأدوات الاستعلام
            print("الانتقال لأدوات الاستعلام")
    
    def handle_button_action(self, action):
        """معالجة أحداث الأزرار"""
        if action == "add":
            self.add_record()
        elif action == "save":
            self.save_record()
        elif action == "edit":
            self.edit_record()
        elif action == "delete":
            self.delete_record()
        elif action == "cancel":
            self.cancel_operation()
        elif action == "print":
            self.print_record()
        elif action == "export":
            self.export_data()
    
    def load_combo_data(self, rank_combo, job_combo, unit_combo):
        """تحميل بيانات القوائم المنسدلة"""
        try:
            # تحميل الرتب
            ranks = self.db_manager.execute_query("SELECT name FROM ranks ORDER BY id")
            rank_names = [rank['name'] for rank in ranks] if ranks else []
            rank_combo['values'] = rank_names
            
            # تحميل المسميات الوظيفية
            jobs = self.db_manager.execute_query("SELECT name FROM job_titles ORDER BY id")
            job_names = [job['name'] for job in jobs] if jobs else []
            job_combo['values'] = job_names
            
            # تحميل الوحدات
            units = self.db_manager.execute_query("SELECT name FROM units ORDER BY id")
            unit_names = [unit['name'] for unit in units] if units else []
            unit_combo['values'] = unit_names
            
        except Exception as e:
            print(f"خطأ في تحميل بيانات القوائم: {e}")
    
    def load_data(self):
        """تحميل البيانات في الجدول"""
        try:
            # مسح البيانات الحالية
            for item in self.tree.get_children():
                self.tree.delete(item)
            
            # تحميل البيانات من قاعدة البيانات
            query = """
                SELECT p.*, r.name as rank_name, j.name as job_name, u.name as unit_name
                FROM personnel p
                LEFT JOIN ranks r ON p.rank_id = r.id
                LEFT JOIN job_titles j ON p.job_title_id = j.id
                LEFT JOIN units u ON p.unit_id = u.id
                WHERE p.is_active = 1
                ORDER BY p.id
            """
            records = self.db_manager.execute_query(query)
            
            for i, record in enumerate(records, 1):
                values = (
                    i,
                    record.get('military_number', ''),
                    record.get('full_name', ''),
                    record.get('rank_name', ''),
                    record.get('job_name', ''),
                    record.get('unit_name', ''),
                    record.get('national_id', ''),
                    record.get('phone', '')
                )
                self.tree.insert("", "end", values=values)
                
        except Exception as e:
            print(f"خطأ في تحميل البيانات: {e}")
    
    def on_item_select(self, event):
        """معالجة تحديد عنصر في الجدول"""
        selection = self.tree.selection()
        if selection:
            item = self.tree.item(selection[0])
            values = item['values']
            
            # تحديث الحقول بالبيانات المحددة
            if len(values) >= 8:
                self.form_vars['military_number'].set(values[1])
                self.form_vars['full_name'].set(values[2])
                self.form_vars['rank'].set(values[3])
                self.form_vars['job_title'].set(values[4])
                self.form_vars['unit'].set(values[5])
                self.form_vars['national_id'].set(values[6])
                self.form_vars['phone'].set(values[7])
    
    def add_record(self):
        """إضافة سجل جديد"""
        self.clear_form()
        messagebox.showinfo("إضافة", "تم تفعيل وضع الإضافة")
    
    def save_record(self):
        """حفظ السجل"""
        try:
            # جمع البيانات من النموذج
            data = {
                'military_number': self.form_vars['military_number'].get().strip(),
                'full_name': self.form_vars['full_name'].get().strip(),
                'rank': self.form_vars['rank'].get().strip(),
                'job_title': self.form_vars['job_title'].get().strip(),
                'unit': self.form_vars['unit'].get().strip(),
                'national_id': self.form_vars['national_id'].get().strip(),
                'phone': self.form_vars['phone'].get().strip(),
                'birth_date': self.form_vars['birth_date'].get().strip(),
                'recruitment_date': self.form_vars['recruitment_date'].get().strip(),
                'username': self.form_vars['username'].get().strip(),
                'password': self.form_vars['password'].get().strip(),
                'role': self.form_vars['role'].get().strip(),
                'status': self.form_vars['status'].get().strip(),
                'address': self.form_vars['address'].get().strip(),
                'notes': self.form_vars['notes'].get().strip()
            }
            
            # التحقق من البيانات المطلوبة
            required_fields = ['military_number', 'full_name', 'rank', 'job_title', 'unit', 'national_id', 'username', 'password', 'role']
            missing_fields = [field for field in required_fields if not data[field]]
            
            if missing_fields:
                messagebox.showerror("خطأ", "يرجى ملء جميع الحقول المطلوبة")
                return
            
            # تشفير كلمة المرور
            hashed_password = hashlib.sha256(data['password'].encode()).hexdigest()
            
            # إنشاء استعلام الإدراج
            query = """
                INSERT INTO personnel 
                (military_number, full_name, national_id, phone, birth_date, recruitment_date,
                 address, notes, is_active)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, 1)
            """
            
            params = (
                data['military_number'], data['full_name'], data['national_id'],
                data['phone'], data['birth_date'], data['recruitment_date'],
                data['address'], data['notes']
            )
            
            # تنفيذ الاستعلام
            self.db_manager.execute_query(query, params)
            messagebox.showinfo("نجح", "تم حفظ بيانات المستخدم بنجاح")
            
            # إعادة تحميل البيانات
            self.load_data()
            self.clear_form()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في حفظ البيانات: {e}")
    
    def edit_record(self):
        """تعديل السجل"""
        selection = self.tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى تحديد مستخدم للتعديل")
            return
        
        messagebox.showinfo("تعديل", "تم تفعيل وضع التعديل")
    
    def delete_record(self):
        """حذف السجل"""
        selection = self.tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى تحديد مستخدم للحذف")
            return
        
        if messagebox.askyesno("تأكيد", "هل أنت متأكد من حذف هذا المستخدم؟"):
            self.tree.delete(selection[0])
            messagebox.showinfo("حذف", "تم حذف المستخدم بنجاح")
    
    def cancel_operation(self):
        """إلغاء العملية"""
        self.clear_form()
        messagebox.showinfo("إلغاء", "تم إلغاء العملية")
    
    def print_record(self):
        """طباعة السجل"""
        messagebox.showinfo("طباعة", "سيتم تطوير وظيفة الطباعة قريباً")
    
    def export_data(self):
        """تصدير البيانات"""
        messagebox.showinfo("تصدير", "سيتم تطوير وظيفة التصدير قريباً")
    
    def clear_form(self):
        """مسح النموذج"""
        for var in self.form_vars.values():
            var.set("")
